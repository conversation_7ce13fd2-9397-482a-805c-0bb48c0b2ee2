#!/usr/bin/env python3
"""
Complete DMC Flow Test
测试DMC完整流程的token使用情况
包括：Draft Generation -> Annotation -> Merge -> Leader Evaluation
"""

import asyncio
import json
import time
import sys
import os
from typing import List, Dict, Any
import statistics
from pathlib import Path

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.api import async_generate_completion
from utils.token_utils import TokenCounter

class CompleteDMCFlowTest:
    """完整DMC流程测试"""
    
    def __init__(self):
        self.agents = ["openai", "gemini", "llama"]
        self.token_counter = TokenCounter()
    
    async def test_complete_dmc_flow(self, question: str, problem_type: str = "general"):
        """测试完整的DMC流程"""
        
        print(f"🚀 Testing Complete DMC Flow")
        print(f"Question: {question[:100]}...")
        print("=" * 60)
        
        total_tokens = 0
        start_time = time.time()
        flow_details = {
            'phases': [],
            'total_tokens': 0,
            'total_time': 0,
            'question': question,
            'problem_type': problem_type
        }
        
        # Phase 1: Parallel Draft Generation
        print("📝 Phase 1: Parallel Draft Generation")
        phase1_start = time.time()
        phase1_tokens = 0
        
        draft_tasks = []
        for agent in self.agents:
            system_prompt = f"You are a {agent} agent. Generate a clear, concise solution draft for this {problem_type} problem."
            
            task = async_generate_completion(
                agent_id=agent,
                prompt=question,
                system_prompt=system_prompt,
                temperature=0.3
            )
            draft_tasks.append((agent, task))
        
        draft_responses = {}
        for agent, task in draft_tasks:
            try:
                response = await task
                draft_responses[agent] = response
                
                # 计算tokens
                input_tokens = self.token_counter.count_tokens(question + system_prompt)
                output_tokens = self.token_counter.count_tokens(response)
                agent_tokens = input_tokens + output_tokens
                
                phase1_tokens += agent_tokens
                print(f"  {agent}: {agent_tokens} tokens ({input_tokens} in + {output_tokens} out)")
                
            except Exception as e:
                print(f"  {agent}: Failed - {e}")
                continue
        
        if not draft_responses:
            print("❌ No drafts generated")
            return None
        
        # 选择最佳draft
        best_draft = max(draft_responses.values(), key=len)
        best_agent = [k for k, v in draft_responses.items() if v == best_draft][0]
        
        phase1_time = time.time() - phase1_start
        total_tokens += phase1_tokens
        
        print(f"  ✅ Phase 1 Complete: {phase1_tokens} tokens, {phase1_time:.2f}s")
        print(f"  📋 Best draft from {best_agent} ({len(best_draft)} chars)")
        
        flow_details['phases'].append({
            'phase': 'draft_generation',
            'tokens': phase1_tokens,
            'time': phase1_time,
            'agents_used': len(draft_responses),
            'best_agent': best_agent
        })
        
        # Phase 2: Parallel Annotation
        print("\n🔍 Phase 2: Parallel Annotation")
        phase2_start = time.time()
        phase2_tokens = 0
        
        annotation_tasks = []
        for agent in self.agents:
            if agent in draft_responses:  # 只让成功生成draft的agent参与annotation
                annotation_prompt = f"""Current solution draft:
{best_draft}

Provide structured feedback to improve this solution. Focus on:
1. Correctness and accuracy
2. Completeness of the solution
3. Clarity and explanation quality

Your annotation (max 150 tokens):"""
                
                system_prompt = f"You are a {agent} agent providing constructive feedback. Be concise and specific."
                
                task = async_generate_completion(
                    agent_id=agent,
                    prompt=annotation_prompt,
                    system_prompt=system_prompt,
                    temperature=0.3
                )
                annotation_tasks.append((agent, task, annotation_prompt, system_prompt))
        
        annotations = {}
        for agent, task, prompt, sys_prompt in annotation_tasks:
            try:
                response = await task
                
                # 强制150 token限制
                response_words = response.split()
                if len(response_words) > 150:
                    response = " ".join(response_words[:150]) + "..."
                
                annotations[agent] = response
                
                # 计算tokens
                input_tokens = self.token_counter.count_tokens(prompt + sys_prompt)
                output_tokens = self.token_counter.count_tokens(response)
                agent_tokens = input_tokens + output_tokens
                
                phase2_tokens += agent_tokens
                print(f"  {agent}: {agent_tokens} tokens ({input_tokens} in + {output_tokens} out)")
                print(f"    Annotation: {response[:100]}...")
                
            except Exception as e:
                print(f"  {agent}: Failed - {e}")
                continue
        
        phase2_time = time.time() - phase2_start
        total_tokens += phase2_tokens
        
        print(f"  ✅ Phase 2 Complete: {phase2_tokens} tokens, {phase2_time:.2f}s")
        
        flow_details['phases'].append({
            'phase': 'annotation',
            'tokens': phase2_tokens,
            'time': phase2_time,
            'annotations_count': len(annotations)
        })
        
        # Phase 3: Merge Process
        print("\n🔄 Phase 3: Merge Process")
        phase3_start = time.time()
        phase3_tokens = 0
        
        if annotations:
            # 构建merge prompt
            annotations_text = "\n\n".join([
                f"Agent {agent}: {annotation}" 
                for agent, annotation in annotations.items()
            ])
            
            merge_prompt = f"""Original draft:
{best_draft}

Agent annotations:
{annotations_text}

As the merge agent, integrate the valuable feedback from annotations to create an improved solution. Maintain the core solution while incorporating valid suggestions.

Improved solution:"""
            
            merge_system_prompt = "You are the merge agent. Intelligently combine the draft with annotations to create the best possible solution."
            
            try:
                merged_solution = await async_generate_completion(
                    agent_id="openai",  # 使用openai作为merge agent
                    prompt=merge_prompt,
                    system_prompt=merge_system_prompt,
                    temperature=0.3
                )
                
                # 计算tokens
                input_tokens = self.token_counter.count_tokens(merge_prompt + merge_system_prompt)
                output_tokens = self.token_counter.count_tokens(merged_solution)
                phase3_tokens = input_tokens + output_tokens
                
                total_tokens += phase3_tokens
                
                print(f"  ✅ Merge Complete: {phase3_tokens} tokens ({input_tokens} in + {output_tokens} out)")
                print(f"  📋 Merged solution: {merged_solution[:150]}...")
                
            except Exception as e:
                print(f"  ❌ Merge Failed: {e}")
                merged_solution = best_draft
                
        else:
            merged_solution = best_draft
            print("  ⚠️  No annotations available, using original draft")
        
        phase3_time = time.time() - phase3_start
        
        flow_details['phases'].append({
            'phase': 'merge',
            'tokens': phase3_tokens,
            'time': phase3_time,
            'merge_successful': phase3_tokens > 0
        })
        
        # Phase 4: Leader Evaluation
        print("\n👑 Phase 4: Leader Evaluation")
        phase4_start = time.time()
        phase4_tokens = 0
        
        evaluation_prompt = f"""Evaluate this solution for a {problem_type} problem:

Problem: {question}

Solution: {merged_solution}

Provide:
1. Quality score (0.0-1.0)
2. Brief assessment of correctness and completeness
3. Final recommendation (accept/revise)

Evaluation:"""
        
        evaluation_system_prompt = "You are the leader agent. Provide objective evaluation of solution quality."
        
        try:
            evaluation = await async_generate_completion(
                agent_id="gemini",  # 使用gemini作为leader
                prompt=evaluation_prompt,
                system_prompt=evaluation_system_prompt,
                temperature=0.3
            )
            
            # 计算tokens
            input_tokens = self.token_counter.count_tokens(evaluation_prompt + evaluation_system_prompt)
            output_tokens = self.token_counter.count_tokens(evaluation)
            phase4_tokens = input_tokens + output_tokens
            
            total_tokens += phase4_tokens
            
            print(f"  ✅ Evaluation Complete: {phase4_tokens} tokens ({input_tokens} in + {output_tokens} out)")
            print(f"  📊 Evaluation: {evaluation[:200]}...")
            
        except Exception as e:
            print(f"  ❌ Evaluation Failed: {e}")
            evaluation = "Evaluation failed"
        
        phase4_time = time.time() - phase4_start
        total_time = time.time() - start_time
        
        flow_details['phases'].append({
            'phase': 'leader_evaluation',
            'tokens': phase4_tokens,
            'time': phase4_time,
            'evaluation_successful': phase4_tokens > 0
        })
        
        # 总结
        flow_details.update({
            'total_tokens': total_tokens,
            'total_time': total_time,
            'final_solution': merged_solution,
            'final_evaluation': evaluation
        })
        
        print(f"\n🎯 COMPLETE DMC FLOW SUMMARY")
        print("=" * 60)
        print(f"Total Tokens: {total_tokens}")
        print(f"Total Time: {total_time:.2f}s")
        print(f"Tokens per Second: {total_tokens/total_time:.1f}")
        
        print(f"\nPhase Breakdown:")
        for phase in flow_details['phases']:
            print(f"  {phase['phase']}: {phase['tokens']} tokens ({phase['tokens']/total_tokens*100:.1f}%)")
        
        return flow_details

async def run_complete_dmc_tests():
    """运行完整的DMC流程测试"""
    
    test_problems = [
        {
            'question': "Solve the quadratic equation: 2x² - 8x + 6 = 0. Show all steps and verify your answer.",
            'type': 'math'
        },
        {
            'question': "Write a Python function that implements binary search on a sorted array. Include error handling and documentation.",
            'type': 'coding'
        },
        {
            'question': "Explain the causes and consequences of climate change, focusing on the greenhouse effect and its impact on global weather patterns.",
            'type': 'science'
        }
    ]
    
    tester = CompleteDMCFlowTest()
    all_results = []
    
    print("🚀 COMPLETE DMC FLOW TESTING")
    print("=" * 80)
    
    for i, problem in enumerate(test_problems):
        print(f"\n🧪 Test {i+1}/{len(test_problems)}")
        print("-" * 40)
        
        result = await tester.test_complete_dmc_flow(
            problem['question'], 
            problem['type']
        )
        
        if result:
            all_results.append(result)
    
    # 分析所有结果
    if all_results:
        print(f"\n📊 OVERALL ANALYSIS")
        print("=" * 80)
        
        avg_total_tokens = statistics.mean([r['total_tokens'] for r in all_results])
        avg_total_time = statistics.mean([r['total_time'] for r in all_results])
        
        print(f"Average Total Tokens: {avg_total_tokens:.0f}")
        print(f"Average Total Time: {avg_total_time:.2f}s")
        print(f"Average Efficiency: {avg_total_tokens/avg_total_time:.1f} tokens/second")
        
        # 各阶段平均分析
        phase_stats = {}
        for result in all_results:
            for phase in result['phases']:
                phase_name = phase['phase']
                if phase_name not in phase_stats:
                    phase_stats[phase_name] = {'tokens': [], 'time': []}
                phase_stats[phase_name]['tokens'].append(phase['tokens'])
                phase_stats[phase_name]['time'].append(phase['time'])
        
        print(f"\nPhase Analysis:")
        for phase_name, stats in phase_stats.items():
            avg_tokens = statistics.mean(stats['tokens'])
            avg_time = statistics.mean(stats['time'])
            print(f"  {phase_name}: {avg_tokens:.0f} tokens, {avg_time:.2f}s average")
        
        # 保存结果
        output_file = Path("results/complete_dmc_flow_test.json")
        output_file.parent.mkdir(exist_ok=True)
        
        summary_results = {
            'timestamp': time.time(),
            'summary': {
                'avg_total_tokens': avg_total_tokens,
                'avg_total_time': avg_total_time,
                'avg_efficiency': avg_total_tokens/avg_total_time,
                'phase_stats': {
                    phase: {
                        'avg_tokens': statistics.mean(stats['tokens']),
                        'avg_time': statistics.mean(stats['time'])
                    }
                    for phase, stats in phase_stats.items()
                }
            },
            'detailed_results': all_results
        }
        
        with open(output_file, 'w') as f:
            json.dump(summary_results, f, indent=2)
        
        print(f"\n💾 Results saved to: {output_file}")
        
        return summary_results
    
    else:
        print("❌ No successful tests completed")
        return None

if __name__ == "__main__":
    asyncio.run(run_complete_dmc_tests())
