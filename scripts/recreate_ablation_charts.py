#!/usr/bin/env python3
"""
重新创建消融实验图表 - 基于原有风格但替换散点图
保持与LaTeX文件的一致性
"""

import pandas as pd
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from pathlib import Path

# 使用原有的双栏论文优化设置
plt.rcParams.update({
    'font.size': 20,           # 大字体
    'axes.titlesize': 22,      # 标题更大
    'axes.labelsize': 20,      # 轴标签
    'xtick.labelsize': 18,     # x轴刻度
    'ytick.labelsize': 18,     # y轴刻度
    'legend.fontsize': 18,     # 图例
    'lines.linewidth': 4,      # 粗线条
    'lines.markersize': 10,    # 大标记
    'axes.linewidth': 2,       # 坐标轴线条
    'grid.linewidth': 1.5,     # 网格线
    'font.weight': 'bold',     # 粗体字
})

def create_simple_ablation_data():
    """创建消融实验数据 - 与原代码保持一致"""
    return pd.DataFrame({
        'Method': ['Single', 'No Merge', 'No Annotation', 'No Compressor', 'Full DMC'],
        'Tokens': [1200, 2800, 3200, 4100, 2900],
        'Time': [8.5, 12.3, 15.8, 18.2, 14.1]
    })

def create_ablation_combined_large():
    """重新创建 ablation_combined_large.png - 保持原有风格"""
    
    df = create_simple_ablation_data()
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)
    
    # 使用原有的鲜明颜色
    colors = ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#592E83']
    
    # 组合图 - 与原代码完全一致
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # Token图
    bars1 = ax1.bar(df['Method'], df['Tokens'], color=colors, alpha=0.8,
                    edgecolor='black', linewidth=2)
    
    for bar, value in zip(bars1, df['Tokens']):
        ax1.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 50,
                f'{int(value)}', ha='center', va='bottom', 
                fontsize=16, fontweight='bold')
    
    ax1.set_title('Token Usage', fontweight='bold', pad=15)
    ax1.set_ylabel('Avg Tokens', fontweight='bold')
    ax1.set_xlabel('Method', fontweight='bold')
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(0, max(df['Tokens']) * 1.15)
    
    # 时间图
    bars2 = ax2.bar(df['Method'], df['Time'], color=colors, alpha=0.8,
                    edgecolor='black', linewidth=2)
    
    for bar, value in zip(bars2, df['Time']):
        ax2.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.5,
                f'{value:.1f}s', ha='center', va='bottom', 
                fontsize=16, fontweight='bold')
    
    ax2.set_title('Processing Time', fontweight='bold', pad=15)
    ax2.set_ylabel('Avg Duration (s)', fontweight='bold')
    ax2.set_xlabel('Method', fontweight='bold')
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, max(df['Time']) * 1.15)
    
    plt.tight_layout()
    plt.savefig(results_dir / 'ablation_combined_large.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return df

def create_ablation_efficiency_chart():
    """创建效率分析图 - 替换散点图为更直观的图表"""
    
    df = create_simple_ablation_data()
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)
    
    # 计算相对比率
    baseline_tokens = df[df['Method'] == 'Single']['Tokens'].iloc[0]
    baseline_time = df[df['Method'] == 'Single']['Time'].iloc[0]
    
    token_ratios = df['Tokens'] / baseline_tokens
    time_ratios = df['Time'] / baseline_time
    
    # 创建效率比率图 - 替代散点图
    fig, ax = plt.subplots(1, 1, figsize=(12, 10))
    
    # 使用原有配色
    colors = ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#592E83']
    
    x = np.arange(len(df))
    width = 0.35
    
    # 创建双轴柱状图
    bars1 = ax.bar(x - width/2, token_ratios, width, 
                   label='Token Ratio vs Single Agent', color=colors, alpha=0.8,
                   edgecolor='black', linewidth=2)
    
    ax2 = ax.twinx()
    bars2 = ax2.bar(x + width/2, time_ratios, width,
                    label='Time Ratio vs Single Agent', 
                    color=[c for c in colors], alpha=0.6,
                    edgecolor='black', linewidth=2, hatch='///')
    
    # 添加数值标签
    for i, (bar1, bar2) in enumerate(zip(bars1, bars2)):
        ax.text(bar1.get_x() + bar1.get_width()/2., bar1.get_height() + 0.1,
                f'{token_ratios.iloc[i]:.1f}x', ha='center', va='bottom', 
                fontsize=14, fontweight='bold')
        ax2.text(bar2.get_x() + bar2.get_width()/2., bar2.get_height() + 0.1,
                f'{time_ratios.iloc[i]:.1f}x', ha='center', va='bottom', 
                fontsize=14, fontweight='bold')
    
    # 添加基线参考线
    ax.axhline(y=1.0, color='red', linestyle='--', linewidth=3, alpha=0.7)
    ax2.axhline(y=1.0, color='red', linestyle='--', linewidth=3, alpha=0.7)
    
    ax.set_xlabel('DMC Configuration', fontweight='bold')
    ax.set_ylabel('Token Ratio vs Single Agent', fontweight='bold', color='#2E86AB')
    ax2.set_ylabel('Time Ratio vs Single Agent', fontweight='bold', color='#A23B72')
    ax.set_title('Efficiency Trade-off Analysis', fontweight='bold', pad=20)
    
    ax.set_xticks(x)
    ax.set_xticklabels(df['Method'], rotation=45, ha='right')
    ax.grid(True, alpha=0.3, linewidth=1.5)
    
    # 设置坐标轴范围
    ax.set_ylim(0, max(token_ratios) * 1.2)
    ax2.set_ylim(0, max(time_ratios) * 1.2)
    
    # 添加图例
    lines1, labels1 = ax.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax.legend(lines1 + lines2, labels1 + labels2, loc='upper left', fontsize=16)
    
    plt.tight_layout()
    plt.savefig(results_dir / 'ablation_scatter_large.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_all_ablation_charts():
    """创建所有消融实验图表"""
    print("📊 Recreating Ablation Charts with Original Style")
    print("=" * 60)
    
    # 创建组合图
    df = create_ablation_combined_large()
    print("✅ ablation_combined_large.png created")
    
    # 创建效率分析图（替代散点图）
    create_ablation_efficiency_chart()
    print("✅ ablation_scatter_large.png created (efficiency ratio chart)")
    
    print("\n📁 Generated files:")
    print("  - ablation_combined_large.png (Token + Time comparison)")
    print("  - ablation_scatter_large.png (Efficiency ratio analysis)")
    
    print("\n🎨 Style features:")
    print("  ✓ Original bold fonts (18-22pt)")
    print("  ✓ Original color scheme")
    print("  ✓ Thick lines and borders")
    print("  ✓ High contrast design")
    print("  ✓ Replaced scatter plot with ratio chart")
    
    print("\n📊 Data summary:")
    print(df)
    
    return df

if __name__ == "__main__":
    create_all_ablation_charts()
