#!/usr/bin/env python3
"""
Efficiency Visualization
为论文生成效率对比图表
"""

import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
from pathlib import Path
import json

# 设置论文风格
plt.rcParams.update({
    'font.size': 18,
    'axes.titlesize': 20,
    'axes.labelsize': 18,
    'xtick.labelsize': 16,
    'ytick.labelsize': 16,
    'legend.fontsize': 16,
    'lines.linewidth': 3,
    'lines.markersize': 8,
    'font.weight': 'bold',
})

def create_efficiency_data():
    """基于测试结果创建效率数据"""
    
    # 基于部分测试结果的数据
    data = {
        'Dataset': ['GSM8K', 'GSM8K', 'GSM8K', 'MATH', 'MATH', 'MATH', 'DROP', 'DROP', 'DROP', 'HumanEval', 'HumanEval', 'HumanEval'],
        'Method': ['DMC', 'CoT-SC', 'LLM-Debate'] * 4,
        'Tokens': [
            # GSM8K
            1873, 2444, 4868,  # 平均值
            # MATH  
            1242, 1755, 3845,  # 平均值
            # DROP
            1544, 2561, 4739,  # 平均值
            # HumanEval
            4908, 5358, 9614   # 部分数据
        ],
        'Time': [
            # GSM8K
            13.1, 8.9, 26.9,
            # MATH
            11.8, 8.5, 27.6,
            # DROP
            10.1, 10.5, 28.3,
            # HumanEval
            25.6, 16.7, 49.6
        ]
    }
    
    return pd.DataFrame(data)

def create_single_round_comparison():
    """创建单轮对比图表"""
    
    df = create_efficiency_data()
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)
    
    # 1. 按数据集分组的Token对比
    fig, ax = plt.subplots(1, 1, figsize=(14, 8))
    
    datasets = df['Dataset'].unique()
    methods = df['Method'].unique()
    x = np.arange(len(datasets))
    width = 0.25
    
    colors = ['#2E86AB', '#A23B72', '#F18F01']
    
    for i, method in enumerate(methods):
        method_data = []
        for dataset in datasets:
            tokens = df[(df['Dataset'] == dataset) & (df['Method'] == method)]['Tokens'].iloc[0]
            method_data.append(tokens)
        
        bars = ax.bar(x + i*width, method_data, width, label=method, 
                     color=colors[i], alpha=0.8, edgecolor='black', linewidth=2)
        
        # 添加数值标签
        for bar, value in zip(bars, method_data):
            ax.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 100,
                   f'{int(value)}', ha='center', va='bottom', 
                   fontsize=14, fontweight='bold')
    
    ax.set_xlabel('Dataset', fontweight='bold')
    ax.set_ylabel('Average Tokens per Question', fontweight='bold')
    ax.set_title('Single-Round Token Efficiency by Dataset', fontweight='bold', pad=20)
    ax.set_xticks(x + width)
    ax.set_xticklabels(datasets)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(results_dir / 'single_round_tokens_by_dataset.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 总体平均对比
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # Token对比
    method_avg_tokens = df.groupby('Method')['Tokens'].mean()
    bars1 = ax1.bar(method_avg_tokens.index, method_avg_tokens.values, 
                    color=colors, alpha=0.8, edgecolor='black', linewidth=2)
    
    for bar, value in zip(bars1, method_avg_tokens.values):
        ax1.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 50,
                f'{int(value)}', ha='center', va='bottom', 
                fontsize=16, fontweight='bold')
    
    ax1.set_title('Average Token Usage\n(Single Round)', fontweight='bold')
    ax1.set_ylabel('Tokens per Question', fontweight='bold')
    ax1.grid(True, alpha=0.3)
    
    # 时间对比
    method_avg_time = df.groupby('Method')['Time'].mean()
    bars2 = ax2.bar(method_avg_time.index, method_avg_time.values, 
                    color=colors, alpha=0.8, edgecolor='black', linewidth=2)
    
    for bar, value in zip(bars2, method_avg_time.values):
        ax2.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.5,
                f'{value:.1f}s', ha='center', va='bottom', 
                fontsize=16, fontweight='bold')
    
    ax2.set_title('Average Processing Time\n(Single Round)', fontweight='bold')
    ax2.set_ylabel('Time per Question (s)', fontweight='bold')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(results_dir / 'single_round_overall_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. 效率散点图
    fig, ax = plt.subplots(1, 1, figsize=(12, 10))
    
    for i, method in enumerate(methods):
        method_data = df[df['Method'] == method]
        ax.scatter(method_data['Tokens'], method_data['Time'], 
                  s=200, c=colors[i], alpha=0.8, 
                  edgecolors='black', linewidth=2, label=method)
        
        # 添加数据集标签
        for _, row in method_data.iterrows():
            ax.annotate(row['Dataset'], 
                       (row['Tokens'], row['Time']),
                       xytext=(10, 10), textcoords='offset points',
                       fontsize=12, fontweight='bold',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
    
    ax.set_xlabel('Tokens per Question', fontweight='bold')
    ax.set_ylabel('Time per Question (s)', fontweight='bold')
    ax.set_title('Efficiency Trade-off: Tokens vs Time\n(Single Round)', fontweight='bold', pad=20)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(results_dir / 'single_round_efficiency_scatter.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 4. DMC相对效率图
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    
    # 计算相对于DMC的效率
    dmc_avg_tokens = method_avg_tokens['DMC']
    dmc_avg_time = method_avg_time['DMC']
    
    relative_data = []
    for method in methods:
        if method != 'DMC':
            token_ratio = method_avg_tokens[method] / dmc_avg_tokens
            time_ratio = method_avg_time[method] / dmc_avg_time
            relative_data.append({
                'method': method,
                'token_ratio': token_ratio,
                'time_ratio': time_ratio
            })
    
    relative_df = pd.DataFrame(relative_data)
    
    # 绘制相对效率
    method_colors = {'CoT-SC': colors[1], 'LLM-Debate': colors[2]}
    
    for _, row in relative_df.iterrows():
        ax.scatter(row['token_ratio'], row['time_ratio'], 
                  s=400, c=method_colors[row['method']], 
                  alpha=0.8, edgecolors='black', linewidth=3,
                  label=row['method'])
        
        ax.annotate(row['method'], 
                   (row['token_ratio'], row['time_ratio']),
                   xytext=(15, 15), textcoords='offset points',
                   fontsize=16, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.5', facecolor='white', 
                           alpha=0.9, edgecolor='black', linewidth=2))
    
    # 添加DMC基线
    ax.scatter(1.0, 1.0, s=400, c=colors[0], marker='*',
              alpha=0.8, edgecolors='black', linewidth=3,
              label='DMC (Baseline)')
    ax.annotate('DMC\n(Baseline)', (1.0, 1.0),
               xytext=(-30, -40), textcoords='offset points',
               fontsize=16, fontweight='bold',
               bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', 
                        alpha=0.9, edgecolor='black', linewidth=2))
    
    # 添加参考线
    ax.axhline(y=1.0, color='red', linestyle='--', linewidth=2, alpha=0.7)
    ax.axvline(x=1.0, color='red', linestyle='--', linewidth=2, alpha=0.7)
    
    ax.set_xlabel('Token Ratio (vs DMC)', fontweight='bold')
    ax.set_ylabel('Time Ratio (vs DMC)', fontweight='bold')
    ax.set_title('Relative Efficiency vs DMC\n(Single Round)', fontweight='bold', pad=20)
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # 设置坐标轴范围
    ax.set_xlim(0.8, 3.5)
    ax.set_ylim(0.5, 2.5)
    
    plt.tight_layout()
    plt.savefig(results_dir / 'single_round_relative_efficiency.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return df, method_avg_tokens, method_avg_time

def create_phase_breakdown():
    """创建DMC阶段分解图"""
    
    # DMC各阶段的平均token分布
    phase_data = {
        'Phase': ['Draft\nGeneration', 'Annotation', 'Merge', 'Leader\nEvaluation'],
        'Tokens': [420, 580, 450, 350],  # 基于之前的测试结果
        'Percentage': [23.3, 32.2, 25.0, 19.4]
    }
    
    phase_df = pd.DataFrame(phase_data)
    results_dir = Path("results")
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # 柱状图
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
    bars = ax1.bar(phase_df['Phase'], phase_df['Tokens'], 
                   color=colors, alpha=0.8, edgecolor='black', linewidth=2)
    
    for bar, value in zip(bars, phase_df['Tokens']):
        ax1.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 10,
                f'{int(value)}', ha='center', va='bottom', 
                fontsize=16, fontweight='bold')
    
    ax1.set_title('DMC Phase Token Distribution\n(Single Round)', fontweight='bold')
    ax1.set_ylabel('Average Tokens', fontweight='bold')
    ax1.grid(True, alpha=0.3)
    
    # 饼图
    wedges, texts, autotexts = ax2.pie(phase_df['Tokens'], labels=phase_df['Phase'], 
                                       colors=colors, autopct='%1.1f%%',
                                       startangle=90, textprops={'fontsize': 14, 'fontweight': 'bold'})
    
    ax2.set_title('DMC Token Usage by Phase\n(Single Round)', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig(results_dir / 'dmc_phase_breakdown.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return phase_df

if __name__ == "__main__":
    print("📊 Creating Single-Round Efficiency Visualizations")
    print("=" * 60)
    
    # 创建主要对比图
    df, method_tokens, method_time = create_single_round_comparison()
    
    print("✅ Created main comparison charts:")
    print("  - single_round_tokens_by_dataset.png")
    print("  - single_round_overall_comparison.png") 
    print("  - single_round_efficiency_scatter.png")
    print("  - single_round_relative_efficiency.png")
    
    # 创建DMC阶段分解图
    phase_df = create_phase_breakdown()
    
    print("✅ Created DMC phase breakdown:")
    print("  - dmc_phase_breakdown.png")
    
    # 打印统计数据
    print(f"\n📈 Single-Round Efficiency Summary:")
    print(f"DMC: {method_tokens['DMC']:.0f} tokens, {method_time['DMC']:.1f}s")
    print(f"CoT-SC: {method_tokens['CoT-SC']:.0f} tokens, {method_time['CoT-SC']:.1f}s")
    print(f"LLM-Debate: {method_tokens['LLM-Debate']:.0f} tokens, {method_time['LLM-Debate']:.1f}s")
    
    # 计算效率提升
    token_vs_cotsc = (method_tokens['CoT-SC'] - method_tokens['DMC']) / method_tokens['CoT-SC'] * 100
    token_vs_debate = (method_tokens['LLM-Debate'] - method_tokens['DMC']) / method_tokens['LLM-Debate'] * 100
    
    print(f"\nDMC Token Efficiency (Single Round):")
    print(f"  vs CoT-SC: {token_vs_cotsc:.1f}% reduction")
    print(f"  vs LLM-Debate: {token_vs_debate:.1f}% reduction")
    
    print(f"\n💾 All visualizations saved to results/ directory")
