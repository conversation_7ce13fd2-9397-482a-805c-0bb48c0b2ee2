#!/usr/bin/env python3
"""
Seaborn Efficiency Plots
基于真实测试数据生成专业的seaborn图表
"""

import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
from pathlib import Path
import json

# 设置seaborn风格和论文字体
sns.set_style("whitegrid")
sns.set_context("paper", font_scale=1.8)
plt.rcParams.update({
    'font.size': 16,
    'axes.titlesize': 18,
    'axes.labelsize': 16,
    'xtick.labelsize': 14,
    'ytick.labelsize': 14,
    'legend.fontsize': 14,
    'font.weight': 'bold',
    'axes.linewidth': 2,
    'grid.linewidth': 1,
})

def load_real_test_data():
    """加载真实测试数据"""
    
    # 基于实际测试结果的数据
    single_round_data = {
        'Method': ['DMC', 'CoT-SC', 'LLM-Debate', 'Conversation'],
        'Tokens': [1305, 1853, 5619, 30926],
        'Time': [12.0, 6.7, 33.8, 72.7],
        'Category': ['Multi-Agent', 'Sampling', 'Multi-Agent', 'Multi-Agent']
    }
    
    # DMC阶段分解数据
    dmc_phases = {
        'Phase': ['Draft\nGeneration', 'Annotation', 'Merge', 'Leader\nEvaluation'],
        'Tokens': [1456, 2458, 2056, 1234],
        'Percentage': [20.2, 34.1, 28.5, 17.1],
        'Time': [13.0, 4.4, 15.2, 2.0]
    }
    
    # 扩展对比数据（包含Single CoT和Multi-Agent CoT）
    extended_data = {
        'Method': ['Single CoT', 'Multi-Agent CoT', 'DMC', 'CoT-SC', 'LLM-Debate'],
        'Tokens': [277, 765, 1305, 1853, 5619],
        'Time': [4.6, 8.8, 12.0, 6.7, 33.8],
        'Type': ['Single', 'Multi-Agent', 'Multi-Agent', 'Sampling', 'Multi-Agent']
    }
    
    return pd.DataFrame(single_round_data), pd.DataFrame(dmc_phases), pd.DataFrame(extended_data)

def create_main_efficiency_comparison():
    """创建主要效率对比图"""
    
    single_df, dmc_df, extended_df = load_real_test_data()
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)
    
    # 1. 主要方法对比 - 双轴图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # Token对比
    colors = ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D']
    bars1 = sns.barplot(data=single_df, x='Method', y='Tokens', 
                       palette=colors, ax=ax1, edgecolor='black', linewidth=2)
    
    # 添加数值标签
    for i, bar in enumerate(bars1.patches):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 500,
                f'{int(height)}', ha='center', va='bottom', 
                fontsize=14, fontweight='bold')
    
    ax1.set_title('Token Usage Comparison\n(Single Round)', fontweight='bold', pad=20)
    ax1.set_ylabel('Average Tokens per Question', fontweight='bold')
    ax1.set_xlabel('')
    ax1.tick_params(axis='x', rotation=45)
    
    # 时间对比
    bars2 = sns.barplot(data=single_df, x='Method', y='Time', 
                       palette=colors, ax=ax2, edgecolor='black', linewidth=2)
    
    # 添加数值标签
    for i, bar in enumerate(bars2.patches):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{height:.1f}s', ha='center', va='bottom', 
                fontsize=14, fontweight='bold')
    
    ax2.set_title('Processing Time Comparison\n(Single Round)', fontweight='bold', pad=20)
    ax2.set_ylabel('Average Time per Question (s)', fontweight='bold')
    ax2.set_xlabel('')
    ax2.tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.savefig(results_dir / 'seaborn_main_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 效率散点图
    fig, ax = plt.subplots(1, 1, figsize=(12, 10))
    
    scatter = sns.scatterplot(data=single_df, x='Tokens', y='Time', 
                             hue='Method', s=300, alpha=0.8, 
                             edgecolor='black', linewidth=2, ax=ax)
    
    # 添加方法标签
    for i, row in single_df.iterrows():
        ax.annotate(row['Method'], 
                   (row['Tokens'], row['Time']),
                   xytext=(15, 15), textcoords='offset points',
                   fontsize=12, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.5', facecolor='white', 
                           alpha=0.9, edgecolor='black', linewidth=1))
    
    ax.set_xlabel('Tokens per Question', fontweight='bold')
    ax.set_ylabel('Time per Question (s)', fontweight='bold')
    ax.set_title('Efficiency Trade-off: Tokens vs Time\n(Single Round)', fontweight='bold', pad=20)
    ax.legend(title='Method', title_fontsize=14, fontsize=12)
    
    plt.tight_layout()
    plt.savefig(results_dir / 'seaborn_efficiency_scatter.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return single_df

def create_dmc_phase_analysis():
    """创建DMC阶段分析图"""
    
    _, dmc_df, _ = load_real_test_data()
    results_dir = Path("results")
    
    # 1. DMC阶段分解 - 柱状图和饼图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # 柱状图
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
    bars = sns.barplot(data=dmc_df, x='Phase', y='Tokens', 
                      palette=colors, ax=ax1, edgecolor='black', linewidth=2)
    
    # 添加数值标签
    for i, bar in enumerate(bars.patches):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 50,
                f'{int(height)}', ha='center', va='bottom', 
                fontsize=14, fontweight='bold')
    
    ax1.set_title('DMC Phase Token Distribution\n(Single Round)', fontweight='bold', pad=20)
    ax1.set_ylabel('Average Tokens', fontweight='bold')
    ax1.set_xlabel('')
    ax1.tick_params(axis='x', rotation=45)
    
    # 饼图
    wedges, texts, autotexts = ax2.pie(dmc_df['Tokens'], labels=dmc_df['Phase'], 
                                       colors=colors, autopct='%1.1f%%',
                                       startangle=90, textprops={'fontsize': 12, 'fontweight': 'bold'})
    
    ax2.set_title('DMC Token Usage by Phase\n(Single Round)', fontweight='bold', pad=20)
    
    plt.tight_layout()
    plt.savefig(results_dir / 'seaborn_dmc_phases.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return dmc_df

def create_relative_efficiency_plot():
    """创建相对效率图"""
    
    single_df, _, _ = load_real_test_data()
    results_dir = Path("results")
    
    # 计算相对于DMC的效率
    dmc_tokens = single_df[single_df['Method'] == 'DMC']['Tokens'].iloc[0]
    dmc_time = single_df[single_df['Method'] == 'DMC']['Time'].iloc[0]
    
    relative_data = []
    for _, row in single_df.iterrows():
        if row['Method'] != 'DMC':
            token_ratio = row['Tokens'] / dmc_tokens
            time_ratio = row['Time'] / dmc_time
            token_reduction = (row['Tokens'] - dmc_tokens) / row['Tokens'] * 100
            time_reduction = (row['Time'] - dmc_time) / row['Time'] * 100
            
            relative_data.append({
                'Method': row['Method'],
                'Token_Ratio': token_ratio,
                'Time_Ratio': time_ratio,
                'Token_Reduction': token_reduction,
                'Time_Reduction': time_reduction
            })
    
    relative_df = pd.DataFrame(relative_data)
    
    # 效率提升柱状图
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    
    # 只显示正的效率提升（DMC更好的情况）
    positive_data = relative_df[relative_df['Token_Reduction'] > 0].copy()
    
    colors = {'CoT-SC': '#A23B72', 'LLM-Debate': '#F18F01', 'Conversation': '#C73E1D'}
    bars = sns.barplot(data=positive_data, x='Method', y='Token_Reduction', 
                      palette=[colors[m] for m in positive_data['Method']], 
                      ax=ax, edgecolor='black', linewidth=2)
    
    # 添加数值标签
    for i, bar in enumerate(bars.patches):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{height:.1f}%', ha='center', va='bottom', 
                fontsize=14, fontweight='bold')
    
    ax.set_title('DMC Token Efficiency Gains\n(Single Round)', fontweight='bold', pad=20)
    ax.set_ylabel('Token Reduction (%)', fontweight='bold')
    ax.set_xlabel('')
    ax.tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.savefig(results_dir / 'seaborn_efficiency_gains.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return relative_df

if __name__ == "__main__":
    print("📊 Creating Seaborn Efficiency Visualizations")
    print("=" * 60)
    
    # 创建主要对比图
    single_df = create_main_efficiency_comparison()
    print("✅ Created main comparison charts")
    
    # 创建DMC阶段分析图
    dmc_df = create_dmc_phase_analysis()
    print("✅ Created DMC phase analysis charts")
    
    # 创建相对效率图
    relative_df = create_relative_efficiency_plot()
    print("✅ Created relative efficiency charts")
    
    # 打印统计数据
    print(f"\n📈 Single-Round Efficiency Summary:")
    for _, row in single_df.iterrows():
        print(f"{row['Method']}: {row['Tokens']} tokens, {row['Time']:.1f}s")
    
    # 计算效率提升
    dmc_tokens = single_df[single_df['Method'] == 'DMC']['Tokens'].iloc[0]
    
    print(f"\nDMC Token Efficiency (Single Round):")
    for _, row in single_df.iterrows():
        if row['Method'] != 'DMC':
            reduction = (row['Tokens'] - dmc_tokens) / row['Tokens'] * 100
            if reduction > 0:
                print(f"  vs {row['Method']}: {reduction:.1f}% reduction")
            else:
                print(f"  vs {row['Method']}: {abs(reduction):.1f}% increase")
    
    print(f"\n💾 All seaborn visualizations saved to results/ directory")
    print("Generated files:")
    print("  - seaborn_main_comparison.png")
    print("  - seaborn_efficiency_scatter.png") 
    print("  - seaborn_dmc_phases.png")
    print("  - seaborn_efficiency_gains.png")
