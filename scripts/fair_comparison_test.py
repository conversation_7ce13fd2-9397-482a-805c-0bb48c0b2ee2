#!/usr/bin/env python3
"""
Fair Comparison Test
使用相同问题对比DMC和LLM-Debate的token使用
"""

import asyncio
import json
import time
import sys
import os
from typing import List, Dict, Any
import statistics
from pathlib import Path

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.api import async_generate_completion
from utils.token_utils import TokenCounter

class FairComparisonTest:
    """公平对比测试"""
    
    def __init__(self):
        self.agents = ["openai", "gemini", "llama"]
        self.token_counter = TokenCounter()
    
    async def test_dmc_on_simple_problem(self, question: str, problem_type: str = "math"):
        """在简单问题上测试DMC"""
        
        print(f"🤝 Testing DMC on: {question}")
        total_tokens = 0
        start_time = time.time()
        
        # Phase 1: Draft Generation (简化版)
        draft_tasks = []
        for agent in self.agents:
            task = async_generate_completion(
                agent_id=agent,
                prompt=question,
                system_prompt=f"Solve this {problem_type} problem concisely.",
                temperature=0.3
            )
            draft_tasks.append(task)
        
        draft_responses = await asyncio.gather(*draft_tasks, return_exceptions=True)
        valid_drafts = [r for r in draft_responses if not isinstance(r, Exception)]
        
        if not valid_drafts:
            return None, 0, 0
        
        best_draft = max(valid_drafts, key=len)
        
        # 计算draft tokens
        for response in valid_drafts:
            draft_tokens = len(question.split()) * 1.3 + len(str(response).split()) * 1.3
            total_tokens += draft_tokens
        
        # Phase 2: Annotation (简化版)
        annotation_tasks = []
        for agent in self.agents:
            annotation_prompt = f"Solution: {best_draft}\n\nBrief feedback (max 100 tokens):"
            
            task = async_generate_completion(
                agent_id=agent,
                prompt=annotation_prompt,
                system_prompt="Provide brief, focused feedback.",
                temperature=0.3
            )
            annotation_tasks.append(task)
        
        annotations = await asyncio.gather(*annotation_tasks, return_exceptions=True)
        
        # 计算annotation tokens (bounded)
        for annotation in annotations:
            if not isinstance(annotation, Exception):
                bounded_annotation = " ".join(str(annotation).split()[:100])
                annotation_tokens = len(annotation_prompt.split()) * 1.3 + len(bounded_annotation.split()) * 1.3
                total_tokens += annotation_tokens
        
        duration = time.time() - start_time
        
        print(f"    ✅ DMC: {duration:.2f}s, {total_tokens:.0f} tokens")
        return best_draft, total_tokens, duration
    
    async def test_llm_debate_on_simple_problem(self, question: str, problem_type: str = "math"):
        """在简单问题上测试LLM-Debate"""
        
        print(f"🥊 Testing LLM-Debate on: {question}")
        total_tokens = 0
        start_time = time.time()
        
        # Phase 1: Independent answers
        initial_answers = {}
        
        for agent in self.agents:
            try:
                prompt = f"Solve this {problem_type} problem independently:\n\n{question}\n\nYour solution:"
                
                response = await async_generate_completion(
                    agent_id=agent,
                    prompt=prompt,
                    system_prompt=f"Solve {problem_type} problems independently.",
                    temperature=0.3
                )
                
                initial_answers[agent] = response
                total_tokens += len(prompt.split()) * 1.3 + len(response.split()) * 1.3
                
            except Exception as e:
                continue
        
        if len(initial_answers) < 2:
            return None, 0, 0
        
        # Phase 2: Debate phase
        debate_responses = {}
        
        for agent in self.agents:
            if agent not in initial_answers:
                continue
                
            try:
                # 构建其他agent的答案
                others_answers = []
                for other_agent, answer in initial_answers.items():
                    if other_agent != agent:
                        others_answers.append(f"{other_agent.upper()}: {answer}")
                
                others_text = "\n\n".join(others_answers)
                
                debate_prompt = f"""Problem: {question}

Your initial answer: {initial_answers[agent]}

Other answers:
{others_text}

Debate and provide your final answer:"""
                
                debate_response = await async_generate_completion(
                    agent_id=agent,
                    prompt=debate_prompt,
                    system_prompt="Engage in brief debate and provide final answer.",
                    temperature=0.3
                )
                
                debate_responses[agent] = debate_response
                total_tokens += len(debate_prompt.split()) * 1.3 + len(debate_response.split()) * 1.3
                
            except Exception as e:
                continue
        
        # Phase 3: Final decision
        if debate_responses:
            try:
                judge_agent = self.agents[0]
                
                all_debates = []
                for agent, response in debate_responses.items():
                    all_debates.append(f"{agent.upper()}: {response}")
                
                debates_text = "\n\n".join(all_debates)
                
                final_prompt = f"""Problem: {question}

All debate responses:
{debates_text}

Final answer:"""
                
                final_answer = await async_generate_completion(
                    agent_id=judge_agent,
                    prompt=final_prompt,
                    system_prompt="Provide the final answer based on debates.",
                    temperature=0.3
                )
                
                total_tokens += len(final_prompt.split()) * 1.3 + len(final_answer.split()) * 1.3
                
                duration = time.time() - start_time
                
                print(f"    ✅ LLM-Debate: {duration:.2f}s, {total_tokens:.0f} tokens")
                return final_answer, total_tokens, duration
                
            except Exception as e:
                pass
        
        duration = time.time() - start_time
        print(f"    ❌ LLM-Debate: Failed")
        return None, total_tokens, duration

async def run_fair_comparison():
    """运行公平对比测试"""
    
    # 使用相同的简单问题
    test_problems = [
        {
            'question': "Janet's ducks lay 16 eggs per day. She eats 3 for breakfast and bakes muffins with 4. She sells the remainder for $2 each. How much does she make daily?",
            'type': 'math'
        },
        {
            'question': "Find the value of x such that sqrt(x+7) = 9.",
            'type': 'math'
        },
        {
            'question': "If a train travels 120 miles in 2 hours, what is its average speed?",
            'type': 'math'
        }
    ]
    
    tester = FairComparisonTest()
    
    print("🚀 FAIR COMPARISON: DMC vs LLM-Debate")
    print("=" * 60)
    
    dmc_results = []
    debate_results = []
    
    for i, problem in enumerate(test_problems):
        print(f"\n📝 Problem {i+1}: {problem['question'][:60]}...")
        
        # 测试DMC
        dmc_solution, dmc_tokens, dmc_time = await tester.test_dmc_on_simple_problem(
            problem['question'], problem['type']
        )
        
        if dmc_solution:
            dmc_results.append({
                'tokens': dmc_tokens,
                'time': dmc_time,
                'problem': problem['question']
            })
        
        # 测试LLM-Debate
        debate_solution, debate_tokens, debate_time = await tester.test_llm_debate_on_simple_problem(
            problem['question'], problem['type']
        )
        
        if debate_solution:
            debate_results.append({
                'tokens': debate_tokens,
                'time': debate_time,
                'problem': problem['question']
            })
        
        # 对比结果
        if dmc_tokens > 0 and debate_tokens > 0:
            token_reduction = (debate_tokens - dmc_tokens) / debate_tokens * 100
            time_reduction = (debate_time - dmc_time) / debate_time * 100
            print(f"    💰 DMC vs Debate: {token_reduction:.1f}% token reduction, {time_reduction:.1f}% time reduction")
    
    # 总体分析
    if dmc_results and debate_results:
        print(f"\n📊 OVERALL COMPARISON")
        print("=" * 60)
        
        avg_dmc_tokens = statistics.mean([r['tokens'] for r in dmc_results])
        avg_dmc_time = statistics.mean([r['time'] for r in dmc_results])
        avg_debate_tokens = statistics.mean([r['tokens'] for r in debate_results])
        avg_debate_time = statistics.mean([r['time'] for r in debate_results])
        
        overall_token_reduction = (avg_debate_tokens - avg_dmc_tokens) / avg_debate_tokens * 100
        overall_time_reduction = (avg_debate_time - avg_dmc_time) / avg_debate_time * 100
        
        print(f"DMC Average: {avg_dmc_tokens:.0f} tokens, {avg_dmc_time:.2f}s")
        print(f"LLM-Debate Average: {avg_debate_tokens:.0f} tokens, {avg_debate_time:.2f}s")
        print(f"Overall Token Reduction: {overall_token_reduction:.1f}%")
        print(f"Overall Time Reduction: {overall_time_reduction:.1f}%")
        
        # 保存结果
        results = {
            'timestamp': time.time(),
            'summary': {
                'dmc_avg_tokens': avg_dmc_tokens,
                'debate_avg_tokens': avg_debate_tokens,
                'token_reduction_pct': overall_token_reduction,
                'dmc_avg_time': avg_dmc_time,
                'debate_avg_time': avg_debate_time,
                'time_reduction_pct': overall_time_reduction
            },
            'dmc_results': dmc_results,
            'debate_results': debate_results
        }
        
        output_file = Path("results/fair_comparison_results.json")
        output_file.parent.mkdir(exist_ok=True)
        
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n💾 Results saved to: {output_file}")
        
        return results
    
    else:
        print("❌ Insufficient data for comparison")
        return None

if __name__ == "__main__":
    asyncio.run(run_fair_comparison())
