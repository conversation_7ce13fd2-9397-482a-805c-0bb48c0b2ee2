#!/usr/bin/env python3
"""
Extended Efficiency Test
扩展的效率对比测试，包含更多题库和详细的token分析
"""

import asyncio
import json
import time
import sys
import os
from typing import List, Dict, Any
import statistics
from pathlib import Path

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.api import async_generate_completion
from utils.token_utils import TokenCounter

class ExtendedEfficiencyTest:
    """扩展效率测试"""
    
    def __init__(self):
        self.agents = ["openai", "gemini", "llama"]
        self.token_counter = TokenCounter()
        
        # 扩展的测试题库
        self.test_datasets = {
            'gsm8k': [
                "<PERSON>'s ducks lay 16 eggs per day. She eats 3 for breakfast and bakes muffins with 4. She sells the remainder for $2 each. How much does she make daily?",
                "A robe takes 2 bolts of blue fiber and half that much white fiber. How many bolts in total does it take?",
                "Josh decides to try flipping a house. He buys a house for $80,000 and then puts in $50,000 in repairs. This increased the value of the house by 150%. How much profit did he make?",
                "<PERSON> decides to run 3 sprints 3 times a week. He runs 60 meters each sprint. How many total meters does he run a week?",
                "Every day, <PERSON>di feeds each of her chickens three cups of mixed chicken feed. She gives the chickens their feed in three separate meals. How many cups of feed does she need in the morning, if she has 20 chickens?"
            ],
            'math': [
                "Find the value of x such that sqrt(x+7) = 9.",
                "If f(x) = x^2 + 2x + 1, find f(3).",
                "Solve for x: 2x + 5 = 17.",
                "What is the area of a circle with radius 5?",
                "Factor x^2 - 9."
            ],
            'drop': [
                "The 2010 United States Census reported that Fresno had a population of 494,665. In the census, 245,306 people (49.6%) were White, 40,960 people (8.3%) were African American. How many more White people than African American people were there?",
                "In 2015, the city's population was estimated at 520,159. If the 2010 population was 494,665, what was the population increase?",
                "The median household income was $41,455. If there were 158,349 households, what was the total household income?",
                "Of the 494,665 people, 230,049 were male and 264,616 were female. What percentage were female?",
                "The city covers 112.0 square miles of land. What is the population density per square mile?"
            ],
            'humaneval': [
                "def has_close_elements(numbers: List[float], threshold: float) -> bool:\n    \"\"\" Check if in given list of numbers, are any two numbers closer to each other than given threshold.\n    >>> has_close_elements([1.0, 2.0, 3.0], 0.5)\n    False\n    >>> has_close_elements([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3)\n    True\n    \"\"\"",
                "def separate_paren_groups(paren_string: str) -> List[str]:\n    \"\"\" Input to this function is a string containing multiple groups of nested parentheses. Your goal is to separate those group and return the list of those.\n    >>> separate_paren_groups('( ) (( )) (( )( ))')\n    ['()', '(())', '(()())']\n    \"\"\"",
                "def truncate_number(number: float) -> float:\n    \"\"\" Given a positive floating point number, it can be decomposed into and integer part and decimals. Return the decimal part of the number.\n    >>> truncate_number(3.5)\n    0.5\n    \"\"\"",
                "def below_zero(operations: List[int]) -> bool:\n    \"\"\" You're given a list of deposit and withdrawal operations on a bank account that starts with zero balance. Your task is to detect if at any point the balance falls below zero.\n    >>> below_zero([1, 2, 3])\n    False\n    >>> below_zero([1, 2, -4, 5])\n    True\n    \"\"\"",
                "def mean_absolute_deviation(numbers: List[float]) -> float:\n    \"\"\" For a given list of input numbers, calculate Mean Absolute Deviation around the mean of this dataset.\n    >>> mean_absolute_deviation([1.0, 2.0, 3.0, 4.0])\n    1.0\n    \"\"\""
            ],
            'mmlu': [
                "Which of the following is NOT a characteristic of a good hypothesis? (A) It is testable (B) It is falsifiable (C) It is complex (D) It makes predictions",
                "What is the primary function of mitochondria in cells? (A) Protein synthesis (B) Energy production (C) DNA storage (D) Waste removal",
                "In economics, what does GDP stand for? (A) General Domestic Product (B) Gross Domestic Product (C) Global Development Program (D) Government Debt Policy",
                "Which planet is closest to the Sun? (A) Venus (B) Earth (C) Mercury (D) Mars",
                "What is the chemical symbol for gold? (A) Go (B) Gd (C) Au (D) Ag"
            ]
        }
    
    async def test_dmc_single_round(self, question: str, dataset: str):
        """测试DMC单轮完整流程"""
        
        total_tokens = 0
        start_time = time.time()
        phase_details = {}
        
        # Phase 1: Draft Generation
        draft_tasks = []
        for agent in self.agents:
            task = async_generate_completion(
                agent_id=agent,
                prompt=question,
                system_prompt=f"Solve this {dataset} problem concisely and accurately.",
                temperature=0.3
            )
            draft_tasks.append(task)
        
        draft_responses = await asyncio.gather(*draft_tasks, return_exceptions=True)
        valid_drafts = [r for r in draft_responses if not isinstance(r, Exception)]
        
        if not valid_drafts:
            return None, 0, 0, {}
        
        best_draft = max(valid_drafts, key=len)
        
        # 计算Phase 1 tokens
        phase1_tokens = 0
        for response in valid_drafts:
            input_tokens = self.token_counter.count_tokens(question)
            output_tokens = self.token_counter.count_tokens(str(response))
            phase1_tokens += input_tokens + output_tokens
        
        total_tokens += phase1_tokens
        phase_details['draft_generation'] = phase1_tokens
        
        # Phase 2: Annotation
        annotation_tasks = []
        for agent in self.agents:
            annotation_prompt = f"Solution: {best_draft}\n\nProvide structured feedback (max 100 tokens):"
            
            task = async_generate_completion(
                agent_id=agent,
                prompt=annotation_prompt,
                system_prompt="Provide brief, focused feedback.",
                temperature=0.3
            )
            annotation_tasks.append((agent, task, annotation_prompt))
        
        phase2_tokens = 0
        annotations = []
        for agent, task, prompt in annotation_tasks:
            try:
                annotation = await task
                bounded_annotation = " ".join(str(annotation).split()[:100])
                annotations.append(bounded_annotation)
                
                input_tokens = self.token_counter.count_tokens(prompt)
                output_tokens = self.token_counter.count_tokens(bounded_annotation)
                phase2_tokens += input_tokens + output_tokens
                
            except Exception as e:
                continue
        
        total_tokens += phase2_tokens
        phase_details['annotation'] = phase2_tokens
        
        # Phase 3: Merge
        phase3_tokens = 0
        if annotations:
            annotations_text = "\n".join([f"Feedback {i+1}: {ann}" for i, ann in enumerate(annotations)])
            
            merge_prompt = f"""Original solution: {best_draft}

Agent feedback:
{annotations_text}

Integrate the feedback to create an improved solution:"""
            
            try:
                merged_solution = await async_generate_completion(
                    agent_id="openai",
                    prompt=merge_prompt,
                    system_prompt="Merge the solution with feedback intelligently.",
                    temperature=0.3
                )
                
                input_tokens = self.token_counter.count_tokens(merge_prompt)
                output_tokens = self.token_counter.count_tokens(merged_solution)
                phase3_tokens = input_tokens + output_tokens
                
            except Exception as e:
                merged_solution = best_draft
        else:
            merged_solution = best_draft
        
        total_tokens += phase3_tokens
        phase_details['merge'] = phase3_tokens
        
        # Phase 4: Leader Evaluation
        evaluation_prompt = f"""Evaluate this solution:

Problem: {question}
Solution: {merged_solution}

Provide quality score (0.0-1.0) and brief assessment:"""
        
        phase4_tokens = 0
        try:
            evaluation = await async_generate_completion(
                agent_id="gemini",
                prompt=evaluation_prompt,
                system_prompt="Evaluate solution quality objectively.",
                temperature=0.3
            )
            
            input_tokens = self.token_counter.count_tokens(evaluation_prompt)
            output_tokens = self.token_counter.count_tokens(evaluation)
            phase4_tokens = input_tokens + output_tokens
            
        except Exception as e:
            pass
        
        total_tokens += phase4_tokens
        phase_details['leader_evaluation'] = phase4_tokens
        
        duration = time.time() - start_time
        
        return merged_solution, total_tokens, duration, phase_details
    
    async def test_cot_sc(self, question: str, dataset: str, num_samples: int = 3):
        """测试CoT Self-Consistency"""
        
        total_tokens = 0
        start_time = time.time()
        
        cot_prompt = f"""Solve this {dataset} problem step by step. Think through each step carefully and show your reasoning.

Problem: {question}

Let me think step by step:"""
        
        responses = []
        
        # 多次采样
        for i in range(num_samples):
            try:
                response = await async_generate_completion(
                    agent_id="openai",
                    prompt=cot_prompt,
                    system_prompt="You are an expert problem solver. Think step by step and show your reasoning clearly.",
                    temperature=0.7  # 更高温度获得多样性
                )
                
                responses.append(response)
                
                input_tokens = self.token_counter.count_tokens(cot_prompt)
                output_tokens = self.token_counter.count_tokens(response)
                total_tokens += input_tokens + output_tokens
                
            except Exception as e:
                continue
        
        # 一致性判断
        if len(responses) > 1:
            consistency_prompt = f"""Here are {len(responses)} different solutions to the same problem:

Problem: {question}

Solutions:
""" + "\n\n".join([f"Solution {i+1}: {resp}" for i, resp in enumerate(responses)]) + """

Determine the most consistent and correct answer:"""
            
            try:
                final_answer = await async_generate_completion(
                    agent_id="openai",
                    prompt=consistency_prompt,
                    system_prompt="Choose the most consistent and correct solution.",
                    temperature=0.3
                )
                
                input_tokens = self.token_counter.count_tokens(consistency_prompt)
                output_tokens = self.token_counter.count_tokens(final_answer)
                total_tokens += input_tokens + output_tokens
                
            except Exception as e:
                final_answer = responses[0] if responses else ""
        else:
            final_answer = responses[0] if responses else ""
        
        duration = time.time() - start_time
        
        return final_answer, total_tokens, duration
    
    async def test_llm_debate(self, question: str, dataset: str):
        """测试LLM-Debate"""
        
        total_tokens = 0
        start_time = time.time()
        
        # Phase 1: Independent answers
        initial_answers = {}
        
        for agent in self.agents:
            try:
                prompt = f"Solve this {dataset} problem independently:\n\n{question}\n\nProvide your complete solution:"
                
                response = await async_generate_completion(
                    agent_id=agent,
                    prompt=prompt,
                    system_prompt=f"You are an expert in {dataset} problems. Solve problems independently.",
                    temperature=0.3
                )
                
                initial_answers[agent] = response
                
                input_tokens = self.token_counter.count_tokens(prompt)
                output_tokens = self.token_counter.count_tokens(response)
                total_tokens += input_tokens + output_tokens
                
            except Exception as e:
                continue
        
        if len(initial_answers) < 2:
            return None, 0, 0
        
        # Phase 2: Debate phase
        debate_responses = {}
        
        for agent in self.agents:
            if agent not in initial_answers:
                continue
                
            try:
                others_answers = []
                for other_agent, answer in initial_answers.items():
                    if other_agent != agent:
                        others_answers.append(f"{other_agent.upper()}: {answer}")
                
                others_text = "\n\n".join(others_answers)
                
                debate_prompt = f"""Original problem: {question}

Your initial answer: {initial_answers[agent]}

Other agents' answers:
{others_text}

Now engage in debate and provide your refined final answer:"""
                
                debate_response = await async_generate_completion(
                    agent_id=agent,
                    prompt=debate_prompt,
                    system_prompt="Engage in constructive debate and refine your answer.",
                    temperature=0.3
                )
                
                debate_responses[agent] = debate_response
                
                input_tokens = self.token_counter.count_tokens(debate_prompt)
                output_tokens = self.token_counter.count_tokens(debate_response)
                total_tokens += input_tokens + output_tokens
                
            except Exception as e:
                continue
        
        # Phase 3: Final decision
        if debate_responses:
            try:
                judge_agent = self.agents[0]
                
                all_debates = []
                for agent, response in debate_responses.items():
                    all_debates.append(f"{agent.upper()}: {response}")
                
                debates_text = "\n\n".join(all_debates)
                
                final_prompt = f"""Original problem: {question}

All agents' debate responses:
{debates_text}

As the judge, synthesize the best solution from all the debates:"""
                
                final_answer = await async_generate_completion(
                    agent_id=judge_agent,
                    prompt=final_prompt,
                    system_prompt="You are the judge. Synthesize the best solution from all debates.",
                    temperature=0.3
                )
                
                input_tokens = self.token_counter.count_tokens(final_prompt)
                output_tokens = self.token_counter.count_tokens(final_answer)
                total_tokens += input_tokens + output_tokens
                
                duration = time.time() - start_time
                return final_answer, total_tokens, duration
                
            except Exception as e:
                pass
        
        duration = time.time() - start_time
        return None, total_tokens, duration

async def run_extended_efficiency_test():
    """运行扩展的效率测试"""
    
    tester = ExtendedEfficiencyTest()
    
    print("🚀 EXTENDED EFFICIENCY TEST - Single Round Comparison")
    print("=" * 80)
    
    all_results = {
        'dmc': [],
        'cot_sc': [],
        'llm_debate': []
    }
    
    dataset_results = {}
    
    for dataset_name, questions in tester.test_datasets.items():
        print(f"\n📊 Testing Dataset: {dataset_name.upper()}")
        print("-" * 60)
        
        dataset_results[dataset_name] = {
            'dmc': [],
            'cot_sc': [],
            'llm_debate': []
        }
        
        for i, question in enumerate(questions):
            print(f"\n  Question {i+1}: {question[:80]}...")
            
            # Test DMC
            print("    🤝 Testing DMC (1 round)...")
            try:
                dmc_solution, dmc_tokens, dmc_time, dmc_phases = await tester.test_dmc_single_round(question, dataset_name)
                
                if dmc_solution:
                    result = {
                        'tokens': dmc_tokens,
                        'time': dmc_time,
                        'phases': dmc_phases,
                        'dataset': dataset_name,
                        'question_id': i
                    }
                    all_results['dmc'].append(result)
                    dataset_results[dataset_name]['dmc'].append(result)
                    print(f"      ✅ DMC: {dmc_tokens} tokens, {dmc_time:.2f}s")
                    
            except Exception as e:
                print(f"      ❌ DMC failed: {e}")
            
            # Test CoT-SC
            print("    🧠🔄 Testing CoT Self-Consistency...")
            try:
                cotsc_solution, cotsc_tokens, cotsc_time = await tester.test_cot_sc(question, dataset_name)
                
                if cotsc_solution:
                    result = {
                        'tokens': cotsc_tokens,
                        'time': cotsc_time,
                        'dataset': dataset_name,
                        'question_id': i
                    }
                    all_results['cot_sc'].append(result)
                    dataset_results[dataset_name]['cot_sc'].append(result)
                    print(f"      ✅ CoT-SC: {cotsc_tokens} tokens, {cotsc_time:.2f}s")
                    
            except Exception as e:
                print(f"      ❌ CoT-SC failed: {e}")
            
            # Test LLM-Debate
            print("    🥊 Testing LLM-Debate...")
            try:
                debate_solution, debate_tokens, debate_time = await tester.test_llm_debate(question, dataset_name)
                
                if debate_solution:
                    result = {
                        'tokens': debate_tokens,
                        'time': debate_time,
                        'dataset': dataset_name,
                        'question_id': i
                    }
                    all_results['llm_debate'].append(result)
                    dataset_results[dataset_name]['llm_debate'].append(result)
                    print(f"      ✅ LLM-Debate: {debate_tokens} tokens, {debate_time:.2f}s")
                    
            except Exception as e:
                print(f"      ❌ LLM-Debate failed: {e}")
    
    # 分析结果
    print(f"\n📊 OVERALL ANALYSIS - Single Round Efficiency")
    print("=" * 80)
    
    method_stats = {}
    for method_name, method_results in all_results.items():
        if method_results:
            avg_tokens = statistics.mean([r['tokens'] for r in method_results])
            avg_time = statistics.mean([r['time'] for r in method_results])
            method_stats[method_name] = {
                'avg_tokens': avg_tokens,
                'avg_time': avg_time,
                'count': len(method_results)
            }
            print(f"{method_name.upper()}: {avg_tokens:.0f} tokens, {avg_time:.2f}s average ({len(method_results)} samples)")
    
    # 计算效率对比
    if 'dmc' in method_stats:
        dmc_tokens = method_stats['dmc']['avg_tokens']
        dmc_time = method_stats['dmc']['avg_time']
        
        print(f"\nDMC Efficiency vs Other Methods (Single Round):")
        for method_name, stats in method_stats.items():
            if method_name != 'dmc':
                token_reduction = (stats['avg_tokens'] - dmc_tokens) / stats['avg_tokens'] * 100
                time_reduction = (stats['avg_time'] - dmc_time) / stats['avg_time'] * 100
                print(f"  vs {method_name.upper()}: {token_reduction:.1f}% token reduction, {time_reduction:.1f}% time reduction")
    
    # 保存结果
    output_file = Path("results/extended_efficiency_results.json")
    output_file.parent.mkdir(exist_ok=True)
    
    summary_results = {
        'timestamp': time.time(),
        'test_type': 'single_round_comparison',
        'method_stats': method_stats,
        'dataset_results': dataset_results,
        'all_results': all_results
    }
    
    with open(output_file, 'w') as f:
        json.dump(summary_results, f, indent=2)
    
    print(f"\n💾 Results saved to: {output_file}")
    
    return summary_results

if __name__ == "__main__":
    asyncio.run(run_extended_efficiency_test())
