#!/usr/bin/env python3
"""
Real Dialogue-based Multi-Agent System
实现真实的对话式多agent系统用于对比
"""

import asyncio
import json
import time
import sys
import os
from typing import List, Dict, Any
import statistics
from pathlib import Path

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.api import async_generate_completion

class RealDialogueSystem:
    """真实的对话式多Agent系统"""
    
    def __init__(self, agents: List[str]):
        self.agents = agents
        self.agent_roles = {
            "openai": "Analytical Agent - Focus on logical reasoning and step-by-step analysis",
            "gemini": "Creative Agent - Provide alternative perspectives and creative solutions", 
            "llama": "Critical Agent - Question assumptions and identify potential errors"
        }
    
    async def llm_debate_solve(self, question: str, problem_type: str = "general"):
        """LLM-Debate风格的解决方案"""
        
        total_tokens = 0
        start_time = time.time()
        
        # Phase 1: 独立初始回答
        print("    Phase 1: Independent answers...")
        initial_answers = {}
        
        for agent in self.agents:
            try:
                prompt = f"Solve this {problem_type} problem independently:\n\n{question}\n\nProvide your complete solution:"
                
                response = await async_generate_completion(
                    agent_id=agent,
                    prompt=prompt,
                    system_prompt=f"You are {self.agent_roles[agent]}. Solve problems independently.",
                    temperature=0.3
                )
                
                initial_answers[agent] = response
                total_tokens += len(prompt.split()) * 1.3 + len(response.split()) * 1.3
                
            except Exception as e:
                print(f"      {agent} failed: {e}")
                continue
        
        if len(initial_answers) < 2:
            return None, total_tokens, time.time() - start_time
        
        # Phase 2: 辩论阶段
        print("    Phase 2: Debate phase...")
        debate_responses = {}
        
        for agent in self.agents:
            if agent not in initial_answers:
                continue
                
            try:
                # 构建其他agent的答案
                others_answers = []
                for other_agent, answer in initial_answers.items():
                    if other_agent != agent:
                        others_answers.append(f"{other_agent.upper()}: {answer}")
                
                others_text = "\n\n".join(others_answers)
                
                debate_prompt = f"""Original problem: {question}

Your initial answer: {initial_answers[agent]}

Other agents' answers:
{others_text}

Now engage in debate: 
1. Analyze the other answers
2. Defend your position or acknowledge better solutions
3. Provide your refined final answer

Your debate response:"""
                
                debate_response = await async_generate_completion(
                    agent_id=agent,
                    prompt=debate_prompt,
                    system_prompt=f"You are {self.agent_roles[agent]}. Engage in constructive debate.",
                    temperature=0.3
                )
                
                debate_responses[agent] = debate_response
                total_tokens += len(debate_prompt.split()) * 1.3 + len(debate_response.split()) * 1.3
                
            except Exception as e:
                print(f"      {agent} debate failed: {e}")
                continue
        
        # Phase 3: 最终决策
        print("    Phase 3: Final decision...")
        if debate_responses:
            try:
                # 选择一个agent作为judge
                judge_agent = self.agents[0]
                
                all_debates = []
                for agent, response in debate_responses.items():
                    all_debates.append(f"{agent.upper()}: {response}")
                
                debates_text = "\n\n".join(all_debates)
                
                final_prompt = f"""Original problem: {question}

All agents' debate responses:
{debates_text}

As the judge, synthesize the best solution from all the debates. Provide the final answer:"""
                
                final_answer = await async_generate_completion(
                    agent_id=judge_agent,
                    prompt=final_prompt,
                    system_prompt="You are the judge. Synthesize the best solution from all debates.",
                    temperature=0.3
                )
                
                total_tokens += len(final_prompt.split()) * 1.3 + len(final_answer.split()) * 1.3
                
                return final_answer, total_tokens, time.time() - start_time
                
            except Exception as e:
                print(f"      Final decision failed: {e}")
        
        return None, total_tokens, time.time() - start_time
    
    async def conversation_solve(self, question: str, problem_type: str = "general", max_rounds: int = 3):
        """对话式协作解决方案"""
        
        total_tokens = 0
        start_time = time.time()
        conversation_history = [f"PROBLEM: {question}"]
        
        print(f"    Conversation-based solving ({max_rounds} rounds)...")
        
        for round_num in range(max_rounds):
            print(f"      Round {round_num + 1}...")
            
            for agent in self.agents:
                try:
                    # 构建完整对话历史
                    full_context = "\n".join(conversation_history)
                    
                    prompt = f"""Conversation so far:
{full_context}

You are {self.agent_roles[agent]}. 

Continue the conversation by:
1. Analyzing what has been discussed
2. Adding your insights or corrections
3. Building toward a solution

Your response:"""
                    
                    response = await async_generate_completion(
                        agent_id=agent,
                        prompt=prompt,
                        system_prompt=f"You are {self.agent_roles[agent]}. Participate in collaborative problem-solving.",
                        temperature=0.3
                    )
                    
                    # 添加到对话历史
                    conversation_history.append(f"{agent.upper()}: {response}")
                    total_tokens += len(prompt.split()) * 1.3 + len(response.split()) * 1.3
                    
                except Exception as e:
                    print(f"        {agent} failed: {e}")
                    continue
        
        # 提取最终答案
        try:
            final_context = "\n".join(conversation_history)
            extract_prompt = f"""Based on this conversation:
{final_context}

Extract and provide the final answer to the original problem:"""
            
            final_answer = await async_generate_completion(
                agent_id=self.agents[0],
                prompt=extract_prompt,
                system_prompt="Extract the final answer from the conversation.",
                temperature=0.3
            )
            
            total_tokens += len(extract_prompt.split()) * 1.3 + len(final_answer.split()) * 1.3
            
            return final_answer, total_tokens, time.time() - start_time
            
        except Exception as e:
            print(f"      Final extraction failed: {e}")
            return None, total_tokens, time.time() - start_time

async def compare_real_systems():
    """对比真实的多agent系统"""
    
    test_problems = [
        {
            'question': "Janet's ducks lay 16 eggs per day. She eats 3 for breakfast and bakes muffins with 4. She sells the remainder for $2 each. How much does she make daily?",
            'type': 'math'
        },
        {
            'question': "Find the value of x such that sqrt(x+7) = 9.",
            'type': 'math'
        },
        {
            'question': "def has_close_elements(numbers, threshold): # Check if any two numbers are closer than threshold",
            'type': 'coding'
        }
    ]
    
    agents = ["openai", "gemini", "llama"]
    dialogue_system = RealDialogueSystem(agents)
    
    print("🚀 Real Multi-Agent Systems Comparison")
    print("=" * 60)
    
    results = {
        'llm_debate': [],
        'conversation': [],
        'dmc_simulation': []  # 我们之前的DMC结果作为对比
    }
    
    for i, problem in enumerate(test_problems):
        print(f"\n📝 Problem {i+1}: {problem['question'][:50]}...")
        
        # 测试LLM-Debate系统
        print("  🥊 Testing LLM-Debate System...")
        try:
            answer, tokens, duration = await dialogue_system.llm_debate_solve(
                problem['question'], problem['type']
            )
            
            if answer:
                results['llm_debate'].append({
                    'tokens': tokens,
                    'time': duration,
                    'success': True
                })
                print(f"    ✅ LLM-Debate: {duration:.2f}s, {tokens:.0f} tokens")
            else:
                print(f"    ❌ LLM-Debate failed")
                
        except Exception as e:
            print(f"    ❌ LLM-Debate error: {e}")
        
        # 测试对话系统
        print("  💬 Testing Conversation System...")
        try:
            answer, tokens, duration = await dialogue_system.conversation_solve(
                problem['question'], problem['type']
            )
            
            if answer:
                results['conversation'].append({
                    'tokens': tokens,
                    'time': duration,
                    'success': True
                })
                print(f"    ✅ Conversation: {duration:.2f}s, {tokens:.0f} tokens")
            else:
                print(f"    ❌ Conversation failed")
                
        except Exception as e:
            print(f"    ❌ Conversation error: {e}")
    
    # 分析结果
    print("\n📊 COMPARISON RESULTS")
    print("=" * 60)
    
    for system_name, system_results in results.items():
        if system_results:
            avg_tokens = statistics.mean([r['tokens'] for r in system_results])
            avg_time = statistics.mean([r['time'] for r in system_results])
            print(f"{system_name.upper()}: {avg_tokens:.0f} tokens, {avg_time:.2f}s average")
    
    # 保存结果
    output_file = Path("results/real_dialogue_comparison.json")
    output_file.parent.mkdir(exist_ok=True)
    
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Results saved to: {output_file}")
    
    return results

if __name__ == "__main__":
    asyncio.run(compare_real_systems())
