#!/usr/bin/env python3
"""
更好的可视化方案 - 替代散点图的多种图表类型
适合单数据点或少量数据点的情况
"""

import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
from pathlib import Path

# 设置小清新风格
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.facecolor'] = 'white'
plt.rcParams['axes.facecolor'] = '#fafafa'
sns.set_style("whitegrid")
sns.set_palette("pastel")

def create_sample_data():
    """创建示例数据"""
    return pd.DataFrame({
        'Method': ['DMC', 'CoT-SC', 'LLM-Debate'],
        'Tokens': [2500, 4200, 3800],
        'Time': [15.2, 28.5, 25.1],
        'Accuracy': [0.75, 0.82, 0.79]
    })

def create_bar_charts():
    """创建柱状图 - 最直观的对比方式"""
    df = create_sample_data()
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)
    
    # 1. 单独的Token使用柱状图 - 小清新风格
    fig, ax = plt.subplots(1, 1, figsize=(10, 8))

    # 小清新配色方案
    colors = ['#87CEEB', '#FFB6C1', '#98FB98']  # 天空蓝、粉红、淡绿
    bars = ax.bar(df['Method'], df['Tokens'], color=colors, alpha=0.85,
                  edgecolor='white', linewidth=1.5)
    
    # 添加数值标签
    for bar, value in zip(bars, df['Tokens']):
        ax.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 50,
                f'{int(value)}', ha='center', va='bottom', 
                fontsize=16, fontweight='bold')
    
    ax.set_title('Token Usage Comparison', fontsize=18, fontweight='normal', pad=20, color='#4A4A4A')
    ax.set_ylabel('Tokens per Question', fontsize=14, fontweight='normal', color='#666666')
    ax.set_xlabel('Method', fontsize=14, fontweight='normal', color='#666666')
    ax.grid(True, alpha=0.2, axis='y', color='#E0E0E0')
    ax.set_ylim(0, max(df['Tokens']) * 1.15)
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_color('#E0E0E0')
    ax.spines['bottom'].set_color('#E0E0E0')
    
    plt.tight_layout()
    plt.savefig(results_dir / 'token_comparison_bar.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 双指标柱状图（并排）
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    
    x = np.arange(len(df))
    width = 0.35
    
    # 标准化数据用于对比
    token_normalized = df['Tokens'] / df['Tokens'].max()
    time_normalized = df['Time'] / df['Time'].max()
    
    bars1 = ax.bar(x - width/2, token_normalized, width,
                   label='Token Usage (normalized)', color='#87CEEB', alpha=0.85,
                   edgecolor='white', linewidth=1)
    bars2 = ax.bar(x + width/2, time_normalized, width,
                   label='Processing Time (normalized)', color='#FFB6C1', alpha=0.85,
                   edgecolor='white', linewidth=1)
    
    # 添加数值标签
    for i, (bar1, bar2) in enumerate(zip(bars1, bars2)):
        ax.text(bar1.get_x() + bar1.get_width()/2., bar1.get_height() + 0.02,
                f'{int(df.iloc[i]["Tokens"])}', ha='center', va='bottom', 
                fontsize=12, fontweight='bold')
        ax.text(bar2.get_x() + bar2.get_width()/2., bar2.get_height() + 0.02,
                f'{df.iloc[i]["Time"]:.1f}s', ha='center', va='bottom', 
                fontsize=12, fontweight='bold')
    
    ax.set_xlabel('Method', fontsize=14, fontweight='normal', color='#666666')
    ax.set_ylabel('Normalized Value', fontsize=14, fontweight='normal', color='#666666')
    ax.set_title('Efficiency Comparison (Normalized)', fontsize=16, fontweight='normal', pad=20, color='#4A4A4A')
    ax.set_xticks(x)
    ax.set_xticklabels(df['Method'])
    ax.legend(fontsize=12, frameon=False)
    ax.grid(True, alpha=0.2, axis='y', color='#E0E0E0')
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_color('#E0E0E0')
    ax.spines['bottom'].set_color('#E0E0E0')
    
    plt.tight_layout()
    plt.savefig(results_dir / 'normalized_comparison_bar.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_radar_chart():
    """创建雷达图 - 多维度对比"""
    df = create_sample_data()
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)
    
    # 准备雷达图数据（标准化到0-1）
    metrics = ['Tokens', 'Time', 'Accuracy']
    
    # 对于Token和Time，值越小越好，所以取倒数
    df_radar = df.copy()
    df_radar['Tokens'] = 1 - (df['Tokens'] - df['Tokens'].min()) / (df['Tokens'].max() - df['Tokens'].min())
    df_radar['Time'] = 1 - (df['Time'] - df['Time'].min()) / (df['Time'].max() - df['Time'].min())
    # Accuracy保持原值（越大越好）
    
    # 设置雷达图
    angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
    angles += angles[:1]  # 闭合图形
    
    fig, ax = plt.subplots(1, 1, figsize=(10, 10), subplot_kw=dict(projection='polar'))

    # 小清新雷达图配色
    colors = ['#87CEEB', '#FFB6C1', '#98FB98']

    for i, method in enumerate(df['Method']):
        values = df_radar.iloc[i][metrics].tolist()
        values += values[:1]  # 闭合图形

        ax.plot(angles, values, 'o-', linewidth=2.5, label=method, color=colors[i], markersize=6)
        ax.fill(angles, values, alpha=0.15, color=colors[i])
    
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(metrics, fontsize=12, fontweight='normal', color='#666666')
    ax.set_ylim(0, 1)
    ax.set_title('Multi-dimensional Performance Comparison',
                 fontsize=16, fontweight='normal', pad=30, color='#4A4A4A')
    ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0), fontsize=12, frameon=False)
    ax.grid(True, alpha=0.3, color='#E0E0E0')
    ax.set_facecolor('#fafafa')
    
    plt.tight_layout()
    plt.savefig(results_dir / 'radar_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_heatmap():
    """创建热力图 - 显示相对性能"""
    df = create_sample_data()
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)
    
    # 创建相对性能矩阵
    baseline_method = 'DMC'
    baseline_idx = df[df['Method'] == baseline_method].index[0]
    
    relative_data = []
    for _, row in df.iterrows():
        relative_tokens = row['Tokens'] / df.iloc[baseline_idx]['Tokens']
        relative_time = row['Time'] / df.iloc[baseline_idx]['Time']
        relative_accuracy = row['Accuracy'] / df.iloc[baseline_idx]['Accuracy']
        
        relative_data.append([relative_tokens, relative_time, relative_accuracy])
    
    relative_df = pd.DataFrame(relative_data, 
                              index=df['Method'],
                              columns=['Token Ratio', 'Time Ratio', 'Accuracy Ratio'])
    
    # 创建小清新热力图
    fig, ax = plt.subplots(1, 1, figsize=(10, 6))

    # 使用小清新配色方案
    sns.heatmap(relative_df, annot=True, fmt='.2f', cmap='RdYlBu_r',
                center=1.0, square=True, linewidths=1, linecolor='white',
                cbar_kws={"shrink": .8}, ax=ax,
                annot_kws={'fontsize': 12, 'color': '#4A4A4A'})

    ax.set_title(f'Relative Performance vs {baseline_method} (Baseline=1.0)',
                 fontsize=14, fontweight='normal', pad=20, color='#4A4A4A')
    ax.set_xlabel('Performance Metrics', fontsize=12, fontweight='normal', color='#666666')
    ax.set_ylabel('Methods', fontsize=12, fontweight='normal', color='#666666')
    
    plt.tight_layout()
    plt.savefig(results_dir / 'performance_heatmap.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_efficiency_ratio_chart():
    """创建效率比率图 - 更直观的效率对比"""
    df = create_sample_data()
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)
    
    # 计算效率指标（准确率/资源消耗）
    df['Token_Efficiency'] = df['Accuracy'] / (df['Tokens'] / 1000)  # 每千token的准确率
    df['Time_Efficiency'] = df['Accuracy'] / df['Time']  # 每秒的准确率
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

    # 小清新配色
    colors = ['#87CEEB', '#FFB6C1', '#98FB98']

    # Token效率
    bars1 = ax1.bar(df['Method'], df['Token_Efficiency'], color=colors, alpha=0.85,
                    edgecolor='white', linewidth=1.5)
    
    for bar, value in zip(bars1, df['Token_Efficiency']):
        ax1.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.01,
                f'{value:.3f}', ha='center', va='bottom', 
                fontsize=14, fontweight='bold')
    
    ax1.set_title('Token Efficiency\n(Accuracy per 1K Tokens)', fontsize=14, fontweight='normal', color='#4A4A4A')
    ax1.set_ylabel('Efficiency Score', fontsize=12, fontweight='normal', color='#666666')
    ax1.grid(True, alpha=0.2, axis='y', color='#E0E0E0')
    ax1.spines['top'].set_visible(False)
    ax1.spines['right'].set_visible(False)
    ax1.spines['left'].set_color('#E0E0E0')
    ax1.spines['bottom'].set_color('#E0E0E0')

    # Time效率
    bars2 = ax2.bar(df['Method'], df['Time_Efficiency'], color=colors, alpha=0.85,
                    edgecolor='white', linewidth=1.5)
    
    for bar, value in zip(bars2, df['Time_Efficiency']):
        ax2.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.002,
                f'{value:.3f}', ha='center', va='bottom', 
                fontsize=14, fontweight='bold')
    
    ax2.set_title('Time Efficiency\n(Accuracy per Second)', fontsize=14, fontweight='normal', color='#4A4A4A')
    ax2.set_ylabel('Efficiency Score', fontsize=12, fontweight='normal', color='#666666')
    ax2.grid(True, alpha=0.2, axis='y', color='#E0E0E0')
    ax2.spines['top'].set_visible(False)
    ax2.spines['right'].set_visible(False)
    ax2.spines['left'].set_color('#E0E0E0')
    ax2.spines['bottom'].set_color('#E0E0E0')

    plt.suptitle('Efficiency Analysis', fontsize=18, fontweight='normal', color='#4A4A4A')
    plt.tight_layout()
    plt.savefig(results_dir / 'efficiency_ratios.png', dpi=300, bbox_inches='tight')
    plt.close()

def create_all_visualizations():
    """创建所有可视化图表"""
    print("📊 Creating Better Visualizations (No Scatter Plots)")
    print("=" * 60)
    
    create_bar_charts()
    print("✅ Bar charts created")
    
    create_radar_chart()
    print("✅ Radar chart created")
    
    create_heatmap()
    print("✅ Heatmap created")
    
    create_efficiency_ratio_chart()
    print("✅ Efficiency ratio charts created")
    
    print("\n📁 Generated files:")
    print("  - token_comparison_bar.png (Simple token comparison)")
    print("  - normalized_comparison_bar.png (Dual-metric comparison)")
    print("  - radar_comparison.png (Multi-dimensional view)")
    print("  - performance_heatmap.png (Relative performance)")
    print("  - efficiency_ratios.png (Efficiency analysis)")
    
    print("\n💡 These charts are much more informative than scatter plots")
    print("   when you have limited data points!")

if __name__ == "__main__":
    create_all_visualizations()
