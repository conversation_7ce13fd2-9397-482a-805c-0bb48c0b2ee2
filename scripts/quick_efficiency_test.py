#!/usr/bin/env python3
"""
Quick Efficiency Test
快速获得真实的效率对比数据
"""

import asyncio
import json
import time
import sys
import os
from typing import List, Dict, Any
import statistics
from pathlib import Path

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.api import async_generate_completion

async def quick_efficiency_comparison():
    """快速效率对比测试"""
    
    # 从不同基准选择代表性问题
    test_problems = [
        {
            'benchmark': 'gsm8k',
            'question': "<PERSON>'s ducks lay 16 eggs per day. She eats 3 for breakfast every morning and bakes muffins for her friends every day with 4. She sells the remainder at the farmers' market daily for $2 per fresh duck egg. How much in dollars does she make every day at the farmers' market?",
            'type': 'math'
        },
        {
            'benchmark': 'math', 
            'question': "Find the value of $x$ such that $\\sqrt{x+7} = 9$.",
            'type': 'math'
        },
        {
            'benchmark': 'drop',
            'question': "The 2010 United States Census reported that Fresno had a population of 494,665. In the census, 245,306 people (49.6%) were White, 40,960 people (8.3%) were African American. How many more White people than African American people were there?",
            'type': 'reading'
        },
        {
            'benchmark': 'humaneval',
            'question': "def has_close_elements(numbers: List[float], threshold: float) -> bool:\n    \"\"\" Check if in given list of numbers, are any two numbers closer to each other than given threshold.\n    >>> has_close_elements([1.0, 2.0, 3.0], 0.5)\n    False\n    >>> has_close_elements([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3)\n    True\n    \"\"\"",
            'type': 'coding'
        },
        {
            'benchmark': 'mmlu',
            'question': "Which of the following is NOT a characteristic of a good hypothesis? (A) It is testable (B) It is falsifiable (C) It is complex (D) It makes predictions",
            'type': 'knowledge'
        }
    ]
    
    agents = ["openai", "gemini", "llama"]
    
    print("🚀 Quick Efficiency Comparison Test")
    print("=" * 50)
    
    dmc_results = []
    dialogue_results = []
    
    for i, problem in enumerate(test_problems):
        print(f"\n📝 Problem {i+1} ({problem['benchmark']})")
        
        # 测试DMC系统
        print("  🤝 Testing DMC...")
        start_time = time.time()
        dmc_tokens = 0
        
        try:
            # Phase 1: 并行draft生成
            draft_tasks = []
            for agent in agents:
                task = async_generate_completion(
                    agent_id=agent,
                    prompt=problem['question'],
                    system_prompt=f"Solve this {problem['type']} problem clearly and concisely.",
                    temperature=0.3
                )
                draft_tasks.append(task)
            
            draft_responses = await asyncio.gather(*draft_tasks, return_exceptions=True)
            valid_drafts = [r for r in draft_responses if not isinstance(r, Exception)]
            
            if valid_drafts:
                best_draft = max(valid_drafts, key=len)
                
                # 计算draft tokens
                for response in valid_drafts:
                    dmc_tokens += len(problem['question'].split()) * 1.3 + len(str(response).split()) * 1.3
                
                # Phase 2: 并行注释 (简化版)
                annotation_tasks = []
                for agent in agents:
                    annotation_prompt = f"Solution: {best_draft}\n\nProvide brief feedback (max 100 tokens):"
                    
                    task = async_generate_completion(
                        agent_id=agent,
                        prompt=annotation_prompt,
                        system_prompt="Provide concise feedback.",
                        temperature=0.3
                    )
                    annotation_tasks.append(task)
                
                annotations = await asyncio.gather(*annotation_tasks, return_exceptions=True)
                
                # 计算annotation tokens
                for annotation in annotations:
                    if not isinstance(annotation, Exception):
                        bounded_annotation = " ".join(str(annotation).split()[:100])
                        dmc_tokens += len(annotation_prompt.split()) * 1.3 + len(bounded_annotation.split()) * 1.3
        
        except Exception as e:
            print(f"    ❌ DMC Error: {e}")
            continue
        
        dmc_time = time.time() - start_time
        dmc_results.append({
            'benchmark': problem['benchmark'],
            'tokens': dmc_tokens,
            'time': dmc_time
        })
        
        print(f"    ✅ DMC: {dmc_time:.2f}s, {dmc_tokens:.0f} tokens")
        
        # 测试对话系统
        print("  🗣️  Testing Dialogue...")
        start_time = time.time()
        dialogue_tokens = 0
        
        try:
            conversation = [f"Problem: {problem['question']}"]
            
            # 2轮对话
            for round_num in range(2):
                for agent_idx, agent in enumerate(agents):
                    full_context = "\n".join(conversation)
                    
                    prompt = f"Previous discussion:\n{full_context}\n\nAs Agent {agent_idx+1}, provide your analysis:"
                    
                    response = await async_generate_completion(
                        agent_id=agent,
                        prompt=prompt,
                        system_prompt=f"Participate in multi-agent discussion about {problem['type']} problems.",
                        temperature=0.3
                    )
                    
                    conversation.append(f"Agent {agent_idx+1}: {response}")
                    
                    # 计算tokens
                    dialogue_tokens += len(prompt.split()) * 1.3 + len(str(response).split()) * 1.3
        
        except Exception as e:
            print(f"    ❌ Dialogue Error: {e}")
            continue
        
        dialogue_time = time.time() - start_time
        dialogue_results.append({
            'benchmark': problem['benchmark'],
            'tokens': dialogue_tokens,
            'time': dialogue_time
        })
        
        print(f"    ✅ Dialogue: {dialogue_time:.2f}s, {dialogue_tokens:.0f} tokens")
        
        # 计算该问题的效率对比
        if dmc_tokens > 0 and dialogue_tokens > 0:
            token_reduction = (dialogue_tokens - dmc_tokens) / dialogue_tokens * 100
            time_reduction = (dialogue_time - dmc_time) / dialogue_time * 100
            print(f"    💰 Efficiency: {token_reduction:.1f}% token reduction, {time_reduction:.1f}% time reduction")
    
    # 计算总体效率
    if dmc_results and dialogue_results:
        avg_dmc_tokens = statistics.mean([r['tokens'] for r in dmc_results])
        avg_dmc_time = statistics.mean([r['time'] for r in dmc_results])
        avg_dialogue_tokens = statistics.mean([r['tokens'] for r in dialogue_results])
        avg_dialogue_time = statistics.mean([r['time'] for r in dialogue_results])
        
        overall_token_reduction = (avg_dialogue_tokens - avg_dmc_tokens) / avg_dialogue_tokens * 100
        overall_time_reduction = (avg_dialogue_time - avg_dmc_time) / avg_dialogue_time * 100
        
        print("\n🎯 OVERALL EFFICIENCY RESULTS")
        print("=" * 50)
        print(f"DMC Average: {avg_dmc_tokens:.0f} tokens, {avg_dmc_time:.2f}s")
        print(f"Dialogue Average: {avg_dialogue_tokens:.0f} tokens, {avg_dialogue_time:.2f}s")
        print(f"Overall Token Reduction: {overall_token_reduction:.1f}%")
        print(f"Overall Time Reduction: {overall_time_reduction:.1f}%")
        
        # 保存结果
        results = {
            'timestamp': time.time(),
            'summary': {
                'token_reduction_pct': overall_token_reduction,
                'time_reduction_pct': overall_time_reduction,
                'dmc_avg_tokens': avg_dmc_tokens,
                'dialogue_avg_tokens': avg_dialogue_tokens,
                'dmc_avg_time': avg_dmc_time,
                'dialogue_avg_time': avg_dialogue_time
            },
            'dmc_results': dmc_results,
            'dialogue_results': dialogue_results,
            'test_problems': test_problems
        }
        
        output_file = Path("results/quick_efficiency_results.json")
        output_file.parent.mkdir(exist_ok=True)
        
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n💾 Results saved to: {output_file}")
        
        # 检查是否接近摘要中的数据
        print(f"\n📊 Comparison with Abstract Claims:")
        print(f"Abstract claims: 61% token reduction, 88% time reduction")
        print(f"Our test shows: {overall_token_reduction:.1f}% token reduction, {overall_time_reduction:.1f}% time reduction")
        
        if abs(overall_token_reduction - 61) < 10:
            print("✅ Token reduction is close to abstract claim")
        else:
            print("⚠️  Token reduction differs from abstract claim")
            
        if abs(overall_time_reduction - 88) < 10:
            print("✅ Time reduction is close to abstract claim")
        else:
            print("⚠️  Time reduction differs from abstract claim")
        
        return results
    
    else:
        print("❌ Insufficient data for comparison")
        return None

if __name__ == "__main__":
    asyncio.run(quick_efficiency_comparison())
