#!/usr/bin/env python3
"""
CoT Comparison Test
测试Chain-of-Thought方法的token消耗，与DMC对比
"""

import asyncio
import json
import time
import sys
import os
from typing import List, Dict, Any
import statistics
from pathlib import Path

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.api import async_generate_completion
from utils.token_utils import TokenCounter

class CoTComparisonTest:
    """CoT对比测试"""
    
    def __init__(self):
        self.agents = ["openai", "gemini", "llama"]
        self.token_counter = TokenCounter()
    
    async def test_single_agent_cot(self, question: str, problem_type: str = "math"):
        """测试单Agent CoT"""
        
        print(f"🧠 Testing Single-Agent CoT on: {question[:50]}...")
        total_tokens = 0
        start_time = time.time()
        
        cot_prompt = f"""Solve this {problem_type} problem step by step. Think through each step carefully and show your reasoning.

Problem: {question}

Let me think step by step:"""
        
        try:
            response = await async_generate_completion(
                agent_id="openai",
                prompt=cot_prompt,
                system_prompt="You are an expert problem solver. Think step by step and show your reasoning clearly.",
                temperature=0.3
            )
            
            # 计算tokens
            input_tokens = self.token_counter.count_tokens(cot_prompt)
            output_tokens = self.token_counter.count_tokens(response)
            total_tokens = input_tokens + output_tokens
            
            duration = time.time() - start_time
            
            print(f"    ✅ Single CoT: {duration:.2f}s, {total_tokens} tokens ({input_tokens} in + {output_tokens} out)")
            return response, total_tokens, duration
            
        except Exception as e:
            print(f"    ❌ Single CoT failed: {e}")
            return None, 0, 0
    
    async def test_multi_agent_cot(self, question: str, problem_type: str = "math"):
        """测试多Agent CoT (每个agent独立思考)"""
        
        print(f"🧠🧠🧠 Testing Multi-Agent CoT on: {question[:50]}...")
        total_tokens = 0
        start_time = time.time()
        
        cot_responses = []
        
        for agent in self.agents:
            try:
                cot_prompt = f"""Solve this {problem_type} problem step by step. Think through each step carefully and show your reasoning.

Problem: {question}

Let me think step by step:"""
                
                response = await async_generate_completion(
                    agent_id=agent,
                    prompt=cot_prompt,
                    system_prompt=f"You are a {agent} expert. Think step by step and show your reasoning clearly.",
                    temperature=0.3
                )
                
                cot_responses.append(response)
                
                # 计算tokens
                input_tokens = self.token_counter.count_tokens(cot_prompt)
                output_tokens = self.token_counter.count_tokens(response)
                agent_tokens = input_tokens + output_tokens
                total_tokens += agent_tokens
                
                print(f"    {agent}: {agent_tokens} tokens ({input_tokens} in + {output_tokens} out)")
                
            except Exception as e:
                print(f"    {agent} failed: {e}")
                continue
        
        duration = time.time() - start_time
        
        print(f"    ✅ Multi-Agent CoT: {duration:.2f}s, {total_tokens} tokens total")
        return cot_responses, total_tokens, duration
    
    async def test_cot_self_consistency(self, question: str, problem_type: str = "math", num_samples: int = 3):
        """测试CoT Self-Consistency (多次采样)"""
        
        print(f"🧠🔄 Testing CoT Self-Consistency on: {question[:50]}...")
        total_tokens = 0
        start_time = time.time()
        
        cot_prompt = f"""Solve this {problem_type} problem step by step. Think through each step carefully and show your reasoning.

Problem: {question}

Let me think step by step:"""
        
        responses = []
        
        for i in range(num_samples):
            try:
                response = await async_generate_completion(
                    agent_id="openai",
                    prompt=cot_prompt,
                    system_prompt="You are an expert problem solver. Think step by step and show your reasoning clearly.",
                    temperature=0.7  # 更高温度获得多样性
                )
                
                responses.append(response)
                
                # 计算tokens
                input_tokens = self.token_counter.count_tokens(cot_prompt)
                output_tokens = self.token_counter.count_tokens(response)
                sample_tokens = input_tokens + output_tokens
                total_tokens += sample_tokens
                
                print(f"    Sample {i+1}: {sample_tokens} tokens ({input_tokens} in + {output_tokens} out)")
                
            except Exception as e:
                print(f"    Sample {i+1} failed: {e}")
                continue
        
        # 一致性判断 (简化版)
        if len(responses) > 1:
            consistency_prompt = f"""Here are {len(responses)} different solutions to the same problem:

Problem: {question}

Solutions:
""" + "\n\n".join([f"Solution {i+1}: {resp}" for i, resp in enumerate(responses)]) + """

Determine the most consistent and correct answer:"""
            
            try:
                final_answer = await async_generate_completion(
                    agent_id="openai",
                    prompt=consistency_prompt,
                    system_prompt="Choose the most consistent and correct solution.",
                    temperature=0.3
                )
                
                # 计算一致性判断的tokens
                input_tokens = self.token_counter.count_tokens(consistency_prompt)
                output_tokens = self.token_counter.count_tokens(final_answer)
                consistency_tokens = input_tokens + output_tokens
                total_tokens += consistency_tokens
                
                print(f"    Consistency check: {consistency_tokens} tokens ({input_tokens} in + {output_tokens} out)")
                
            except Exception as e:
                print(f"    Consistency check failed: {e}")
        
        duration = time.time() - start_time
        
        print(f"    ✅ CoT Self-Consistency: {duration:.2f}s, {total_tokens} tokens total")
        return responses, total_tokens, duration
    
    async def test_dmc_complete(self, question: str, problem_type: str = "math"):
        """完整DMC测试 (4个完整阶段)"""

        print(f"🤝 Testing Complete DMC on: {question[:50]}...")
        total_tokens = 0
        start_time = time.time()

        # Phase 1: Draft Generation
        print(f"    Phase 1: Draft Generation")
        draft_tasks = []
        for agent in self.agents:
            task = async_generate_completion(
                agent_id=agent,
                prompt=question,
                system_prompt=f"Solve this {problem_type} problem concisely.",
                temperature=0.3
            )
            draft_tasks.append(task)

        draft_responses = await asyncio.gather(*draft_tasks, return_exceptions=True)
        valid_drafts = [r for r in draft_responses if not isinstance(r, Exception)]

        if not valid_drafts:
            return None, 0, 0

        best_draft = max(valid_drafts, key=len)

        # 计算draft tokens (真实计算)
        for response in valid_drafts:
            input_tokens = self.token_counter.count_tokens(question)
            output_tokens = self.token_counter.count_tokens(str(response))
            total_tokens += input_tokens + output_tokens

        print(f"      Draft tokens: {total_tokens}")

        # Phase 2: Annotation
        print(f"    Phase 2: Annotation")
        annotation_tasks = []
        annotations_data = []

        for agent in self.agents:
            annotation_prompt = f"Solution: {best_draft}\n\nBrief feedback (max 100 tokens):"

            task = async_generate_completion(
                agent_id=agent,
                prompt=annotation_prompt,
                system_prompt="Provide brief, focused feedback.",
                temperature=0.3
            )
            annotation_tasks.append((agent, task, annotation_prompt))

        phase2_tokens = 0
        for agent, task, prompt in annotation_tasks:
            try:
                annotation = await task
                bounded_annotation = " ".join(str(annotation).split()[:100])
                annotations_data.append(bounded_annotation)

                input_tokens = self.token_counter.count_tokens(prompt)
                output_tokens = self.token_counter.count_tokens(bounded_annotation)
                phase2_tokens += input_tokens + output_tokens

            except Exception as e:
                print(f"      {agent} annotation failed: {e}")
                continue

        total_tokens += phase2_tokens
        print(f"      Annotation tokens: {phase2_tokens}")

        # Phase 3: Merge Process
        print(f"    Phase 3: Merge")
        if annotations_data:
            annotations_text = "\n".join([f"Feedback {i+1}: {ann}" for i, ann in enumerate(annotations_data)])

            merge_prompt = f"""Original solution: {best_draft}

Agent feedback:
{annotations_text}

Integrate the feedback to create an improved solution:"""

            try:
                merged_solution = await async_generate_completion(
                    agent_id="openai",
                    prompt=merge_prompt,
                    system_prompt="Merge the solution with feedback intelligently.",
                    temperature=0.3
                )

                input_tokens = self.token_counter.count_tokens(merge_prompt)
                output_tokens = self.token_counter.count_tokens(merged_solution)
                phase3_tokens = input_tokens + output_tokens
                total_tokens += phase3_tokens

                print(f"      Merge tokens: {phase3_tokens}")

            except Exception as e:
                print(f"      Merge failed: {e}")
                merged_solution = best_draft
                phase3_tokens = 0
        else:
            merged_solution = best_draft
            phase3_tokens = 0

        # Phase 4: Leader Evaluation
        print(f"    Phase 4: Leader Evaluation")
        evaluation_prompt = f"""Evaluate this solution:

Problem: {question}
Solution: {merged_solution}

Provide quality score (0.0-1.0) and brief assessment:"""

        try:
            evaluation = await async_generate_completion(
                agent_id="gemini",
                prompt=evaluation_prompt,
                system_prompt="Evaluate solution quality objectively.",
                temperature=0.3
            )

            input_tokens = self.token_counter.count_tokens(evaluation_prompt)
            output_tokens = self.token_counter.count_tokens(evaluation)
            phase4_tokens = input_tokens + output_tokens
            total_tokens += phase4_tokens

            print(f"      Evaluation tokens: {phase4_tokens}")

        except Exception as e:
            print(f"      Evaluation failed: {e}")
            phase4_tokens = 0

        duration = time.time() - start_time

        print(f"    ✅ Complete DMC: {duration:.2f}s, {total_tokens} tokens")
        return merged_solution, total_tokens, duration

async def run_cot_comparison():
    """运行CoT对比测试"""
    
    test_problems = [
        {
            'question': "Janet's ducks lay 16 eggs per day. She eats 3 for breakfast and bakes muffins with 4. She sells the remainder for $2 each. How much does she make daily?",
            'type': 'math'
        },
        {
            'question': "Find the value of x such that sqrt(x+7) = 9.",
            'type': 'math'
        },
        {
            'question': "If a train travels 120 miles in 2 hours, what is its average speed?",
            'type': 'math'
        }
    ]
    
    tester = CoTComparisonTest()
    
    print("🚀 CoT METHODS COMPARISON")
    print("=" * 80)
    
    results = {
        'single_cot': [],
        'multi_cot': [],
        'cot_sc': [],
        'dmc': []
    }
    
    for i, problem in enumerate(test_problems):
        print(f"\n📝 Problem {i+1}: {problem['question'][:60]}...")
        print("-" * 60)
        
        # 测试Single-Agent CoT
        single_solution, single_tokens, single_time = await tester.test_single_agent_cot(
            problem['question'], problem['type']
        )
        
        if single_solution:
            results['single_cot'].append({
                'tokens': single_tokens,
                'time': single_time
            })
        
        # 测试Multi-Agent CoT
        multi_solutions, multi_tokens, multi_time = await tester.test_multi_agent_cot(
            problem['question'], problem['type']
        )
        
        if multi_solutions:
            results['multi_cot'].append({
                'tokens': multi_tokens,
                'time': multi_time
            })
        
        # 测试CoT Self-Consistency
        sc_solutions, sc_tokens, sc_time = await tester.test_cot_self_consistency(
            problem['question'], problem['type'], num_samples=3
        )
        
        if sc_solutions:
            results['cot_sc'].append({
                'tokens': sc_tokens,
                'time': sc_time
            })
        
        # 测试DMC (完整流程)
        dmc_solution, dmc_tokens, dmc_time = await tester.test_dmc_complete(
            problem['question'], problem['type']
        )
        
        if dmc_solution:
            results['dmc'].append({
                'tokens': dmc_tokens,
                'time': dmc_time
            })
    
    # 分析结果
    print(f"\n📊 OVERALL COMPARISON")
    print("=" * 80)
    
    method_stats = {}
    for method_name, method_results in results.items():
        if method_results:
            avg_tokens = statistics.mean([r['tokens'] for r in method_results])
            avg_time = statistics.mean([r['time'] for r in method_results])
            method_stats[method_name] = {
                'avg_tokens': avg_tokens,
                'avg_time': avg_time
            }
            print(f"{method_name.upper()}: {avg_tokens:.0f} tokens, {avg_time:.2f}s average")
    
    # 计算DMC相对于各CoT方法的效率
    if 'dmc' in method_stats:
        dmc_tokens = method_stats['dmc']['avg_tokens']
        dmc_time = method_stats['dmc']['avg_time']
        
        print(f"\nDMC Efficiency vs CoT Methods:")
        for method_name, stats in method_stats.items():
            if method_name != 'dmc':
                token_reduction = (stats['avg_tokens'] - dmc_tokens) / stats['avg_tokens'] * 100
                time_reduction = (stats['avg_time'] - dmc_time) / stats['avg_time'] * 100
                print(f"  vs {method_name}: {token_reduction:.1f}% token reduction, {time_reduction:.1f}% time reduction")
    
    # 保存结果
    output_file = Path("results/cot_comparison_results.json")
    output_file.parent.mkdir(exist_ok=True)
    
    summary_results = {
        'timestamp': time.time(),
        'method_stats': method_stats,
        'detailed_results': results
    }
    
    with open(output_file, 'w') as f:
        json.dump(summary_results, f, indent=2)
    
    print(f"\n💾 Results saved to: {output_file}")
    
    return summary_results

if __name__ == "__main__":
    asyncio.run(run_cot_comparison())
