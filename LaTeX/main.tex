%File: anonymous-submission-latex-2026.tex
\documentclass[letterpaper]{article} % DO NOT CHANGE THIS
\usepackage[submission]{aaai2026}  % DO NOT CHANGE THIS
\usepackage{times}  % DO NOT CHANGE THIS
\usepackage{helvet}  % DO NOT CHANGE THIS
\usepackage{courier}  % DO NOT CHANGE THIS
\usepackage[hyphens]{url}  % DO NOT CHANGE THIS
\usepackage{graphicx} % DO NOT CHANGE THIS
\urlstyle{rm} % DO NOT CHANGE THIS
\def\UrlFont{\rm}  % DO NOT CHANGE THIS
\usepackage{natbib}  % DO NOT CHANGE THIS AND DO NOT ADD ANY OPTIONS TO IT
\usepackage{caption} % DO NOT CHANGE THIS AND DO NOT ADD ANY OPTIONS TO IT
\frenchspacing  % DO NOT CHANGE THIS
\setlength{\pdfpagewidth}{8.5in} % DO NOT CHANGE THIS
\setlength{\pdfpageheight}{11in} % DO NOT CHANGE THIS
%
% These are recommended to typeset algorithms but not required. See the subsubsection on algorithms. Remove them if you don't have algorithms in your paper.
\usepackage{algorithm}
\usepackage{algorithmic}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{amsthm}
\newtheorem{lemma}{Lemma}
\newtheorem{theorem}{Theorem}

%
% These are are recommended to typeset listings but not required. See the subsubsection on listing. Remove this block if you don't have listings in your paper.
\usepackage{newfloat}
\usepackage{listings}
\DeclareCaptionStyle{ruled}{labelfont=normalfont,labelsep=colon,strut=off} % DO NOT CHANGE THIS
\lstset{%
	basicstyle={\footnotesize\ttfamily},% footnotesize acceptable for monospace
	numbers=left,numberstyle=\footnotesize,xleftmargin=2em,% show line numbers, remove this entire line if you don't want the numbers.
	aboveskip=0pt,belowskip=0pt,%
	showstringspaces=false,tabsize=2,breaklines=true}
\floatstyle{ruled}
\newfloat{listing}{tb}{lst}{}
\floatname{listing}{Listing}
%
% Keep the \pdfinfo as shown here. There's no need
% for you to add the /Title and /Author tags.
\pdfinfo{
/TemplateVersion (2026.1)
}

% DISALLOWED PACKAGES
% \usepackage{authblk} -- This package is specifically forbidden
% \usepackage{balance} -- This package is specifically forbidden
% \usepackage{color (if used in text)
% \usepackage{CJK} -- This package is specifically forbidden
% \usepackage{float} -- This package is specifically forbidden
% \usepackage{flushend} -- This package is specifically forbidden
% \usepackage{fontenc} -- This package is specifically forbidden
% \usepackage{fullpage} -- This package is specifically forbidden
% \usepackage{geometry} -- This package is specifically forbidden
% \usepackage{grffile} -- This package is specifically forbidden
% \usepackage{hyperref} -- This package is specifically forbidden
% \usepackage{navigator} -- This package is specifically forbidden
% (or any other package that embeds links such as navigator or hyperref)
% \indentfirst} -- This package is specifically forbidden
% \layout} -- This package is specifically forbidden
% \multicol} -- This package is specifically forbidden
% \nameref} -- This package is specifically forbidden
% \usepackage{savetrees} -- This package is specifically forbidden
% \usepackage{setspace} -- This package is specifically forbidden
% \usepackage{stfloats} -- This package is specifically forbidden
% \usepackage{tabu} -- This package is specifically forbidden
% \usepackage{titlesec} -- This package is specifically forbidden
% \usepackage{tocbibind} -- This package is specifically forbidden
% \usepackage{ulem} -- This package is specifically forbidden
% \usepackage{wrapfig} -- This package is specifically forbidden
% DISALLOWED COMMANDS
% \nocopyright -- Your paper will not be published if you use this command
% \addtolength -- This command may not be used
% \balance -- This command may not be used
% \baselinestretch -- Your paper will not be published if you use this command
% \clearpage -- No page breaks of any kind may be used for the final version of your paper
% \columnsep -- This command may not be used
% \newpage -- No page breaks of any kind may be used for the final version of your paper
% \pagebreak -- No page breaks of any kind may be used for the final version of your paperr
% \pagestyle -- This command may not be used
% \tiny -- This is not an acceptable font size.
% \vspace{- -- No negative value may be used in proximity of a caption, figure, table, section, subsection, subsubsection, or reference
% \vskip{- -- No negative value may be used to alter spacing above or below a caption, figure, table, section, subsection, subsubsection, or reference

\setcounter{secnumdepth}{0} %May be changed to 1 or 2 if section numbers are desired.

% The file aaai2026.sty is the style file for AAAI Press
% proceedings, working notes, and technical reports.
%

% Title
\title{Draft-Mediated Collaboration: A Context Engineering Approach for Multi-Agent LLM Systems}
\author{
    Anonymous Submission
}
\affiliations{
    Anonymous Institution
}

\begin{document}

\maketitle

% --------- Polished Abstract ---------
\begin{abstract}
Multi-agent LLM systems that rely on open-ended dialogue suffer from context explosion, topic drift, and high coordination cost. We present Draft-Mediated Collaboration (DMC), an artifact-centric protocol in which agents annotate a shared draft instead of exchanging unbounded messages. DMC enforces schema-constrained edits and a coordinator–merge loop that bounds per-round communication to O(N + A·L) and yields total complexity O(R(N + A·L)). Across nine reasoning benchmarks, DMC reduces input tokens by up to 78\% and wall-clock latency by up to 81\% vs. dialogue-based systems, while matching or surpassing strong baselines (e.g., 98\% on GSM8K, 100\% on MBPP). Ablations show that disciplined context control—rather than additional dialogue—drives the gains, and theory explains the linear scaling in agent count. These results suggest that the most effective multi-agent collaboration is not the most talkative: structuring interaction around drafts yields predictable resource use and strong accuracy.

\textbf{Key Efficiency Insights:} Our single-round analysis reveals three critical findings: (1) \textit{Bounded communication prevents exponential growth}—DMC's 150-token annotation limit achieves 95.8\% token reduction versus unbounded conversation; (2) \textit{Structured collaboration outperforms sampling}—DMC's four-phase pipeline is 29.6\% more efficient than CoT self-consistency while providing multi-perspective reasoning; (3) \textit{Artifact-centric design scales linearly}—DMC's token complexity grows as O(N + A·L) compared to quadratic growth in dialogue-based systems, where context accumulates across rounds.
\end{abstract}

% --------- Introduction (already polished) ---------
\section{Introduction}

The capacity of Large Language Models (LLMs) to act as autonomous agents has unlocked new possibilities for solving complex, multi-faceted problems \cite{brown2020language, ouyang2022training}. A natural evolution is to form teams of LLM agents, hoping to harness collective intelligence. However, the predominant collaboration paradigm—direct, open-ended dialogue—faces critical scalability challenges that undermine its potential. As agents converse, the shared context expands uncontrollably, leading to four fundamental challenges: 1)~\textbf{Context Explosion}, where history quickly exceeds model limits; 2)~\textbf{Topic Drift}, where conversations lose focus; 3)~\textbf{Communication Overhead}, with complexity scaling with dialogue length; and 4)~\textbf{Information Dilution}, where key insights are buried in verbose exchanges.

Existing approaches offer an unsatisfactory trade-off. On one hand, dialogue-based systems like multi-agent debate \cite{du2023improving, chen2023multi} demonstrate the power of multiple perspectives but suffer from communication challenges and coordination difficulties. Recent empirical analysis reveals the severity of this problem: Cemri et al.~\cite{cemri2025multi} found that popular multi-agent frameworks exhibit failure rates as high as 75\%, with many failures stemming from inter-agent communication breakdowns rather than individual agent limitations. Their systematic study of 14 failure modes across 150+ conversation traces demonstrates that current multi-agent systems suffer from fundamental design flaws in how agents coordinate and communicate.

Much of the recent work on LLM reasoning, such as Chain-of-Thought~\cite{wei2022chain} and its more complex variants like Tree-of-Thoughts~\cite{yao2024tree}, has focused on structuring the internal thought process of a *single agent*. However, when multiple agents collaborate, the bottleneck shifts from individual reasoning to inter-agent communication. We argue that optimizing the communication protocol is as critical as optimizing the reasoning topology. Just as an effective human team relies on a concise, shared document (like a meeting minute or a project draft) rather than verbose, unstructured discussion, we posit that multi-agent LLM systems can achieve better coordination by shifting their interaction model from dialogue to a shared artifact.

To bridge this gap, we propose a fundamentally different approach: \textbf{Draft-Mediated Collaboration (DMC)}. Instead of agents communicating directly *with each other*, they interact indirectly *through a shared draft*. This draft serves as a centralized, structured artifact that embodies the current state of the solution. Communication shifts from verbose natural language exchanges to concise, targeted operations on the draft, such as proposing edits, adding annotations, or verifying facts. This paradigm inherently enforces focus, structures interaction, and contains context growth.

Our work makes the following contributions, implemented and validated in our accompanying open-source framework:

\textbf{A Draft-Mediated Collaboration Protocol:} We formalize and implement a novel protocol where a shared draft, not dialogue, is the central medium of collaboration, fundamentally altering the dynamics of multi-agent interaction.

\textbf{Efficient Specialised Agent Roles:} We design a lean set of agents—including Workers, a Merger, and a Leader—each with a specialised function that contributes to the draft's evolution without redundant communication. This is orchestrated by a central \textbf{Controller}, as depicted in Figure~\ref{fig:architecture}.

\textbf{Structured Annotation Protocol:} We formalize a bounded annotation system that transforms verbose multi-agent dialogue into concise, targeted feedback operations, fundamentally changing how agents communicate and coordinate.

\textbf{Collaboration Efficiency Analysis:} We demonstrate that draft-mediated collaboration achieves linear scaling compared to quadratic growth in dialogue-based schemes, with empirical validation across diverse benchmarks.

\textbf{Systematic Failure Mode Prevention:} We develop a principled framework that structurally prevents common multi-agent system failures through artifact-centric design, addressing critical failure modes identified in recent taxonomies while maintaining coordination effectiveness.

\textbf{Annotation Quality and Consensus Analysis:} We provide empirical analysis of annotation quality patterns, consensus formation dynamics, and their relationship to final solution quality, revealing insights into effective multi-agent coordination mechanisms.

\textbf{Comprehensive Empirical Validation:} We conduct extensive experiments on nine diverse benchmarks, demonstrating that DMC achieves strong performance (including 98.0\% on GSM8K, 100\% on MBPP) with structured communication patterns, often outperforming prominent baselines (including CoT, CoD, and LLM-Debate).

This work establishes that for complex problem-solving, the most effective multi-agent collaboration is not the most loquacious. By structuring interaction around a shared artifact, DMC provides a scalable path forward for multi-agent LLM systems.

% Figure for system architecture
\begin{figure}[t]
\centering
% --- Mermaid diagram placeholder ---
% The diagram generated previously should be converted to a PDF 
% and included here using \includegraphics
\fbox{
    \parbox{0.9\columnwidth}{
        \centering
        \vspace{4cm}
        \textbf{Figure 1: System Architecture Diagram} \\
        \textit{(Placeholder for the Mermaid diagram showing the Controller, Worker, Merger, and Leader agent interactions in the iterative draft refinement loop.)}
        \vspace{4cm}
    }
}
\caption{The Draft-Mediated Collaboration (DMC) framework. The Controller orchestrates an iterative loop where Worker agents generate and annotate a shared draft. The Merger synthesizes feedback, and the Leader evaluates quality for iterative refinement.}
\label{fig:architecture}
\end{figure}

% Communication Complexity Comparison
\begin{figure}[t]
\centering
\fbox{\parbox{0.9\columnwidth}{\vspace{2cm}\centering Communication complexity comparison: DMC vs Dialogue-based systems\vspace{2cm}}}
\caption{Communication complexity scaling: DMC maintains O(A) complexity while dialogue-based systems exhibit O(A²) growth with agent count.}
\label{fig:complexity}
\end{figure}

% Performance vs Communication Trade-off
\begin{figure}[t]
\centering
\fbox{\parbox{0.9\columnwidth}{\vspace{2cm}\centering Performance-Communication trade-off analysis across different agent counts\vspace{2cm}}}
\caption{Performance-communication trade-off analysis: DMC achieves competitive accuracy across diverse tasks while maintaining bounded token consumption through structured communication patterns.}
\label{fig:tradeoff}
\end{figure}

\section{Related Work}

\textbf{Context Engineering in LLMs:} Recent advances have focused on leveraging longer contexts effectively \cite{brown2020language}. However, longer contexts introduce challenges including attention dilution and difficulty maintaining focus on specific tasks. Our work addresses these challenges specifically in multi-agent settings.

\textbf{Multi-Agent LLM Collaboration:} Existing approaches typically involve direct agent communication through extended dialogues \cite{chen2023multi,du2023improving}. While these methods can improve reasoning quality, they suffer from context explosion and coordination challenges. Chen et al. \cite{chen2023multi} demonstrated multi-agent debate effectiveness but acknowledged the communication overhead of lengthy exchanges.

Recent work by Cemri et al. \cite{cemri2025multi} provides crucial insights into why multi-agent systems fail, identifying 14 distinct failure modes organized into three categories: specification and system design failures, inter-agent misalignment, and task verification and termination issues. Their Multi-Agent System Failure Taxonomy (MASFT) reveals that many failures stem from inter-agent communication challenges rather than individual agent limitations, with failure rates as high as 75\% in popular frameworks like ChatDev. This systematic analysis underscores the critical need for structured communication protocols that can prevent common failure patterns such as conversation reset, task derailment, and incomplete verification.

Recent variants further explore structured reasoning topologies, including Tree\nobreakdash-of\nobreakdash-Thoughts~\cite{yao2024tree} and Graph\nobreakdash-of\nobreakdash-Thoughts~\cite{besta2024graph}. Automated workflow discovery like AFLOW~\cite{zhang2024aflow} and decentralised agent collectives such as GPT\nobreakdash-Swarm~\cite{frendo2023gptswarm} offer orthogonal perspectives on orchestrating multiple LLM calls.

\textbf{Structured Communication in AI:} Classical multi-agent systems \cite{stone2000multiagent,wooldridge2009introduction} have explored structured communication protocols, but these approaches were designed for symbolic reasoning rather than natural language collaboration. Our work bridges structured communication principles with modern LLM capabilities.

\textbf{Structured Communication in Multi-Agent Systems:} Recent work has emphasized the importance of structured communication in multi-agent applications. Our draft-mediated approach directly addresses coordination challenges while maintaining collaborative benefits.

% ---------- NEW SECTION: Problem Definition ----------
\section{Problem Definition}
We formalize multi-agent collaboration as an iterative draft refinement process. Let $q$ denote a task prompt and $A=\{a_1,\dots,a_{|A|}\}$ the set of LLM agents. The system maintains a shared draft $d^{(r)}$ at round $r$, which evolves through structured phases:

\begin{enumerate}
    \item \textbf{Draft Generation}: Workers create initial solution candidates in parallel
    \item \textbf{Annotation}: Each agent produces structured feedback $\alpha_i^{(r)}$ with bounded length
    \item \textbf{Adaptive Merge}: An intelligent merger synthesizes annotations into $d^{(r+1)}$
    \item \textbf{Quality Assessment}: A leader evaluates draft quality and decides continuation
\end{enumerate}

The collaboration terminates when quality score $\mathcal{Q}(d^{(r)}) \geq \tau_t$ for task type $t$, or after reaching maximum rounds $R_{\max}$. The objective maximizes answer accuracy while maintaining communication efficiency:
\[
\max_{A,\mathcal{M},\mathcal{Q}} \mathbb{E}_{q \sim \mathcal{D}}[\text{Acc}(d^{(\leq R)}, q)] \quad \text{s.t. communication overhead is bounded}
\]

Our framework employs task-adaptive parameters: annotation limits $L_t \in [100, 150]$ tokens and quality thresholds $\tau_t \in [0.65, 0.95]$ vary by task complexity. This structured approach contrasts with dialogue-based schemes where communication grows quadratically with agents and rounds.

\section{Method}

Our draft-mediated collaboration framework addresses context engineering challenges through structured, indirect agent communication. Instead of lengthy multi-agent dialogues, agents collaborate around concise drafts using targeted annotations and intelligent synthesis.

\subsection{Conceptual Framework}

Our approach fundamentally differs from dialogue-based multi-agent systems by using shared drafts as communication intermediates. Instead of agents exchanging unbounded messages, they collaborate through bounded annotations on evolving drafts. This shift from dialogue sequences to artifact evolution enables predictable resource consumption and structured coordination patterns.

\subsection{Draft-Mediated Collaboration Architecture}

\subsubsection{Core Design Principles}
Our framework is built on three key principles that address context engineering challenges:

\textbf{Context Containment}: Each collaboration phase operates within bounded context limits, preventing exponential growth typical in dialogue-based approaches.

\textbf{Information Density}: Communication focuses on specific, actionable content rather than verbose explanations, maximizing information value.

\textbf{Structured Interaction}: Agents interact through predefined formats (drafts, annotations, evaluations) rather than open-ended conversations, maintaining focus and clarity.

\subsubsection{Draft as Communication Medium}
The central innovation is using concise drafts as communication intermediates. Unlike traditional approaches where agents directly exchange lengthy messages, our agents collaborate through a structured three-phase process. First, agents create focused drafts where initial solutions are generated concisely while maintaining both clarity and completeness. Second, agents provide targeted annotations by suggesting improvements through specific, bounded feedback rather than lengthy explanations. Third, this approach enables indirect collaboration where agents see each other's work through the shared draft rather than through direct communication channels.

This design eliminates the context explosion problem while preserving the collaborative benefits of multiple perspectives.

\subsection{Efficient Agent Specialization}

\subsubsection{Role-Based Collaboration}
To maximize coordination while maintaining quality, each agent is assigned a well-defined responsibility. The \textit{Domain Expert} contributes task-specific knowledge with focused input, the \textit{Quality Assessor} verifies completeness and correctness through structured criteria, the \textit{Fact Checker} performs targeted verification of key statements, and the \textit{Editor} polishes clarity and presentation. Clear boundaries among these roles prevent scope creep and keep the collaborative focus sharp.

\subsubsection{Structured Annotation System}
Instead of verbose free-form discussions, our annotation system enforces a concise feedback protocol: every annotation pinpoints the exact text span to revise, labels the suggestion category (accuracy, clarity, or completeness), assigns a priority level, and maintains brevity. This structure concentrates the agents’ attention on high-impact edits while keeping context growth under control.

\subsection{Intelligent Synthesis Without Dialogue}

\subsubsection{LLM-Driven Semantic Merging}
The Merger agent integrates content without the overhead of extended debates. It performs semantic analysis to understand relationships among solution components, detects contradictions, resolves conflicts through reasoning, and finally synthesises a coherent answer that combines multiple perspectives.

\subsubsection{Context-Aware Quality Assessment}
The Leader agent applies multi-dimensional scoring—covering accuracy, completeness, and clarity—then returns focused feedback. This targeted guidance steers subsequent refinement rounds without triggering a full re-discussion.

\subsection{Adaptive Collaboration Intensity}

\subsubsection{Task-Complexity-Based Scaling}
Our framework automatically adjusts collaboration depth based on task requirements, optimizing the coordination-quality tradeoff:

\textbf{Minimal Collaboration} (Simple tasks): Single-round annotation with basic quality checks.

\textbf{Standard Collaboration} (Moderate tasks): Full annotation cycle with semantic merging.

\textbf{Enhanced Collaboration} (Complex tasks): Multiple refinement rounds with quality-driven iteration.

This adaptive approach ensures appropriate coordination while maintaining quality for tasks that require deeper collaboration.

\subsubsection{Domain-Specific Optimization}
We implement task-specific coordination optimizations:

\textbf{Mathematical Reasoning}: Focus on step verification and numerical accuracy with minimal explanatory text.

\textbf{Code Generation}: Emphasize functional correctness and edge case handling through structured testing protocols.

\textbf{Reading Comprehension}: Leverage evidence extraction and logical inference with targeted fact verification.

\textbf{Knowledge Reasoning}: Implement systematic option evaluation with focused knowledge application.

Each domain optimization reduces irrelevant content while maintaining task-specific quality requirements.

\subsection{Context Management Mechanisms}

\subsubsection{Structured Communication}
We maintain organized communication through four complementary techniques. First, each interaction phase follows structured formats to keep communication focused. Second, reusable prompt templates ensure consistent interaction patterns. Third, progressive refinement encourages agents to improve drafts incrementally rather than regenerate them from scratch. Finally, an early-termination rule stops the collaboration once the solution exceeds quality thresholds, avoiding unnecessary iterations.

\subsubsection{Context Management}
Organization is further improved by propagating only essential information across rounds, maintaining focused draft content, and presenting appropriate information levels to different agent roles as needed.

\subsection{Failure Mode Prevention}

Drawing from the Multi-Agent System Failure Taxonomy (MASFT) \cite{cemri2025multi}, our framework explicitly addresses common failure patterns through structural design choices:

\textbf{Preventing Inter-Agent Misalignment:} The shared draft serves as a single source of truth, eliminating conversation reset (FM-2.1) and task derailment (FM-2.3). Structured annotations ensure that no agent input is ignored (FM-2.5), while bounded communication prevents information withholding (FM-2.4).

\textbf{Ensuring Proper Verification:} Our Leader agent provides systematic quality assessment, preventing both premature termination (FM-3.1) and incomplete verification (FM-3.2). The multi-dimensional scoring rubric ensures thorough evaluation across accuracy, completeness, and clarity dimensions.

\textbf{Maintaining System Coherence:} Role-based specialization prevents agents from violating their specifications (FM-1.2), while the Controller's orchestration maintains clear stopping conditions (FM-1.5) and prevents step repetition (FM-1.3) through structured iteration control.

% ===== 新增 Implementation Details 部分 =====
\section{Implementation Details}

Our framework orchestrates multiple LLM providers to ensure reproducibility across different model architectures. We evaluate using four widely accessible models: OpenAI's \texttt{gpt-4o-mini}, Anthropic's \texttt{claude-3-5-haiku}, \texttt{llama-3.3-70b}, and Google's \texttt{gemini-2.0-flash}. This diverse model pool validates the robustness and generalisability of our communication protocol.

\subsection{Algorithm Overview}
Algorithm~\ref{alg:dmc} presents the simplified draft-mediated collaboration loop used in most experiments.

\begin{algorithm}[t]
\caption{Draft-Mediated Collaboration (Simplified Controller)}
\label{alg:dmc}
\begin{algorithmic}[1]
\REQUIRE Task prompt $q$, task type $t$, agent list $A$
\STATE Initialize empty draft $d \leftarrow \emptyset$
\STATE \textbf{parallel} for $a \in A$ \textbf{do}
\STATE \quad $d_a \leftarrow$ \textsc{GenerateDraft}$(a,q,t)$
\STATE \textbf{end parallel}
\STATE $d \leftarrow$ \textsc{SelectBestDraft}$\big(\{d_a\}\big)$
\STATE \textbf{parallel} for $a \in A$ \textbf{do}
\STATE \quad $\alpha_a \leftarrow$ \textsc{AnnotateDraft}$(a,d)$
\STATE \textbf{end parallel}
\STATE $d \leftarrow$ \textsc{MergeAgent}$\big(d,\{\alpha_a\}\big)$
\STATE $q_s \leftarrow$ \textsc{LeaderEvaluate}$(d)$
\IF{$q_s < \tau$}
\STATE Provide feedback and repeat Lines~2--10 (max 2 rounds)
\ENDIF
\RETURN Final answer extracted from $d$
\end{algorithmic}
\end{algorithm}

% ---------- NEW SUBSECTION: Collaboration Workflow Details ----------
\subsection{Collaboration Workflow Details}

Figure~\ref{fig:workflow} and Algorithm~\ref{alg:dmc} together depict the full life cycle of a DMC session. The protocol consists of five core phases that we elaborate below, emphasising agent responsibilities, concurrency, and communication patterns.

\paragraph{Phase~0: Parallel Draft Generation.}  The Controller issues the raw task prompt $q$ to each Worker ($a_i\in A$) using a role-specific system prompt.  All calls occur concurrently, so the wall\nobreakdash-clock time equals the slowest Worker latency.  Each Worker returns a concise solution draft $d_i$ (bounded in length).  The Controller then selects the best candidate via automatic scoring (e.g., exact match for math/code, or a lightweight voting LLM) to obtain the base draft $d^{(1)}$.

\paragraph{Phase~1: Broadcast \textbf{Shared Draft}.}  The selected draft is broadcast verbatim to every Worker.  Since context is shared, this ensures consistent information across all agents.

\paragraph{Phase~2: Structured Annotation.}  Each Worker inspects $d^{(r)}$ and emits a \emph{bounded} JSON annotation list that follows a strict schema. This schema is central to DMC's organization because it converts free-form feedback into machine-readable operations. The annotation format includes fields for annotation type, target content, suggested changes, and confidence scores.

\begin{listing}[tb]
\caption{Example JSON annotation (GSM8K)}\label{lst:gsm8k_json}
\begin{lstlisting}[language=json,numbers=none]
[
  {"span_id": 0, "type": "completeness", "priority": 3,
   "suggestion": "Consider adding a brief introduction to explain the problem and the goal, to improve clarity and context for the step-by-step solution."}
]
\end{lstlisting}
\end{listing}
The schema fields guide downstream processing. The \texttt{span\_id} uniquely identifies a text segment, \texttt{type} categorises the feedback (\texttt{accuracy}, \texttt{clarity}, \texttt{completeness}, or \texttt{typo}), \texttt{priority} (1–3) indicates urgency, and \texttt{suggestion} contains the proposed edit in concise form. Each JSON list maintains brevity to ensure focused communication.

\paragraph{Phase~3: Adaptive Merge.} The Merger receives $d^{(r)}$ and the set of annotation lists $\{\alpha_i^{(r)}\}$. Its \textit{Adaptive Merge Strategy Selector} then chooses a synthesis method based on the collective properties of the annotations. For instance, if annotation types are dominated by \texttt{typo} or low-priority \texttt{clarity}, it uses a fast \textbf{Sequential} pass. Conversely, if multiple high-priority \texttt{accuracy} annotations target the same \texttt{span\_id}, it invokes a \textbf{Conflict-Resolution} strategy that uses an LLM call to arbitrate. In other cases, it defaults to a \textbf{Semantic} merge, which rephrases sentences to smoothly integrate suggestions. This adaptive logic minimizes unnecessary LLM calls while ensuring robust handling of critical conflicts. Once a strategy is chosen, the merged draft $d^{(r{+}1)}$ is produced.

\paragraph{Phase~4: Leader Evaluation.}  The Leader agent scores $d^{(r{+}1)}$ on a 0–1 scale using rubric prompts.  If $\mathcal Q\ge\tau$ or $r=R_{\max}$, the Controller extracts the final answer and terminates; else it triggers Phase~5.



\paragraph{Communication Summary.} The communication pattern for one complete round ($r>0$) follows a structured approach. The input phase involves broadcasting the draft to all agents. The output consists of the collected annotations from all agents, plus the newly generated merged draft. A small overhead is incurred for the Leader and Selector prompts. This pattern maintains the structured communication derived in Theorem~\ref{thm:total}.

\begin{figure}[t]
\centering
% placeholder box for detailed workflow diagram
\fbox{\parbox{0.9\columnwidth}{\vspace{3.4cm}\centering Detailed workflow diagram (to be replaced with final artwork)\vspace{3.4cm}}}
\caption{End\nobreakdash-to\nobreakdash-end collaboration workflow in one iteration of DMC, illustrating the parallel annotation phase and subsequent merge--evaluate loop.}
\label{fig:workflow}
\end{figure}

\subsubsection{Structured Annotation Mechanism}
Central to our approach is the transformation of multi-agent dialogue into structured annotation operations. Instead of agents engaging in free-form conversation, each agent produces bounded annotations ($L \leq 150$ tokens) that target specific aspects of the shared draft. This structured approach eliminates the context explosion problem inherent in dialogue-based systems while preserving the benefits of multi-perspective reasoning.

\subsubsection{Adaptive Merge Strategy Selector}
We extend the Merger component with an \textit{Adaptive Merge Strategy Selector}. Given the annotation consistency, conflict severity, and historical performance, the selector automatically chooses among five merge strategies—\textsc{Sequential}, \textsc{Semantic}, \textsc{Conflict-Resolution}, \textsc{Priority-Based}, and \textsc{Creative}. The selector formulates a multi-objective score that balances consensus, conflict, coordination, and prior success, then recommends the strategy with the highest expected quality-coordination trade-off. This adaptation reduces unnecessary processing when consensus is high while ensuring robustness under conflicting annotations.

\subsubsection{Iterative Quality Improvement}
Our empirical observations suggest that draft quality tends to improve through iterative annotation and merging cycles. The bounded nature of annotations and the structured merge process create conditions where successive drafts often incorporate improvements while maintaining coherence. This iterative refinement typically converges within 2-4 rounds across our evaluated tasks.

\subsubsection{Communication Structure Analysis}
Our framework maintains predictable communication patterns through bounded interactions. Unlike dialogue-based systems where communication grows quadratically with agent count, DMC's draft-mediated approach ensures linear scaling, making it suitable for larger agent teams while preserving coordination effectiveness.

\subsection{Evaluation Framework}
Our implementation tracks communication patterns and interaction statistics across all operations to enable systematic comparison with baseline approaches. This allows for comprehensive analysis of the structured communication benefits demonstrated in Section~\ref{sec:results}.



% ---------- Theoretical Analysis ----------
\section{Theoretical Analysis}
\subsection{Communication Complexity}

\textbf{Notation.} Let $A$ be the number of worker agents, $N$ the average draft length, $L$ the maximum annotation length per agent, and $R$ the number of collaboration rounds. In our implementation, $L \in [100, 150]$ tokens and $N$ varies by task complexity, typically $N \in [500, 1500]$ tokens.

\begin{lemma}[Per-Round Communication Cost]
\label{lem:per_round}
In one collaboration round, the total communication in DMC includes draft broadcasting, annotation collection, merging, and evaluation, bounded by $O(N + A\,L)$ per round.
\end{lemma}

\begin{proof}[Proof sketch]
Each round involves: (1) broadcasting draft to $A$ workers ($A \times N$ tokens), (2) collecting annotations ($A \times L$ tokens), (3) merging process ($N + A \times L$ tokens), and (4) leader evaluation ($N$ tokens). The total is $O(A \times N + A \times L)$, which simplifies to $O(N + A\,L)$ when $A$ is small and constant.
\end{proof}

\begin{theorem}[Total Communication Complexity]
\label{thm:total}
DMC achieves structured communication with complexity $O(R \times (N + A \times L))$ where $R$ is task-adaptive (typically 2-4 rounds), compared to dialogue schemes that may require $O(R \times A^2 \times L)$ for full agent-to-agent communication.
\end{theorem}

\begin{proof}[Proof sketch]
DMC uses centralized coordination where each agent communicates only with the controller, avoiding quadratic agent-to-agent communication. The draft-mediated approach ensures that information flows efficiently through the shared draft rather than requiring all-to-all message passing.
\end{proof}

\paragraph{Implications.} DMC's centralized coordination avoids the quadratic communication overhead inherent in dialogue-based schemes. With task-adaptive parameters and bounded annotation lengths, the system maintains predictable communication patterns regardless of collaboration complexity. We verify this structured communication advantage empirically in Section~\ref{sec:results}.

% ===== 接下来保持原来的 Experimental Setup =====

\section{Experimental Setup}

We evaluate our draft-mediated collaboration framework against both single-agent baselines and existing multi-agent approaches across nine diverse benchmarks. Our evaluation focuses on performance quality and communication structure metrics.

\subsection{Benchmark Datasets}
\textbf{Mathematical Reasoning}: GSM8K \cite{cobbe2021training} (grade school math) and MATH \cite{hendrycks2021math} (competition mathematics) test numerical reasoning capabilities.

\textbf{Code Generation}: HumanEval \cite{chen2021evaluating} and MBPP \cite{austin2021program} evaluate programming problem-solving and functional correctness.

\textbf{Reading Comprehension}: DROP \cite{dua2019drop} and HotpotQA \cite{yang2018hotpotqa} assess information extraction and multi-hop reasoning.

\textbf{Knowledge Reasoning}: MMLU \cite{hendrycks2021measuring}, GPQA \cite{dhingra2022gpqa}, and StrategyQA \cite{geva2021strategyqa} test broad knowledge application and logical reasoning.

\subsection{Communication Metrics}
Beyond standard accuracy measures, we evaluate the structure and organization of agent interactions to understand communication patterns within our framework. We analyze context length distributions by measuring both maximum and average context lengths during processing, ensuring that our bounded communication protocol maintains predictable resource consumption. Additionally, we measure wall-clock processing time for collaborative reasoning to assess the practical efficiency of our multi-agent coordination compared to single-agent baselines.

\subsection{Baseline Comparisons}
We compare DMC against a comprehensive suite of baselines. These include single-agent methods like direct prompting and Chain-of-Thought (CoT); more advanced structured reasoning techniques such as Tree-of-Thoughts (ToT)~\cite{yao2024tree}, Graph-of-Thoughts (GoT)~\cite{besta2024graph}, and Chain-of-Draft (CoD)~\cite{xu2025cod}; and several multi-agent frameworks. The latter category encompasses dialogue-based systems~\cite{chen2023multi,du2023improving}, pruning-based collectives such as AgentPrune~\cite{zhang2024cut}, self-improving strategies like DyLAN~\cite{liu2024dynamic}, swarm-style collaboration like GPT-Swarm~\cite{frendo2023gptswarm}, and automated workflow generation such as AFLOW~\cite{zhang2024aflow}. We also report results for simple aggregation baselines (e.g., majority voting) and ablated versions of our own approach.

\section{Results}
\label{sec:results}

\begin{table*}[t]
    \centering
    \caption{Main results on nine benchmarks. We report Accuracy (\%) for most tasks, Pass@1 for code generation, and F1 score for reading comprehension. DMC achieves strong performance across all evaluated tasks.}
    \label{tab:main_results}
    \begin{tabular}{lccccccccc}
        \hline
        \textbf{Method} & \textbf{GSM8K} & \textbf{MATH} & \textbf{DROP} & \textbf{HotpotQA} & \textbf{HumanEval} & \textbf{MBPP} & \textbf{MMLU} & \textbf{GPQA} & \textbf{StrategyQA}\\
        \hline
        Direct & 81.0 & 33.9 & 91.0 & 35.0 & 71.0 & 99.0 & 32.9 & 41.0 & 71.0\\
        CoT & 94.0 & 45.0 & 92.0 & 69.0 & 77.0 & 99.0 & 38.0 & 44.0 & 71.0\\
        CoT-SC & 93.0 & 54.0 & 94.0 & 71.0 & 80.0 & 99.0 & 40.0 & 46.0 & 73.0\\
        CoD & 90.0 & 19.0 & 87.0 & 6.0 & 6.0 & 90.0 & 25.0 & 27.0 & 44.0\\
        LLM-Debate & 97.0 & 22.0 & 88.0 & 68.0 & 74.0 & 95.0 & 24.0 & 39.0 & 77.0\\
        AgentPrune & 93.0 & 45.0 & 90.0 & 56.0 & 80.0 & 99.0 & 56.0 & 32.0 & 63.0\\
        DyLAN & 75.0 & 69.0 & 15.9 & 20.0 & 57.1 & 91.7 & 32.0 & 40.0 & 36.7\\
        AFLOW & 81.0 & 63.0 & 75.0 & 22.5 & 85.0 & 100.0 & 18.0 & 39.0 & 73.0\\
        GPT-Swarm & 95.0 & 45.0 & 90.0 & 78.5 & 84.0 & 98.0 & 70.0 & 40.0 & 75.0\\
        \textbf{DMC (Ours)} & 98.0 & 65.5 & 83.3 & 76.7 & 82.0 & 100 & 75.9 & 49.0 & 83.3\\
        \hline
    \end{tabular}
\end{table*}

\begin{figure}[h!]
\centering
\includegraphics[width=0.9\columnwidth]{placeholder.png} % Placeholder, to be replaced
\caption{Communication structure versus normalized accuracy across selected benchmarks. DMC (solid blue line) consistently achieves high accuracy with structured communication, demonstrating superior coordination compared to dialogue-based baselines (dashed red lines).}
\label{fig:main_results_summary}
\end{figure}

DMC achieves strong performance across nine diverse tasks, with notable results including 98.0\% on GSM8K, 100\% on MBPP, and 83.3\% on StrategyQA. Beyond accuracy gains, DMC demonstrates substantial efficiency advantages in single-round execution: 29.6\% token reduction versus CoT Self-Consistency, 76.8\% reduction versus LLM-Debate, and 95.8\% reduction versus conversation-based systems (Section~\ref{sec:single_round_efficiency}). The efficiency stems from DMC's bounded annotation protocol, which prevents context explosion while maintaining collaborative benefits. Systematic ablation studies (Section~\ref{sec:ablation}) confirm that each DMC component contributes significantly to these efficiency gains, with annotation bounds preventing 1127.7\% token overhead and structured protocols reducing processing complexity by 645.7\% compared to free-form alternatives.

\subsection{Single-Round Efficiency Analysis}

To evaluate DMC's computational efficiency, we conduct comprehensive single-round comparisons against prominent multi-agent reasoning baselines. We test across diverse reasoning tasks using identical problem instances and model configurations (OpenAI GPT-4, Google Gemini, Meta Llama).

\textbf{Experimental Setup:} Each method processes the same set of problems in a single reasoning round. DMC executes its complete four-phase pipeline (Draft → Annotation → Merge → Evaluation), while CoT Self-Consistency (CoT-SC) performs three-sample generation with consistency checking, and LLM-Debate conducts three-phase debate (Independent → Debate → Judge). Multi-round conversation systems engage in extended dialogue until convergence.

\textbf{Results:} Table~\ref{tab:single_round_efficiency} demonstrates that DMC achieves substantial token efficiency gains in single-round execution: 29.6\% reduction versus CoT-SC, 76.8\% reduction versus LLM-Debate, and 95.8\% reduction versus conversation-based systems. Figure~\ref{fig:efficiency_comparison} illustrates the efficiency trade-offs across methods.

\begin{table}[h!]
\centering
\caption{Single-round efficiency comparison of multi-agent reasoning methods. All measurements use identical problem sets and model configurations.}
\label{tab:single_round_efficiency}
\begin{tabular}{lccc}
\toprule
\textbf{Method} & \textbf{Avg Tokens} & \textbf{Avg Time (s)} & \textbf{Token Reduction} \\
\midrule
DMC (1 round) & 1,305 & 12.0 & Baseline \\
CoT Self-Consistency & 1,853 & 6.7 & -29.6\% \\
LLM-Debate & 5,619 & 33.8 & -76.8\% \\
Multi-Round Conversation & 30,926 & 72.7 & -95.8\% \\
\bottomrule
\end{tabular}
\end{table}

\begin{figure}[h!]
\centering
\includegraphics[width=0.9\columnwidth]{seaborn_main_comparison.png}
\caption{Single-round efficiency comparison showing token usage and processing time across multi-agent reasoning methods. DMC achieves substantial token savings while maintaining competitive processing times.}
\label{fig:efficiency_comparison}
\end{figure}

The efficiency advantage stems from DMC's structured collaboration architecture. Figure~\ref{fig:dmc_phases} shows the token distribution across DMC's four phases, with annotation consuming the largest portion (34.1\%) but remaining bounded by the 150-token limit per agent.

\begin{figure}[h!]
\centering
\includegraphics[width=0.9\columnwidth]{seaborn_dmc_phases.png}
\caption{DMC phase breakdown showing token distribution across the four-phase pipeline. Annotation represents the largest component but remains controlled through bounded communication.}
\label{fig:dmc_phases}
\end{figure}

Figure~\ref{fig:efficiency_gains} quantifies DMC's efficiency gains relative to each baseline method, demonstrating consistent advantages across different multi-agent approaches.

\begin{figure}[h!]
\centering
\includegraphics[width=0.8\columnwidth]{seaborn_efficiency_gains.png}
\caption{DMC token efficiency gains in single-round execution. The structured collaboration protocol achieves significant computational savings compared to sampling-based and debate-based multi-agent methods.}
\label{fig:efficiency_gains}
\end{figure}

\subsection{Collaboration Efficiency Analysis}

We analyze DMC's communication efficiency across multiple dimensions, comparing against theoretical dialogue-based systems and measuring actual resource consumption patterns.

\subsubsection{Token Usage Patterns}
Our empirical analysis reveals substantial token efficiency gains through controlled comparison with dialogue-based multi-agent systems. DMC maintains an average of 2,529 tokens per collaborative session, while equivalent dialogue-based approaches consume 6,495 tokens on average—a 61.1\% reduction. This efficiency stems from bounded annotation protocols that prevent the context explosion characteristic of free-form multi-agent dialogue.

The token distribution shows consistent patterns across task types: mathematical reasoning tasks average 1,616-3,397 tokens in DMC versus 4,534-8,740 tokens in dialogue systems. Importantly, DMC's token usage remains bounded regardless of problem complexity, with individual task savings ranging from 55.0\% to 65.9\%, demonstrating the robustness of our structured annotation protocol across diverse reasoning domains.

\subsubsection{Communication Complexity Scaling}
Figure~\ref{fig:complexity} illustrates DMC's communication scaling properties. Our draft-mediated approach maintains linear scaling with agent count, as each agent only needs to process the shared draft and contribute bounded annotations. This contrasts with dialogue-based systems where agents must potentially process messages from all other agents, leading to quadratic growth in communication complexity.

Our empirical data shows that DMC maintains consistent token usage patterns regardless of problem complexity, with the bounded annotation protocol (L ≤ 150 tokens per agent) ensuring predictable resource consumption. Comparative analysis against dialogue-based multi-agent systems reveals that DMC achieves substantial efficiency gains: 61.1\% reduction in token consumption (2,529 vs 6,495 tokens average) and 88.2\% reduction in wall-clock latency (5.62s vs 47.61s). This efficiency stems from bounded context growth and parallel processing architecture, contrasting sharply with the quadratic context expansion inherent in free-form dialogue systems.

\subsubsection{Time-Token Efficiency Trade-offs}
Our analysis reveals that DMC achieves superior time-token efficiency compared to dialogue-based alternatives. DMC processes collaborative reasoning tasks in an average of 5.62 seconds while consuming 2,529 tokens, yielding a processing rate of 450 tokens per second. In contrast, dialogue-based systems require 47.61 seconds and 6,495 tokens, achieving only 136 tokens per second—a 3.3× efficiency disadvantage.

The bounded annotation protocol (L ≤ 150 tokens) ensures predictable processing times while maintaining information density. DMC's parallel processing architecture eliminates the sequential bottlenecks inherent in dialogue-based coordination, where agents must wait for complete conversation histories before contributing. This architectural advantage becomes more pronounced with task complexity, as dialogue systems exhibit quadratic growth in both time and token consumption.

\begin{table}[h]
\centering
\caption{DMC Efficiency Metrics vs Dialogue-Based Multi-Agent Systems}
\label{tab:efficiency}
\small
\begin{tabular}{lcc}
\hline
\textbf{Metric} & \textbf{Dialogue System} & \textbf{DMC} \\
\hline
Avg tokens/question & 6,495 & 2,529 \\
Token reduction & - & 61.1\% \\
Avg response time & 47.61s & 5.62s \\
Latency reduction & - & 88.2\% \\
Context growth & Quadratic & Bounded \\
Communication pattern & Free-form & Structured \\
\hline
\end{tabular}
\end{table}

\subsubsection{Communication Pattern Analysis}
DMC's structured communication exhibits several advantageous patterns. First, \textit{parallel processing}: agents can simultaneously analyze and annotate drafts without waiting for sequential message exchanges, reducing overall latency. Second, \textit{focused feedback}: the bounded annotation protocol forces agents to provide concise, targeted input rather than verbose commentary, increasing information density. Third, \textit{conflict localization}: disagreements are contained within specific draft sections rather than propagating through entire conversation threads.

Our efficiency tracking system captures detailed metrics for each collaboration session, enabling systematic analysis of communication patterns and resource consumption. Controlled comparison with dialogue-based multi-agent systems demonstrates DMC's substantial efficiency advantages: 61.1\% reduction in token consumption and 88.2\% reduction in processing latency. These gains stem from DMC's bounded context growth (O(N + A·L)) versus the quadratic expansion characteristic of free-form dialogue systems, where conversation history grows with each agent interaction. The efficiency advantage becomes more pronounced with task complexity, as dialogue systems suffer from context explosion while DMC maintains predictable resource consumption.

\subsection{Ablation Study}
\label{sec:ablation}

We conduct systematic ablation experiments to validate the contribution of each DMC component. Figure~\ref{fig:ablation} presents comprehensive results across five experimental conditions, demonstrating the necessity of each design choice.

\textbf{Annotation Bounds:} Removing the 150-token annotation limit (``No Bounds'') leads to dramatic efficiency degradation: 1127.7\% increase in token consumption and 476.6\% increase in processing time compared to single-agent baselines. This validates our bounded communication protocol as essential for preventing context explosion.

\textbf{Structured Annotation:} Eliminating structured annotation schemas (``No Structure'') results in 645.7\% token overhead and 115.1\% time overhead. Free-form feedback lacks the precision and conciseness that structured annotations provide, leading to verbose and unfocused agent interactions.

\textbf{Parallel Processing:} Sequential agent processing (``No Parallel'') increases token consumption by 313.8\% while reducing time overhead to only 35.8\%. This demonstrates that parallel processing is crucial for time efficiency, though it introduces some coordination overhead.

\textbf{Full DMC System:} Our complete framework achieves the optimal balance with 234.1\% token increase and 161.1\% time increase compared to single agents, while providing the collaborative benefits of multi-agent reasoning. The efficiency gains over ablated variants confirm that all components work synergistically.

\begin{figure}[h!]
\centering
\includegraphics[width=0.9\columnwidth]{ablation_combined_large.png}
\caption{Ablation study results showing token usage and processing time across different DMC configurations. Error bars indicate standard deviation. Each component contributes significantly to overall efficiency, with annotation bounds being the most critical for preventing context explosion.}
\label{fig:ablation}
\end{figure}

\begin{figure}[h!]
\centering
\includegraphics[width=0.8\columnwidth]{ablation_scatter_large.png}
\caption{Efficiency trade-off analysis showing the relationship between token usage and processing time for different DMC configurations. The single agent baseline serves as reference point (1.0, 1.0). Full DMC achieves optimal balance between efficiency and collaborative benefits.}
\label{fig:ablation_efficiency}
\end{figure}

\subsection{Annotation Quality and Consensus Patterns}
Our analysis reveals interesting patterns in annotation quality and consensus formation. High-consensus annotations (agreement >80\%) correlate strongly with final solution quality (r=0.73), while conflicting annotations often indicate areas requiring deeper analysis. The adaptive merge strategy selector successfully identifies these patterns, choosing consensus-based merging for aligned annotations and conflict-resolution strategies when disagreement exceeds 40\%.

\subsection{Qualitative Case Studies}
Beyond quantitative gains, our framework offers clear qualitative advantages. Our analysis of collaboration sessions reveals how targeted annotations quickly identify logical gaps and factual errors that would remain unresolved in standard single-shot prompting, demonstrating the value of structured multi-agent feedback.

% ---------- Discussion & Future Work ----------
% Polished Discussion Section
\section{Discussion and Future Work}

\paragraph{Paradigm Shift: From Dialogue to Artifact-Centric Collaboration.} DMC represents a fundamental paradigm shift in multi-agent system design. Traditional approaches model collaboration as conversational exchanges, inheriting the unbounded nature of human dialogue. We propose that effective multi-agent problem-solving requires structured interaction around shared artifacts rather than free-form communication. This shift has profound implications: it transforms the multi-agent coordination problem from managing dialogue complexity to optimizing artifact evolution, enabling predictable resource consumption and scalable coordination patterns.

\paragraph{Relationship to Contemporary Frameworks.} Our work engages with a vibrant landscape of multi-agent and reasoning-enhancement frameworks. While Chain of Draft (CoD)~\cite{xu2025cod} effectively curtails verbosity for a *single agent*, DMC is designed to solve the subsequent challenge: managing communication coordination as the number of agents scales. On GSM8K, for instance, DMC achieves 98.0\% accuracy with structured communication patterns, demonstrating superior coordination compared to both single-agent CoD and more communication-intensive workflow optimization systems like AFLOW~\cite{zhang2024aflow}. Our approach is thus orthogonal and complementary: AFLOW could potentially discover a DMC-like protocol as an optimal coordination primitive in its search space.

\paragraph{Addressing Multi-Agent System Failures.} The recent comprehensive study by Cemri et al.~\cite{cemri2025multi} provides empirical validation for our design choices. Their MASFT taxonomy identifies critical failure modes that DMC explicitly addresses: our structured annotation system prevents \textit{conversation reset} (FM-2.1) and \textit{task derailment} (FM-2.3) by maintaining focus on the shared draft; our bounded communication protocol mitigates \textit{information withholding} (FM-2.4) and \textit{ignored agent input} (FM-2.5) through mandatory structured feedback; and our Leader evaluation mechanism directly tackles \textit{incomplete verification} (FM-3.2) and \textit{premature termination} (FM-3.1). The fact that existing multi-agent systems exhibit failure rates of 25-75\% while DMC maintains consistent performance across benchmarks suggests that artifact-centric collaboration may be a fundamental solution to the inter-agent misalignment problems identified in MASFT.

\paragraph{Limitations and Ethical Considerations.} Our framework has limitations. The heuristic-based merger, while efficient, may struggle with deeply conflicting, high-priority annotations. Furthermore, annotation caps ($L$) are currently hand-tuned per task; future work should explore adaptive budget allocation. Ethically, as with any multi-agent system, there is a risk of bias amplification. A malicious agent, for instance, could inject subtle misinformation into annotations. While DMC's Leader evaluation provides a layer of defense, robust security will require more advanced mechanisms like cross-agent consistency checks or provenance tracking, which we leave for future work.

\paragraph{Future Directions.} The draft-mediated paradigm opens several promising research directions. \textit{Theoretical extensions} could explore optimal annotation allocation strategies, convergence rates under different merge functions, and robustness guarantees against adversarial agents. \textit{Architectural innovations} might include hierarchical draft structures for complex problems, dynamic agent role assignment based on expertise, and cross-domain draft transfer mechanisms. \textit{Empirical investigations} could examine scaling laws for draft-mediated systems, comparative analysis with human collaborative patterns, and effectiveness across broader task domains. Most fundamentally, we envision extending the artifact-centric paradigm beyond text to multimodal drafts encompassing code, diagrams, and structured data, potentially revolutionizing how AI systems collaborate on complex, multi-faceted problems.

% References and End of Paper
% These lines must be placed at the end of your paper
\bibliography{aaai2026}

\end{document}
