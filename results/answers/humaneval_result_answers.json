{"metadata": {"timestamp": "2025-07-23T16:31:01.255135", "models_used": ["openai", "anthropic", "llama"], "task_type": "humaneval"}, "results": [{"id": "62f9b2a2", "prompt": "\ndef solve(N):\n    \"\"\"Given a positive integer N, return the total sum of its digits in binary.\n    \n    Example\n        For N = 1000, the sum of digits will be 1 the output should be \"1\".\n        For N = 150, the sum of digits will be 6 the output should be \"110\".\n        For N = 147, the sum of digits will be 12 the output should be \"1100\".\n    \n    Variables:\n        @N integer\n             Constraints: 0 ≤ N ≤ 10000.\n    Output:\n         a string of binary number\n    \"\"\"\n", "solution": "```python\ndef solve(N):\n    \"\"\"\n    Calculates the sum of the digits of a non-negative integer N, and returns the binary representation of that sum.\n\n    Args:\n        N: A non-negative integer between 0 and 10000 inclusive.\n\n    Returns:\n        A string representing the binary representation of the sum of the digits of N.\n\n    Raises:\n        ValueError: If N is not an integer or if N is not within the valid range [0, 10000].\n    \"\"\"\n    if not isinstance(N, int) or N < 0 or N > 10000:\n        raise ValueError(\"Input must be a positive integer between 0 and 10000 inclusive.\")\n    \n    digit_sum = sum(int(digit) for digit in str(N))\n    binary_sum = bin(digit_sum)[2:]\n    \n    return binary_sum\n```", "execution_time": 72.03275299072266, "code": "def solve(N):\n    \"\"\"\n    Calculates the sum of the digits of a non-negative integer N, and returns the binary representation of that sum.\n\n    Args:\n        N: A non-negative integer between 0 and 10000 inclusive.\n\n    Returns:\n        A string representing the binary representation of the sum of the digits of N.\n\n    Raises:\n        ValueError: If N is not an integer or if N is not within the valid range [0, 10000].\n    \"\"\"\n    if not isinstance(N, int) or N < 0 or N > 10000:\n        raise ValueError(\"Input must be a positive integer between 0 and 10000 inclusive.\")\n    \n    digit_sum = sum(int(digit) for digit in str(N))\n    binary_sum = bin(digit_sum)[2:]\n    \n    return binary_sum", "problem_index": 0, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "b39fa437", "prompt": "\n\ndef truncate_number(number: float) -> float:\n    \"\"\" Given a positive floating point number, it can be decomposed into\n    and integer part (largest integer smaller than given number) and decimals\n    (leftover part always smaller than 1).\n\n    Return the decimal part of the number.\n    >>> truncate_number(3.5)\n    0.5\n    \"\"\"\n", "solution": "```python\ndef truncate_number(number: float) -> float:\n    \"\"\" Given a positive floating point number, it can be decomposed into\n    an integer part (largest integer smaller than given number) and decimals\n    (leftover part always smaller than 1).\n\n    Return the decimal part of the number.\n    \n    >>> truncate_number(3.5)\n    0.5\n    >>> truncate_number(5.0)\n    0.0\n    >>> truncate_number(0.75)\n    0.75\n    >>> truncate_number(10.99)\n    0.99\n    >>> truncate_number(1.234567)\n    0.234567\n    >>> truncate_number(-3.5)\n    Traceback (most recent call last):\n        ...\n    ValueError: Input must be a positive floating point number.\n    >>> truncate_number(\"3.5\")\n    Traceback (most recent call last):\n        ...\n    TypeError: Input must be a float.\n    \"\"\"\n    if not isinstance(number, float):\n        raise TypeError(\"Input must be a float.\")\n    if number < 0:\n        raise ValueError(\"Input must be a positive floating point number.\")\n    \n    integer_part = int(number)\n    decimal_part = number - integer_part\n    return decimal_part\n```", "execution_time": 29.33232092857361, "code": "def truncate_number(number: float) -> float:\n    \"\"\" Given a positive floating point number, it can be decomposed into\n    an integer part (largest integer smaller than given number) and decimals\n    (leftover part always smaller than 1).\n\n    Return the decimal part of the number.\n    \n    >>> truncate_number(3.5)\n    0.5\n    >>> truncate_number(5.0)\n    0.0\n    >>> truncate_number(0.75)\n    0.75\n    >>> truncate_number(10.99)\n    0.99\n    >>> truncate_number(1.234567)\n    0.234567\n    >>> truncate_number(-3.5)\n    Traceback (most recent call last):\n        ...\n    ValueError: Input must be a positive floating point number.\n    >>> truncate_number(\"3.5\")\n    Traceback (most recent call last):\n        ...\n    TypeError: Input must be a float.\n    \"\"\"\n    if not isinstance(number, float):\n        raise TypeError(\"Input must be a float.\")\n    if number < 0:\n        raise ValueError(\"Input must be a positive floating point number.\")\n    \n    integer_part = int(number)\n    decimal_part = number - integer_part\n    return decimal_part", "problem_index": 1, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "819e4285", "prompt": "\n\ndef skjkasdkd(lst):\n    \"\"\"You are given a list of integers.\n    You need to find the largest prime value and return the sum of its digits.\n\n    Examples:\n    For lst = [0,3,2,1,3,5,7,4,5,5,5,2,181,32,4,32,3,2,32,324,4,3] the output should be 10\n    For lst = [1,0,1,8,2,4597,2,1,3,40,1,2,1,2,4,2,5,1] the output should be 25\n    For lst = [1,3,1,32,5107,34,83278,109,163,23,2323,32,30,1,9,3] the output should be 13\n    For lst = [0,724,32,71,99,32,6,0,5,91,83,0,5,6] the output should be 11\n    For lst = [0,81,12,3,1,21] the output should be 3\n    For lst = [0,8,1,2,1,7] the output should be 7\n    \"\"\"\n", "solution": "```python\ndef sum_of_digits_of_largest_prime(lst: list[int]) -> int:\n    \"\"\"\n    This function finds the largest prime number in a given list and returns the sum of its digits.\n\n    Args:\n        lst (list): A list of integers.\n\n    Returns:\n        int: The sum of the digits of the largest prime number in the list.\n    \"\"\"\n\n    def is_prime(n):\n        \"\"\"Check if a number is prime.\"\"\"\n        if n < 2:\n            return False\n        for i in range(2, int(n ** 0.5) + 1):\n            if n % i == 0:\n                return False\n        return True\n\n    def sum_of_digits(n):\n        \"\"\"Calculate the sum of digits of a number.\"\"\"\n        return sum(int(digit) for digit in str(n))\n\n    # Filter the list to include only prime numbers\n    prime_numbers = []\n    for num in lst:\n        if not isinstance(num, int):\n            continue\n        if is_prime(num):\n            prime_numbers.append(num)\n\n    # If no prime numbers are found, return 0\n    if not prime_numbers:\n        return 0\n\n    # Find the largest prime number\n    largest_prime = max(prime_numbers)\n\n    # Return the sum of the digits of the largest prime number\n    return sum_of_digits(largest_prime)\n```", "execution_time": 61.76173806190491, "code": "def sum_of_digits_of_largest_prime(lst: list[int]) -> int:\n    \"\"\"\n    This function finds the largest prime number in a given list and returns the sum of its digits.\n\n    Args:\n        lst (list): A list of integers.\n\n    Returns:\n        int: The sum of the digits of the largest prime number in the list.\n    \"\"\"\n\n    def is_prime(n):\n        \"\"\"Check if a number is prime.\"\"\"\n        if n < 2:\n            return False\n        for i in range(2, int(n ** 0.5) + 1):\n            if n % i == 0:\n                return False\n        return True\n\n    def sum_of_digits(n):\n        \"\"\"Calculate the sum of digits of a number.\"\"\"\n        return sum(int(digit) for digit in str(n))\n\n    # Filter the list to include only prime numbers\n    prime_numbers = []\n    for num in lst:\n        if not isinstance(num, int):\n            continue\n        if is_prime(num):\n            prime_numbers.append(num)\n\n    # If no prime numbers are found, return 0\n    if not prime_numbers:\n        return 0\n\n    # Find the largest prime number\n    largest_prime = max(prime_numbers)\n\n    # Return the sum of the digits of the largest prime number\n    return sum_of_digits(largest_prime)", "problem_index": 2, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "e66405c4", "prompt": "\n\ndef triangle_area(a, h):\n    \"\"\"Given length of a side and high return area for a triangle.\n    >>> triangle_area(5, 3)\n    7.5\n    \"\"\"\n", "solution": "def triangle_area(a: float, h: float) -> float:\n    \"\"\"Given length of a side and height, return the area of a triangle.\n    \n    Args:\n        a (float): Length of the base of the triangle.\n        h (float): Height of the triangle.\n    \n    Returns:\n        float: Area of the triangle calculated using the formula (1/2) * base * height.\n    \n    Raises:\n        ValueError: If the base or height is negative.\n    \n    >>> triangle_area(5, 3)\n    7.5\n    >>> triangle_area(0, 5)\n    0.0\n    >>> triangle_area(5, 0)\n    0.0\n    >>> triangle_area(-5, 3)\n    Traceback (most recent call last):\n        ...\n    ValueError: Base must be non-negative.\n    >>> triangle_area(5, -3)\n    Traceback (most recent call last):\n        ...\n    ValueError: Height must be non-negative.\n    \"\"\"\n    if a < 0:\n        raise ValueError(\"Base must be non-negative.\")\n    if h < 0:\n        raise ValueError(\"Height must be non-negative.\")\n    return 0.5 * a * h", "execution_time": 25.849169969558716, "code": "def triangle_area(a: float, h: float) -> float:\n    \"\"\"Given length of a side and height, return the area of a triangle.\n    \n    Args:\n        a (float): Length of the base of the triangle.\n        h (float): Height of the triangle.\n    \n    Returns:\n        float: Area of the triangle calculated using the formula (1/2) * base * height.\n    \n    Raises:\n        ValueError: If the base or height is negative.\n    \n    >>> triangle_area(5, 3)\n    7.5\n    >>> triangle_area(0, 5)\n    0.0\n    >>> triangle_area(5, 0)\n    0.0\n    >>> triangle_area(-5, 3)\n    Traceback (most recent call last):\n        ...\n    ValueError: Base must be non-negative.\n    >>> triangle_area(5, -3)\n    Traceback (most recent call last):\n        ...\n    ValueError: Height must be non-negative.\n    \"\"\"\n    if a < 0:\n        raise ValueError(\"Base must be non-negative.\")\n    if h < 0:\n        raise ValueError(\"Height must be non-negative.\")\n    return 0.5 * a * h", "problem_index": 3, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "e0cc32e0", "prompt": "\n\ndef incr_list(l: list):\n    \"\"\"Return list with elements incremented by 1.\n    >>> incr_list([1, 2, 3])\n    [2, 3, 4]\n    >>> incr_list([5, 3, 5, 2, 3, 3, 9, 0, 123])\n    [6, 4, 6, 3, 4, 4, 10, 1, 124]\n    \"\"\"\n", "solution": "```python\ndef incr_list(l: list):\n    \"\"\"Return list with elements incremented by 1.\n    >>> incr_list([1, 2, 3])\n    [2, 3, 4]\n    >>> incr_list([5, 3, 5, 2, 3, 3, 9, 0, 123])\n    [6, 4, 6, 3, 4, 4, 10, 1, 124]\n    >>> incr_list([])\n    []\n    >>> incr_list([-1, -2, -3])\n    [0, -1, -2]\n    >>> incr_list([0])\n    [1]\n    >>> incr_list([None])\n    Traceback (most recent call last):\n        ...\n    TypeError: unsupported operand type(s) for +: 'NoneType' and 'int'\n    \"\"\"\n    if not isinstance(l, list):\n        raise TypeError(\"Input must be a list.\")\n    if not all(isinstance(x, (int, float)) for x in l):\n        raise TypeError(\"All elements in the list must be integers or floats.\")\n    return list(map(lambda x: x + 1, l))\n```", "execution_time": 27.801373958587646, "code": "def incr_list(l: list):\n    \"\"\"Return list with elements incremented by 1.\n    >>> incr_list([1, 2, 3])\n    [2, 3, 4]\n    >>> incr_list([5, 3, 5, 2, 3, 3, 9, 0, 123])\n    [6, 4, 6, 3, 4, 4, 10, 1, 124]\n    >>> incr_list([])\n    []\n    >>> incr_list([-1, -2, -3])\n    [0, -1, -2]\n    >>> incr_list([0])\n    [1]\n    >>> incr_list([None])\n    Traceback (most recent call last):\n        ...\n    TypeError: unsupported operand type(s) for +: 'NoneType' and 'int'\n    \"\"\"\n    if not isinstance(l, list):\n        raise TypeError(\"Input must be a list.\")\n    if not all(isinstance(x, (int, float)) for x in l):\n        raise TypeError(\"All elements in the list must be integers or floats.\")\n    return list(map(lambda x: x + 1, l))", "problem_index": 4, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "97a074c8", "prompt": "\ndef search(lst):\n    '''\n    You are given a non-empty list of positive integers. Return the greatest integer that is greater than \n    zero, and has a frequency greater than or equal to the value of the integer itself. \n    The frequency of an integer is the number of times it appears in the list.\n    If no such a value exist, return -1.\n    Examples:\n        search([4, 1, 2, 2, 3, 1]) == 2\n        search([1, 2, 2, 3, 3, 3, 4, 4, 4]) == 3\n        search([5, 5, 4, 4, 4]) == -1\n    '''\n", "solution": "```python\nfrom collections import Counter\n\ndef search(lst):\n    '''\n    You are given a non-empty list of positive integers. Return the greatest integer that is greater than \n    zero, and has a frequency greater than or equal to the value of the integer itself. \n    The frequency of an integer is the number of times it appears in the list.\n    If no such a value exist, return -1.\n    Examples:\n        search([4, 1, 2, 2, 3, 1]) == 2\n        search([1, 2, 2, 3, 3, 3, 4, 4, 4]) == 3\n        search([5, 5, 4, 4, 4]) == -1\n    '''\n    frequency = Counter(lst)\n    result = -1\n    \n    for num in sorted(frequency.keys(), reverse=True):\n        if num > 0 and frequency[num] >= num:\n            return num\n    \n    return result\n```", "execution_time": 57.72081208229065, "code": "from collections import Counter\n\ndef search(lst):\n    '''\n    You are given a non-empty list of positive integers. Return the greatest integer that is greater than \n    zero, and has a frequency greater than or equal to the value of the integer itself. \n    The frequency of an integer is the number of times it appears in the list.\n    If no such a value exist, return -1.\n    Examples:\n        search([4, 1, 2, 2, 3, 1]) == 2\n        search([1, 2, 2, 3, 3, 3, 4, 4, 4]) == 3\n        search([5, 5, 4, 4, 4]) == -1\n    '''\n    frequency = Counter(lst)\n    result = -1\n    \n    for num in sorted(frequency.keys(), reverse=True):\n        if num > 0 and frequency[num] >= num:\n            return num\n    \n    return result", "problem_index": 5, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "c099a008", "prompt": "\ndef Strongest_Extension(class_name, extensions):\n    \"\"\"You will be given the name of a class (a string) and a list of extensions.\n    The extensions are to be used to load additional classes to the class. The\n    strength of the extension is as follows: Let CAP be the number of the uppercase\n    letters in the extension's name, and let SM be the number of lowercase letters \n    in the extension's name, the strength is given by the fraction CAP - SM. \n    You should find the strongest extension and return a string in this \n    format: ClassName.StrongestExtensionName.\n    If there are two or more extensions with the same strength, you should\n    choose the one that comes first in the list.\n    For example, if you are given \"Slices\" as the class and a list of the\n    extensions: ['SErviNGSliCes', 'Cheese', 'StuFfed'] then you should\n    return 'Slices.SErviNGSliCes' since 'SErviNGSliCes' is the strongest extension \n    (its strength is -1).\n    Example:\n    for Strongest_Extension('my_class', ['AA', 'Be', 'CC']) == 'my_class.AA'\n    \"\"\"\n", "solution": "```python\ndef Strongest_Extension(class_name, extensions):\n    \"\"\"You will be given the name of a class (a string) and a list of extensions.\n    The extensions are to be used to load additional classes to the class. The\n    strength of the extension is as follows: Let CAP be the number of the uppercase\n    letters in the extension's name, and let SM be the number of lowercase letters \n    in the extension's name, the strength is given by the fraction CAP - SM. \n    You should find the strongest extension and return a string in this \n    format: ClassName.StrongestExtensionName.\n    If there are two or more extensions with the same strength, you should\n    choose the one that comes first in the list.\n    For example, if you are given \"Slices\" as the class and a list of the\n    extensions: ['SErviNGSliCes', 'Cheese', 'StuFfed'] then you should\n    return 'Slices.SErviNGSliCes' since 'SErviNGSliCes' is the strongest extension \n    (its strength is -1).\n    Example:\n    for Strongest_Extension('my_class', ['AA', 'Be', 'CC']) == 'my_class.AA'\n    \"\"\"\n    if not isinstance(class_name, str) or not isinstance(extensions, list):\n        raise ValueError(\"class_name must be a string and extensions must be a list.\")\n    \n    if not extensions:\n        raise ValueError(\"The extensions list cannot be empty.\")\n    \n    strongest_extension = None\n    max_strength = float('-inf')\n    \n    for extension in extensions:\n        if not isinstance(extension, str):\n            raise ValueError(\"All extensions must be strings.\")\n        \n        uppercase_count = sum(1 for char in extension if char.isupper())\n        lowercase_count = sum(1 for char in extension if char.islower())\n        strength = uppercase_count - lowercase_count\n        \n        if strength > max_strength:\n            max_strength = strength\n            strongest_extension = extension\n    \n    return f\"{class_name}.{strongest_extension}\"\n```", "execution_time": 76.43227982521057, "code": "def Strongest_Extension(class_name, extensions):\n    \"\"\"You will be given the name of a class (a string) and a list of extensions.\n    The extensions are to be used to load additional classes to the class. The\n    strength of the extension is as follows: Let CAP be the number of the uppercase\n    letters in the extension's name, and let SM be the number of lowercase letters \n    in the extension's name, the strength is given by the fraction CAP - SM. \n    You should find the strongest extension and return a string in this \n    format: ClassName.StrongestExtensionName.\n    If there are two or more extensions with the same strength, you should\n    choose the one that comes first in the list.\n    For example, if you are given \"Slices\" as the class and a list of the\n    extensions: ['SErviNGSliCes', 'Cheese', 'StuFfed'] then you should\n    return 'Slices.SErviNGSliCes' since 'SErviNGSliCes' is the strongest extension \n    (its strength is -1).\n    Example:\n    for Strongest_Extension('my_class', ['AA', 'Be', 'CC']) == 'my_class.AA'\n    \"\"\"\n    if not isinstance(class_name, str) or not isinstance(extensions, list):\n        raise ValueError(\"class_name must be a string and extensions must be a list.\")\n    \n    if not extensions:\n        raise ValueError(\"The extensions list cannot be empty.\")\n    \n    strongest_extension = None\n    max_strength = float('-inf')\n    \n    for extension in extensions:\n        if not isinstance(extension, str):\n            raise ValueError(\"All extensions must be strings.\")\n        \n        uppercase_count = sum(1 for char in extension if char.isupper())\n        lowercase_count = sum(1 for char in extension if char.islower())\n        strength = uppercase_count - lowercase_count\n        \n        if strength > max_strength:\n            max_strength = strength\n            strongest_extension = extension\n    \n    return f\"{class_name}.{strongest_extension}\"", "problem_index": 6, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "90a10f2b", "prompt": "from typing import List\n\n\ndef remove_duplicates(numbers: List[int]) -> List[int]:\n    \"\"\" From a list of integers, remove all elements that occur more than once.\n    Keep order of elements left the same as in the input.\n    >>> remove_duplicates([1, 2, 3, 2, 4])\n    [1, 3, 4]\n    \"\"\"\n", "solution": "```python\nfrom typing import List\n\ndef remove_duplicates(numbers: List[int]) -> List[int]:\n    \"\"\" From a list of integers, remove all elements that occur more than once.\n    Keep order of elements left the same as in the input.\n    >>> remove_duplicates([1, 2, 3, 2, 4])\n    [1, 3, 4]\n    >>> remove_duplicates([1, 1, 1, 1])\n    []\n    >>> remove_duplicates([])\n    []\n    >>> remove_duplicates([1, 2, 3, 4, 5])\n    [1, 2, 3, 4, 5]\n    >>> remove_duplicates([1, 2, 2, 3, 3, 4])\n    [1, 4]\n    \"\"\"\n    from collections import Counter\n    \n    count = Counter(numbers)\n    return [num for num in numbers if count[num] == 1]\n```", "execution_time": 25.037242889404297, "code": "from typing import List\n\ndef remove_duplicates(numbers: List[int]) -> List[int]:\n    \"\"\" From a list of integers, remove all elements that occur more than once.\n    Keep order of elements left the same as in the input.\n    >>> remove_duplicates([1, 2, 3, 2, 4])\n    [1, 3, 4]\n    >>> remove_duplicates([1, 1, 1, 1])\n    []\n    >>> remove_duplicates([])\n    []\n    >>> remove_duplicates([1, 2, 3, 4, 5])\n    [1, 2, 3, 4, 5]\n    >>> remove_duplicates([1, 2, 2, 3, 3, 4])\n    [1, 4]\n    \"\"\"\n    from collections import Counter\n    \n    count = Counter(numbers)\n    return [num for num in numbers if count[num] == 1]", "problem_index": 7, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "b4cd4aa3", "prompt": "\ndef file_name_check(file_name):\n    \"\"\"Create a function which takes a string representing a file's name, and returns\n    'Yes' if the the file's name is valid, and returns 'No' otherwise.\n    A file's name is considered to be valid if and only if all the following conditions \n    are met:\n    - There should not be more than three digits ('0'-'9') in the file's name.\n    - The file's name contains exactly one dot '.'\n    - The substring before the dot should not be empty, and it starts with a letter from \n    the latin alphapet ('a'-'z' and 'A'-'Z').\n    - The substring after the dot should be one of these: ['txt', 'exe', 'dll']\n    Examples:\n    file_name_check(\"example.txt\") # => 'Yes'\n    file_name_check(\"1example.dll\") # => 'No' (the name should start with a latin alphapet letter)\n    \"\"\"\n", "solution": "def file_name_check(file_name):\n    \"\"\"Create a function which takes a string representing a file's name, and returns\n    'Yes' if the the file's name is valid, and returns 'No' otherwise.\n    A file's name is considered to be valid if and only if all the following conditions \n    are met:\n    - There should not be more than three digits ('0'-'9') in the file's name.\n    - The file's name contains exactly one dot '.'\n    - The substring before the dot should not be empty, and it starts with a letter from \n    the latin alphapet ('a'-'z' and 'A'-'Z').\n    - The substring after the dot should be one of these: ['txt', 'exe', 'dll']\n    Examples:\n    file_name_check(\"example.txt\") # => 'Yes'\n    file_name_check(\"1example.dll\") # => 'No' (the name should start with a latin alphapet letter)\n    \"\"\"\n    if not isinstance(file_name, str) or not file_name:\n        return 'No'\n    \n    if file_name.count('.') != 1:\n        return 'No'\n    \n    name_part, extension_part = file_name.split('.')\n    \n    if len(name_part) == 0 or not name_part[0].isalpha():\n        return 'No'\n    \n    if sum(c.isdigit() for c in file_name) > 3:\n        return 'No'\n    \n    if extension_part not in {'txt', 'exe', 'dll'}:\n        return 'No'\n    \n    return 'Yes'", "execution_time": 67.25196886062622, "code": "def file_name_check(file_name):\n    \"\"\"Create a function which takes a string representing a file's name, and returns\n    'Yes' if the the file's name is valid, and returns 'No' otherwise.\n    A file's name is considered to be valid if and only if all the following conditions \n    are met:\n    - There should not be more than three digits ('0'-'9') in the file's name.\n    - The file's name contains exactly one dot '.'\n    - The substring before the dot should not be empty, and it starts with a letter from \n    the latin alphapet ('a'-'z' and 'A'-'Z').\n    - The substring after the dot should be one of these: ['txt', 'exe', 'dll']\n    Examples:\n    file_name_check(\"example.txt\") # => 'Yes'\n    file_name_check(\"1example.dll\") # => 'No' (the name should start with a latin alphapet letter)\n    \"\"\"\n    if not isinstance(file_name, str) or not file_name:\n        return 'No'\n    \n    if file_name.count('.') != 1:\n        return 'No'\n    \n    name_part, extension_part = file_name.split('.')\n    \n    if len(name_part) == 0 or not name_part[0].isalpha():\n        return 'No'\n    \n    if sum(c.isdigit() for c in file_name) > 3:\n        return 'No'\n    \n    if extension_part not in {'txt', 'exe', 'dll'}:\n        return 'No'\n    \n    return 'Yes'", "problem_index": 8, "partial_success": true, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "c56dbb17", "prompt": "\ndef select_words(s, n):\n    \"\"\"Given a string s and a natural number n, you have been tasked to implement \n    a function that returns a list of all words from string s that contain exactly \n    n consonants, in order these words appear in the string s.\n    If the string s is empty then the function should return an empty list.\n    Note: you may assume the input string contains only letters and spaces.\n    Examples:\n    select_words(\"<PERSON> had a little lamb\", 4) ==> [\"little\"]\n    select_words(\"<PERSON> had a little lamb\", 3) ==> [\"<PERSON>\", \"lamb\"]\n    select_words(\"simple white space\", 2) ==> []\n    select_words(\"Hello world\", 4) ==> [\"world\"]\n    select_words(\"Uncle sam\", 3) ==> [\"Uncle\"]\n    \"\"\"\n", "solution": "```python\ndef select_words(s: str, n: int) -> list[str]:\n    \"\"\"\n    Given a string s and a natural number n, returns a list of all words from string s \n    that contain exactly n consonants, in order these words appear in the string s.\n    The function counts consonants only in alphabetic characters.\n\n    Args:\n        s (str): The input string.\n        n (int): The number of consonants.\n\n    Returns:\n        list[str]: A list of words with exactly n consonants.\n\n    Raises:\n        ValueError: If the input string is not a string or the number of consonants is not a non-negative integer.\n    \"\"\"\n    if not isinstance(s, str) or not isinstance(n, int) or n < 0:\n        raise ValueError(\"Invalid input type or negative number of consonants\")\n\n    vowels = set('aeiouAEIOU')\n    words = s.split()\n    result = [word for word in words if word and sum(1 for char in word if char.isalpha() and char.lower() not in vowels) == n]\n    return result\n```", "execution_time": 57.68469190597534, "code": "def select_words(s: str, n: int) -> list[str]:\n    \"\"\"\n    Given a string s and a natural number n, returns a list of all words from string s \n    that contain exactly n consonants, in order these words appear in the string s.\n    The function counts consonants only in alphabetic characters.\n\n    Args:\n        s (str): The input string.\n        n (int): The number of consonants.\n\n    Returns:\n        list[str]: A list of words with exactly n consonants.\n\n    Raises:\n        ValueError: If the input string is not a string or the number of consonants is not a non-negative integer.\n    \"\"\"\n    if not isinstance(s, str) or not isinstance(n, int) or n < 0:\n        raise ValueError(\"Invalid input type or negative number of consonants\")\n\n    vowels = set('aeiouAEIOU')\n    words = s.split()\n    result = [word for word in words if word and sum(1 for char in word if char.isalpha() and char.lower() not in vowels) == n]\n    return result", "problem_index": 9, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "e6533177", "prompt": "\ndef encode(message):\n    \"\"\"\n    Write a function that takes a message, and encodes in such a \n    way that it swaps case of all letters, replaces all vowels in \n    the message with the letter that appears 2 places ahead of that \n    vowel in the english alphabet. \n    Assume only letters. \n    \n    Examples:\n    >>> encode('test')\n    'TGST'\n    >>> encode('This is a message')\n    'tHKS KS C MGSSCGG'\n    \"\"\"\n", "solution": "```python\ndef encode(message: str) -> str:\n    \"\"\"\n    Encodes a message by swapping the case of all letters and replacing all vowels with the letter that appears 2 places ahead of that vowel in the English alphabet.\n\n    Args:\n        message (str): The input message to be encoded.\n\n    Returns:\n        str: The encoded message.\n    \"\"\"\n    if not isinstance(message, str):\n        raise TypeError(\"Input must be a string\")\n\n    if not message:\n        return \"\"\n\n    vowels = 'aeiouAEIOU'\n    vowel_map = str.maketrans({\n        'a': 'c', 'e': 'g', 'i': 'k', 'o': 'q', 'u': 'w',\n        'A': 'C', 'E': 'G', 'I': 'K', 'O': 'Q', 'U': 'W'\n    })\n\n    result = message.translate(vowel_map).swapcase()\n\n    return result\n```", "execution_time": 67.53225517272949, "code": "def encode(message: str) -> str:\n    \"\"\"\n    Encodes a message by swapping the case of all letters and replacing all vowels with the letter that appears 2 places ahead of that vowel in the English alphabet.\n\n    Args:\n        message (str): The input message to be encoded.\n\n    Returns:\n        str: The encoded message.\n    \"\"\"\n    if not isinstance(message, str):\n        raise TypeError(\"Input must be a string\")\n\n    if not message:\n        return \"\"\n\n    vowels = 'aeiouAEIOU'\n    vowel_map = str.maketrans({\n        'a': 'c', 'e': 'g', 'i': 'k', 'o': 'q', 'u': 'w',\n        'A': 'C', 'E': 'G', 'I': 'K', 'O': 'Q', 'U': 'W'\n    })\n\n    result = message.translate(vowel_map).swapcase()\n\n    return result", "problem_index": 10, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "2bde7322", "prompt": "\n\ndef sum_squares(lst):\n    \"\"\"You are given a list of numbers.\n    You need to return the sum of squared numbers in the given list,\n    round each element in the list to the upper int(Ceiling) first.\n    Examples:\n    For lst = [1,2,3] the output should be 14\n    For lst = [1,4,9] the output should be 98\n    For lst = [1,3,5,7] the output should be 84\n    For lst = [1.4,4.2,0] the output should be 29\n    For lst = [-2.4,1,1] the output should be 6\n    \n\n    \"\"\"\n", "solution": "```python\nimport math\n\ndef sum_squares(lst):\n    \"\"\"You are given a list of numbers.\n    You need to return the sum of squared numbers in the given list,\n    round each element in the list to the upper int(Ceiling) first.\n    Examples:\n    For lst = [1,2,3] the output should be 14\n    For lst = [1,4,9] the output should be 98\n    For lst = [1,3,5,7] the output should be 84\n    For lst = [1.4,4.2,0] the output should be 29\n    For lst = [-2.4,1,1] the output should be 6\n    \n\n    \"\"\"\n    if not isinstance(lst, list):\n        raise TypeError(\"Input must be a list.\")\n    try:\n        return sum(map(lambda x: math.ceil(x) ** 2, lst))\n    except TypeError:\n        raise ValueError(\"The list must only contain numeric values.\")\n```", "execution_time": 74.80221104621887, "code": "import math\n\ndef sum_squares(lst):\n    \"\"\"You are given a list of numbers.\n    You need to return the sum of squared numbers in the given list,\n    round each element in the list to the upper int(Ceiling) first.\n    Examples:\n    For lst = [1,2,3] the output should be 14\n    For lst = [1,4,9] the output should be 98\n    For lst = [1,3,5,7] the output should be 84\n    For lst = [1.4,4.2,0] the output should be 29\n    For lst = [-2.4,1,1] the output should be 6\n    \n\n    \"\"\"\n    if not isinstance(lst, list):\n        raise TypeError(\"Input must be a list.\")\n    try:\n        return sum(map(lambda x: math.ceil(x) ** 2, lst))\n    except TypeError:\n        raise ValueError(\"The list must only contain numeric values.\")", "problem_index": 11, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "72a5dbd5", "prompt": "\n\ndef fizz_buzz(n: int):\n    \"\"\"Return the number of times the digit 7 appears in integers less than n which are divisible by 11 or 13.\n    >>> fizz_buzz(50)\n    0\n    >>> fizz_buzz(78)\n    2\n    >>> fizz_buzz(79)\n    3\n    \"\"\"\n", "solution": "```python\ndef fizz_buzz(n: int) -> int:\n    \"\"\"Return the number of times the digit 7 appears in integers less than n which are divisible by 11 or 13.\n    >>> fizz_buzz(50)\n    0\n    >>> fizz_buzz(78)\n    2\n    >>> fizz_buzz(79)\n    3\n    >>> fizz_buzz(0)\n    0\n    >>> fizz_buzz(-10)\n    0\n    >>> fizz_buzz(100)\n    8\n    \"\"\"\n    if not isinstance(n, int) or n < 0:\n        raise ValueError(\"Input must be a non-negative integer.\")\n    \n    if n == 0:\n        return 0\n\n    count = 0\n    for i in range(n):\n        if i % 11 == 0 or i % 13 == 0:\n            count += str(i).count('7')\n    \n    return count\n```", "execution_time": 27.535547256469727, "code": "def fizz_buzz(n: int) -> int:\n    \"\"\"Return the number of times the digit 7 appears in integers less than n which are divisible by 11 or 13.\n    >>> fizz_buzz(50)\n    0\n    >>> fizz_buzz(78)\n    2\n    >>> fizz_buzz(79)\n    3\n    >>> fizz_buzz(0)\n    0\n    >>> fizz_buzz(-10)\n    0\n    >>> fizz_buzz(100)\n    8\n    \"\"\"\n    if not isinstance(n, int) or n < 0:\n        raise ValueError(\"Input must be a non-negative integer.\")\n    \n    if n == 0:\n        return 0\n\n    count = 0\n    for i in range(n):\n        if i % 11 == 0 or i % 13 == 0:\n            count += str(i).count('7')\n    \n    return count", "problem_index": 12, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "d5fbe517", "prompt": "\ndef prime_length(string):\n    \"\"\"Write a function that takes a string and returns True if the string\n    length is a prime number or False otherwise\n    Examples\n    prime_length('Hello') == True\n    prime_length('abcdcba') == True\n    prime_length('kittens') == True\n    prime_length('orange') == False\n    \"\"\"\n", "solution": "```python\ndef is_prime(n):\n    if n <= 1:\n        return False\n    for i in range(2, int(n**0.5) + 1):\n        if n % i == 0:\n            return False\n    return True\n\ndef prime_length(string):\n    if not isinstance(string, str):\n        raise ValueError(\"Input must be a string.\")\n    if not string:\n        return False\n    length = len(string)\n    return is_prime(length)\n```", "execution_time": 18.62409281730652, "code": "def is_prime(n):\n    if n <= 1:\n        return False\n    for i in range(2, int(n**0.5) + 1):\n        if n % i == 0:\n            return False\n    return True\n\ndef prime_length(string):\n    if not isinstance(string, str):\n        raise ValueError(\"Input must be a string.\")\n    if not string:\n        return False\n    length = len(string)\n    return is_prime(length)", "problem_index": 13, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "e176dc69", "prompt": "from typing import List, Any\n\n\ndef filter_integers(values: List[Any]) -> List[int]:\n    \"\"\" Filter given list of any python values only for integers\n    >>> filter_integers(['a', 3.14, 5])\n    [5]\n    >>> filter_integers([1, 2, 3, 'abc', {}, []])\n    [1, 2, 3]\n    \"\"\"\n", "solution": "```python\ndef filter_integers(values: List[Any]) -> List[int]:\n    \"\"\" Filter given list of any python values only for integers\n    >>> filter_integers(['a', 3.14, 5])\n    [5]\n    >>> filter_integers([1, 2, 3, 'abc', {}, []])\n    [1, 2, 3]\n    \"\"\"\n    if not isinstance(values, list):\n        raise ValueError(\"Input must be a list.\")\n    \n    return [value for value in values if isinstance(value, int)]\n```", "execution_time": 22.866164922714233, "code": "def filter_integers(values: List[Any]) -> List[int]:\n    \"\"\" Filter given list of any python values only for integers\n    >>> filter_integers(['a', 3.14, 5])\n    [5]\n    >>> filter_integers([1, 2, 3, 'abc', {}, []])\n    [1, 2, 3]\n    \"\"\"\n    if not isinstance(values, list):\n        raise ValueError(\"Input must be a list.\")\n    \n    return [value for value in values if isinstance(value, int)]", "problem_index": 14, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "5ed5ce43", "prompt": "\ndef is_sorted(lst):\n    '''\n    Given a list of numbers, return whether or not they are sorted\n    in ascending order. If list has more than 1 duplicate of the same\n    number, return False. Assume no negative numbers and only integers.\n\n    Examples\n    is_sorted([5]) ➞ True\n    is_sorted([1, 2, 3, 4, 5]) ➞ True\n    is_sorted([1, 3, 2, 4, 5]) ➞ False\n    is_sorted([1, 2, 3, 4, 5, 6]) ➞ True\n    is_sorted([1, 2, 3, 4, 5, 6, 7]) ➞ True\n    is_sorted([1, 3, 2, 4, 5, 6, 7]) ➞ False\n    is_sorted([1, 2, 2, 3, 3, 4]) ➞ True\n    is_sorted([1, 2, 2, 2, 3, 4]) ➞ False\n    '''\n", "solution": "def is_sorted(lst):\n    if len(lst) <= 1:\n        return True\n\n    for i in range(1, len(lst)):\n        if lst[i] < lst[i-1]:\n            return False\n\n    return True", "execution_time": 68.15522980690002, "code": "def is_sorted(lst):\n    if len(lst) <= 1:\n        return True\n\n    for i in range(1, len(lst)):\n        if lst[i] < lst[i-1]:\n            return False\n\n    return True", "problem_index": 15, "partial_success": true, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "641f743f", "prompt": "\ndef fruit_distribution(s,n):\n    \"\"\"\n    In this task, you will be given a string that represents a number of apples and oranges \n    that are distributed in a basket of fruit this basket contains \n    apples, oranges, and mango fruits. Given the string that represents the total number of \n    the oranges and apples and an integer that represent the total number of the fruits \n    in the basket return the number of the mango fruits in the basket.\n    for examble:\n    fruit_distribution(\"5 apples and 6 oranges\", 19) ->19 - 5 - 6 = 8\n    fruit_distribution(\"0 apples and 1 oranges\",3) -> 3 - 0 - 1 = 2\n    fruit_distribution(\"2 apples and 3 oranges\", 100) -> 100 - 2 - 3 = 95\n    fruit_distribution(\"100 apples and 1 oranges\",120) -> 120 - 100 - 1 = 19\n    \"\"\"\n", "solution": "```python\ndef fruit_distribution(s: str, n: int) -> int:\n    \"\"\"\n    Calculates the number of mangoes given a string containing the number of apples and oranges, and the total number of fruits.\n\n    Args:\n        s (str): A string containing the number of apples and oranges.\n                 The string should contain phrases like \"X apples\" or \"Y oranges\",\n                 where X and Y are non-negative integers.\n        n (int): The total number of fruits.\n\n    Returns:\n        int: The number of mangoes.\n\n    Raises:\n        ValueError: If the total number of fruits is less than the sum of apples and oranges,\n                    or if the total number of fruits is not a non-negative integer.\n    \"\"\"\n    import re\n\n    if not isinstance(n, int) or n < 0:\n        raise ValueError(\"The total number of fruits must be a non-negative integer.\")\n    \n    # Extract numbers of apples and oranges from the string\n    apples = sum(map(int, re.findall(r'(\\d+)\\s*apples?', s)))\n    oranges = sum(map(int, re.findall(r'(\\d+)\\s*oranges?', s)))\n    \n    # Calculate the number of mangoes\n    mangoes = n - apples - oranges\n    \n    # Ensure the result is not negative\n    if mangoes < 0:\n        raise ValueError(\"The total number of fruits cannot be less than the sum of apples and oranges.\")\n    \n    return mangoes\n```", "execution_time": 63.51117420196533, "code": "def fruit_distribution(s: str, n: int) -> int:\n    \"\"\"\n    Calculates the number of mangoes given a string containing the number of apples and oranges, and the total number of fruits.\n\n    Args:\n        s (str): A string containing the number of apples and oranges.\n                 The string should contain phrases like \"X apples\" or \"Y oranges\",\n                 where X and Y are non-negative integers.\n        n (int): The total number of fruits.\n\n    Returns:\n        int: The number of mangoes.\n\n    Raises:\n        ValueError: If the total number of fruits is less than the sum of apples and oranges,\n                    or if the total number of fruits is not a non-negative integer.\n    \"\"\"\n    import re\n\n    if not isinstance(n, int) or n < 0:\n        raise ValueError(\"The total number of fruits must be a non-negative integer.\")\n    \n    # Extract numbers of apples and oranges from the string\n    apples = sum(map(int, re.findall(r'(\\d+)\\s*apples?', s)))\n    oranges = sum(map(int, re.findall(r'(\\d+)\\s*oranges?', s)))\n    \n    # Calculate the number of mangoes\n    mangoes = n - apples - oranges\n    \n    # Ensure the result is not negative\n    if mangoes < 0:\n        raise ValueError(\"The total number of fruits cannot be less than the sum of apples and oranges.\")\n    \n    return mangoes", "problem_index": 16, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "3ec96284", "prompt": "\ndef multiply(a, b):\n    \"\"\"Complete the function that takes two integers and returns \n    the product of their unit digits.\n    Assume the input is always valid.\n    Examples:\n    multiply(148, 412) should return 16.\n    multiply(19, 28) should return 72.\n    multiply(2020, 1851) should return 0.\n    multiply(14,-15) should return 20.\n    \"\"\"\n", "solution": "```python\ndef multiply(a, b):\n    \"\"\"Complete the function that takes two integers and returns \n    the product of their unit digits.\n    Assume the input is always valid.\n    Examples:\n    multiply(148, 412) should return 16.\n    multiply(19, 28) should return 72.\n    multiply(2020, 1851) should return 0.\n    multiply(14,-15) should return 20.\n    Note: Unit digits are considered absolute, so negative inputs are treated as their positive equivalents. The unit digit is always considered positive.\n    \"\"\"\n    return abs(a % 10) * abs(b % 10)\n```", "execution_time": 16.485180139541626, "code": "def multiply(a, b):\n    \"\"\"Complete the function that takes two integers and returns \n    the product of their unit digits.\n    Assume the input is always valid.\n    Examples:\n    multiply(148, 412) should return 16.\n    multiply(19, 28) should return 72.\n    multiply(2020, 1851) should return 0.\n    multiply(14,-15) should return 20.\n    Note: Unit digits are considered absolute, so negative inputs are treated as their positive equivalents. The unit digit is always considered positive.\n    \"\"\"\n    return abs(a % 10) * abs(b % 10)", "problem_index": 17, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "18740711", "prompt": "from typing import List\n\n\ndef string_xor(a: str, b: str) -> str:\n    \"\"\" Input are two strings a and b consisting only of 1s and 0s.\n    Perform binary XOR on these inputs and return result also as a string.\n    >>> string_xor('010', '110')\n    '100'\n    \"\"\"\n", "solution": "```python\ndef string_xor(a: str, b: str) -> str:\n    \"\"\" Input are two strings a and b consisting only of 1s and 0s.\n    Perform binary XOR on these inputs and return result also as a string.\n    :param a: str - First binary string\n    :param b: str - Second binary string\n    :return: str - XOR result as a binary string\n    >>> string_xor('010', '110')\n    '100'\n    >>> string_xor('111', '000')\n    '111'\n    >>> string_xor('000', '000')\n    '000'\n    >>> string_xor('101010', '010101')\n    '111111'\n    >>> string_xor('1', '1')\n    '0'\n    >>> string_xor('1', '0')\n    '1'\n    >>> string_xor('0', '1')\n    '1'\n    >>> string_xor('1101', '1011')\n    '0110'\n    >>> string_xor('', '')\n    ''\n    >>> string_xor('110', '1010')\n    Traceback (most recent call last):\n        ...\n    ValueError: Strings must be of the same length\n    \"\"\"\n    for bit in a + b:\n        if bit not in '01':\n            raise ValueError(\"Strings must consist only of '1's and '0's\")\n\n    if len(a) != len(b):\n        raise ValueError(\"Strings must be of the same length\")\n    \n    result = []\n    for bit_a, bit_b in zip(a, b):\n        result.append(str(int(bit_a) ^ int(bit_b)))\n    \n    return ''.join(result)\n```", "execution_time": 30.419105052947998, "code": "def string_xor(a: str, b: str) -> str:\n    \"\"\" Input are two strings a and b consisting only of 1s and 0s.\n    Perform binary XOR on these inputs and return result also as a string.\n    :param a: str - First binary string\n    :param b: str - Second binary string\n    :return: str - XOR result as a binary string\n    >>> string_xor('010', '110')\n    '100'\n    >>> string_xor('111', '000')\n    '111'\n    >>> string_xor('000', '000')\n    '000'\n    >>> string_xor('101010', '010101')\n    '111111'\n    >>> string_xor('1', '1')\n    '0'\n    >>> string_xor('1', '0')\n    '1'\n    >>> string_xor('0', '1')\n    '1'\n    >>> string_xor('1101', '1011')\n    '0110'\n    >>> string_xor('', '')\n    ''\n    >>> string_xor('110', '1010')\n    Traceback (most recent call last):\n        ...\n    ValueError: Strings must be of the same length\n    \"\"\"\n    for bit in a + b:\n        if bit not in '01':\n            raise ValueError(\"Strings must consist only of '1's and '0's\")\n\n    if len(a) != len(b):\n        raise ValueError(\"Strings must be of the same length\")\n    \n    result = []\n    for bit_a, bit_b in zip(a, b):\n        result.append(str(int(bit_a) ^ int(bit_b)))\n    \n    return ''.join(result)", "problem_index": 18, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "2ceafd51", "prompt": "\ndef circular_shift(x, shift):\n    \"\"\"Circular shift the digits of the integer x, shift the digits right by shift\n    and return the result as a string.\n    If shift > number of digits, return digits reversed.\n    >>> circular_shift(12, 1)\n    \"21\"\n    >>> circular_shift(12, 2)\n    \"12\"\n    \"\"\"\n", "solution": "```python\ndef circular_shift(x, shift):\n    \"\"\"Circular shift the digits of the integer x, shift the digits right by shift\n    and return the result as a string. Handles negative integers by preserving the sign and shifting the digits.\n    If shift > number of digits, return digits reversed.\n    >>> circular_shift(12, 1)\n    \"21\"\n    >>> circular_shift(12, 2)\n    \"12\"\n    >>> circular_shift(12345, 1)\n    \"51234\"\n    >>> circular_shift(12345, 5)\n    \"54321\"\n    >>> circular_shift(12345, 6)\n    \"54321\"\n    >>> circular_shift(0, 1)\n    \"0\"\n    >>> circular_shift(-123, 1)\n    \"-321\"\n    >>> circular_shift(-123, 3)\n    \"-123\"\n    >>> circular_shift(-123, 4)\n    \"-321\"\n    \"\"\"\n    if not isinstance(x, int):\n        raise TypeError(\"Input x must be an integer.\")\n    if not isinstance(shift, int):\n        raise TypeError(\"Input shift must be an integer.\")\n    \n    is_negative = x < 0\n    x_str = str(abs(x))\n    num_digits = len(x_str)\n    \n    if num_digits == 0:\n        return \"0\"\n    \n    shift = shift % num_digits\n    \n    if shift == 0:\n        result = x_str\n    elif shift >= num_digits:\n        result = x_str[::-1]\n    else:\n        result = x_str[-shift:] + x_str[:-shift]\n    \n    return f\"-{result}\" if is_negative else result\n```", "execution_time": 31.528778076171875, "code": "def circular_shift(x, shift):\n    \"\"\"Circular shift the digits of the integer x, shift the digits right by shift\n    and return the result as a string. Handles negative integers by preserving the sign and shifting the digits.\n    If shift > number of digits, return digits reversed.\n    >>> circular_shift(12, 1)\n    \"21\"\n    >>> circular_shift(12, 2)\n    \"12\"\n    >>> circular_shift(12345, 1)\n    \"51234\"\n    >>> circular_shift(12345, 5)\n    \"54321\"\n    >>> circular_shift(12345, 6)\n    \"54321\"\n    >>> circular_shift(0, 1)\n    \"0\"\n    >>> circular_shift(-123, 1)\n    \"-321\"\n    >>> circular_shift(-123, 3)\n    \"-123\"\n    >>> circular_shift(-123, 4)\n    \"-321\"\n    \"\"\"\n    if not isinstance(x, int):\n        raise TypeError(\"Input x must be an integer.\")\n    if not isinstance(shift, int):\n        raise TypeError(\"Input shift must be an integer.\")\n    \n    is_negative = x < 0\n    x_str = str(abs(x))\n    num_digits = len(x_str)\n    \n    if num_digits == 0:\n        return \"0\"\n    \n    shift = shift % num_digits\n    \n    if shift == 0:\n        result = x_str\n    elif shift >= num_digits:\n        result = x_str[::-1]\n    else:\n        result = x_str[-shift:] + x_str[:-shift]\n    \n    return f\"-{result}\" if is_negative else result", "problem_index": 19, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "82995b23", "prompt": "\ndef anti_shuffle(s):\n    \"\"\"\n    Write a function that takes a string and returns an ordered version of it.\n    Ordered version of string, is a string where all words (separated by space)\n    are replaced by a new word where all the characters arranged in\n    ascending order based on ascii value.\n    Note: You should keep the order of words and blank spaces in the sentence.\n\n    For example:\n    anti_shuffle('Hi') returns 'Hi'\n    anti_shuffle('hello') returns 'ehllo'\n    anti_shuffle('Hello World!!!') returns 'Hello !!!Wdlor'\n    \"\"\"\n", "solution": "```python\ndef anti_shuffle(s):\n    if not isinstance(s, str):\n        raise ValueError(\"Input must be a string\")\n    \n    ordered_words = [''.join(sorted(word)) if word else '' for word in s.split(' ')]\n    \n    return ' '.join(ordered_words)\n```", "execution_time": 60.90058994293213, "code": "def anti_shuffle(s):\n    if not isinstance(s, str):\n        raise ValueError(\"Input must be a string\")\n    \n    ordered_words = [''.join(sorted(word)) if word else '' for word in s.split(' ')]\n    \n    return ' '.join(ordered_words)", "problem_index": 20, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "30662102", "prompt": "from typing import List\n\n\ndef parse_nested_parens(paren_string: str) -> List[int]:\n    \"\"\" Input to this function is a string represented multiple groups for nested parentheses separated by spaces.\n    For each of the group, output the deepest level of nesting of parentheses.\n    E.g. (()()) has maximum two levels of nesting while ((())) has three.\n\n    >>> parse_nested_parens('(()()) ((())) () ((())()())')\n    [2, 3, 1, 3]\n    \"\"\"\n", "solution": "```python\nfrom typing import List\n\ndef parse_nested_parens(paren_string: str) -> List[int]:\n    if not isinstance(paren_string, str):\n        raise ValueError(\"Input must be a string.\")\n    \n    for char in paren_string:\n        if char not in ['(', ')', ' ']:\n            raise ValueError(\"Input string contains invalid characters. Only parentheses and whitespace are allowed.\")\n    \n    groups = paren_string.split()\n    depths = []\n    \n    for group in groups:\n        max_depth = 0\n        current_depth = 0\n        \n        for char in group:\n            if char == '(':\n                current_depth += 1\n                max_depth = max(max_depth, current_depth)\n            elif char == ')':\n                current_depth -= 1\n                if current_depth < 0:\n                    raise ValueError(\"Unmatched closing parenthesis.\")\n        \n        if current_depth != 0:\n            raise ValueError(\"Unmatched opening parenthesis.\")\n        \n        depths.append(max_depth)\n    \n    return depths\n```", "execution_time": 64.55771112442017, "code": "from typing import List\n\ndef parse_nested_parens(paren_string: str) -> List[int]:\n    if not isinstance(paren_string, str):\n        raise ValueError(\"Input must be a string.\")\n    \n    for char in paren_string:\n        if char not in ['(', ')', ' ']:\n            raise ValueError(\"Input string contains invalid characters. Only parentheses and whitespace are allowed.\")\n    \n    groups = paren_string.split()\n    depths = []\n    \n    for group in groups:\n        max_depth = 0\n        current_depth = 0\n        \n        for char in group:\n            if char == '(':\n                current_depth += 1\n                max_depth = max(max_depth, current_depth)\n            elif char == ')':\n                current_depth -= 1\n                if current_depth < 0:\n                    raise ValueError(\"Unmatched closing parenthesis.\")\n        \n        if current_depth != 0:\n            raise ValueError(\"Unmatched opening parenthesis.\")\n        \n        depths.append(max_depth)\n    \n    return depths", "problem_index": 21, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "7a7d9208", "prompt": "\n\ndef flip_case(string: str) -> str:\n    \"\"\" For a given string, flip lowercase characters to uppercase and uppercase to lowercase.\n    >>> flip_case('Hello')\n    'hELLO'\n    \"\"\"\n", "solution": "```python\ndef flip_case(string: str) -> str:\n    \"\"\" For a given string, flip lowercase characters to uppercase and uppercase to lowercase.\n    >>> flip_case('Hello')\n    'hELLO'\n    \"\"\"\n    if not isinstance(string, str):\n        raise ValueError(\"Input must be a string\")\n    return string.swapcase()\n```", "execution_time": 20.981563806533813, "code": "def flip_case(string: str) -> str:\n    \"\"\" For a given string, flip lowercase characters to uppercase and uppercase to lowercase.\n    >>> flip_case('Hello')\n    'hELLO'\n    \"\"\"\n    if not isinstance(string, str):\n        raise ValueError(\"Input must be a string\")\n    return string.swapcase()", "problem_index": 22, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "ae8b8b3c", "prompt": "\ndef is_simple_power(x, n):\n    \"\"\"Your task is to write a function that returns true if a number x is a simple\n    power of n and false in other cases.\n    x is a simple power of n if n**int=x\n    For example:\n    is_simple_power(1, 4) => true\n    is_simple_power(2, 2) => true\n    is_simple_power(8, 2) => true\n    is_simple_power(3, 2) => false\n    is_simple_power(3, 1) => false\n    is_simple_power(5, 3) => false\n    \"\"\"\n", "solution": "```python\ndef is_simple_power(x: int, n: int) -> bool:\n    \"\"\"\n    Checks if a number x is a simple power of n.\n    \n    A number x is a simple power of n if n raised to some integer equals x.\n    \n    Args:\n    x (int): The number to check.\n    n (int): The base number.\n    \n    Returns:\n    bool: True if x is a simple power of n, False otherwise.\n    \"\"\"\n    if not isinstance(x, int) or not isinstance(n, int):\n        raise TypeError(\"Both x and n must be integers.\")\n    if x <= 0 or n <= 0 or n == 1:\n        return x == 1\n    import math\n    log_value = math.log(x, n)\n    return math.isclose(round(log_value), log_value)\n```", "execution_time": 56.37714195251465, "code": "def is_simple_power(x: int, n: int) -> bool:\n    \"\"\"\n    Checks if a number x is a simple power of n.\n    \n    A number x is a simple power of n if n raised to some integer equals x.\n    \n    Args:\n    x (int): The number to check.\n    n (int): The base number.\n    \n    Returns:\n    bool: True if x is a simple power of n, False otherwise.\n    \"\"\"\n    if not isinstance(x, int) or not isinstance(n, int):\n        raise TypeError(\"Both x and n must be integers.\")\n    if x <= 0 or n <= 0 or n == 1:\n        return x == 1\n    import math\n    log_value = math.log(x, n)\n    return math.isclose(round(log_value), log_value)", "problem_index": 23, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "b777d329", "prompt": "\n\n\ndef sum_squares(lst):\n    \"\"\"\"\n    This function will take a list of integers. For all entries in the list, the function shall square the integer entry if its index is a \n    multiple of 3 and will cube the integer entry if its index is a multiple of 4 and not a multiple of 3. The function will not \n    change the entries in the list whose indexes are not a multiple of 3 or 4. The function shall then return the sum of all entries. \n    \n    Examples:\n    For lst = [1,2,3] the output should be 6\n    For lst = []  the output should be 0\n    For lst = [-1,-5,2,-1,-5]  the output should be -126\n    \"\"\"\n", "solution": "def sum_squares(lst):\n    if not isinstance(lst, list):\n        raise ValueError(\"Input must be a list.\")\n    \n    total_sum = 0\n    for index, value in enumerate(lst):\n        if not isinstance(value, int):\n            raise ValueError(\"All elements in the list must be integers.\")\n        \n        if index % 3 == 0:\n            total_sum += value ** 2\n        elif index % 4 == 0 and index % 3 != 0:\n            total_sum += value ** 3\n        else:\n            total_sum += value\n            \n    return total_sum", "execution_time": 59.45285987854004, "code": "def sum_squares(lst):\n    if not isinstance(lst, list):\n        raise ValueError(\"Input must be a list.\")\n    \n    total_sum = 0\n    for index, value in enumerate(lst):\n        if not isinstance(value, int):\n            raise ValueError(\"All elements in the list must be integers.\")\n        \n        if index % 3 == 0:\n            total_sum += value ** 2\n        elif index % 4 == 0 and index % 3 != 0:\n            total_sum += value ** 3\n        else:\n            total_sum += value\n            \n    return total_sum", "problem_index": 24, "partial_success": true, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "ab815ee2", "prompt": "\n\ndef encode_cyclic(s: str):\n    \"\"\"\n    returns encoded string by cycling groups of three characters.\n    \"\"\"\n    # split string to groups. Each of length 3.\n    groups = [s[(3 * i):min((3 * i + 3), len(s))] for i in range((len(s) + 2) // 3)]\n    # cycle elements in each group. Unless group has fewer elements than 3.\n    groups = [(group[1:] + group[0]) if len(group) == 3 else group for group in groups]\n    return \"\".join(groups)\n\n\ndef decode_cyclic(s: str):\n    \"\"\"\n    takes as input string encoded with encode_cyclic function. Returns decoded string.\n    \"\"\"\n", "solution": "```python\ndef encode_cyclic(s: str) -> str:\n    \"\"\"\n    Returns encoded string by cycling groups of three characters.\n    \"\"\"\n    if not isinstance(s, str):\n        raise TypeError(\"Input must be a string.\")\n    if len(s) == 0:\n        return \"\"\n    \n    groups = [s[i:i + 3] for i in range(0, len(s), 3)]\n    encoded_groups = [(group[1:] + group[0:1]) if len(group) == 3 else group for group in groups]\n    return \"\".join(encoded_groups)\n\n\ndef decode_cyclic(s: str) -> str:\n    \"\"\"\n    Takes as input string encoded with encode_cyclic function. Returns decoded string.\n    \"\"\"\n    if not isinstance(s, str):\n        raise TypeError(\"Input must be a string.\")\n    if len(s) == 0:\n        return \"\"\n    \n    groups = [s[i:i + 3] for i in range(0, len(s), 3)]\n    decoded_groups = [(group[-1] + group[:-1]) if len(group) == 3 else group for group in groups]\n    return \"\".join(decoded_groups)\n```", "execution_time": 75.68791890144348, "code": "def encode_cyclic(s: str) -> str:\n    \"\"\"\n    Returns encoded string by cycling groups of three characters.\n    \"\"\"\n    if not isinstance(s, str):\n        raise TypeError(\"Input must be a string.\")\n    if len(s) == 0:\n        return \"\"\n    \n    groups = [s[i:i + 3] for i in range(0, len(s), 3)]\n    encoded_groups = [(group[1:] + group[0:1]) if len(group) == 3 else group for group in groups]\n    return \"\".join(encoded_groups)\n\n\ndef decode_cyclic(s: str) -> str:\n    \"\"\"\n    Takes as input string encoded with encode_cyclic function. Returns decoded string.\n    \"\"\"\n    if not isinstance(s, str):\n        raise TypeError(\"Input must be a string.\")\n    if len(s) == 0:\n        return \"\"\n    \n    groups = [s[i:i + 3] for i in range(0, len(s), 3)]\n    decoded_groups = [(group[-1] + group[:-1]) if len(group) == 3 else group for group in groups]\n    return \"\".join(decoded_groups)", "problem_index": 25, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "13d1d7f3", "prompt": "\n\ndef car_race_collision(n: int):\n    \"\"\"\n    Imagine a road that's a perfectly straight infinitely long line.\n    n cars are driving left to right;  simultaneously, a different set of n cars\n    are driving right to left.   The two sets of cars start out being very far from\n    each other.  All cars move in the same speed.  Two cars are said to collide\n    when a car that's moving left to right hits a car that's moving right to left.\n    However, the cars are infinitely sturdy and strong; as a result, they continue moving\n    in their trajectory as if they did not collide.\n\n    This function outputs the number of such collisions.\n    \"\"\"\n", "solution": "```python\ndef car_race_collision(n: int) -> int:\n    \"\"\"\n    Calculate the number of collisions between two sets of cars moving in opposite directions.\n\n    Args:\n    n (int): The number of cars in each set.\n\n    Returns:\n    int: The number of collisions.\n\n    Raises:\n    ValueError: If the input is not a non-negative integer.\n    \"\"\"\n    if not isinstance(n, int) or n < 0:\n        raise ValueError(\"Input must be a non-negative integer.\")\n    return n * n\n```", "execution_time": 68.11974501609802, "code": "def car_race_collision(n: int) -> int:\n    \"\"\"\n    Calculate the number of collisions between two sets of cars moving in opposite directions.\n\n    Args:\n    n (int): The number of cars in each set.\n\n    Returns:\n    int: The number of collisions.\n\n    Raises:\n    ValueError: If the input is not a non-negative integer.\n    \"\"\"\n    if not isinstance(n, int) or n < 0:\n        raise ValueError(\"Input must be a non-negative integer.\")\n    return n * n", "problem_index": 26, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "24da64c4", "prompt": "from typing import List\n\n\ndef mean_absolute_deviation(numbers: List[float]) -> float:\n    \"\"\" For a given list of input numbers, calculate Mean Absolute Deviation\n    around the mean of this dataset.\n    Mean Absolute Deviation is the average absolute difference between each\n    element and a centerpoint (mean in this case):\n    MAD = average | x - x_mean |\n    >>> mean_absolute_deviation([1.0, 2.0, 3.0, 4.0])\n    1.0\n    \"\"\"\n", "solution": "```python\nfrom typing import List\nimport statistics\n\ndef mean_absolute_deviation(numbers: List[float]) -> float:\n    \"\"\"\n    For a given list of input numbers, calculate Mean Absolute Deviation\n    around the mean of this dataset.\n    Mean Absolute Deviation is the average absolute difference between each\n    element and a centerpoint (mean in this case):\n    MAD = average | x - x_mean |\n\n    :param numbers: List[float] - A list of numerical values.\n    :return: float - The Mean Absolute Deviation of the input list.\n\n    For example:\n    mean_absolute_deviation([1.0, 2.0, 3.0, 4.0]) == 1.0\n\n    The mathematical formula is:\n    MAD = (1/n) * Σ |x_i - μ|, where n is the number of data points,\n    x_i is each data point, and μ is the mean of the data.\n\n    Examples:\n    >>> mean_absolute_deviation([1.0, 2.0, 3.0, 4.0, 5.0])\n    1.2\n    >>> mean_absolute_deviation([2.0, 4.0, 6.0, 8.0])\n    2.0\n    \"\"\"\n    if not numbers:\n        raise ValueError(\"Input list cannot be empty\")\n\n    if not all(isinstance(num, (int, float)) for num in numbers):\n        raise TypeError(\"All elements in the list must be numbers\")\n\n    mean = statistics.mean(numbers)\n    absolute_deviations = [abs(num - mean) for num in numbers]\n    mad = statistics.mean(absolute_deviations)\n\n    return mad\n```", "execution_time": 62.26671600341797, "code": "from typing import List\nimport statistics\n\ndef mean_absolute_deviation(numbers: List[float]) -> float:\n    \"\"\"\n    For a given list of input numbers, calculate Mean Absolute Deviation\n    around the mean of this dataset.\n    Mean Absolute Deviation is the average absolute difference between each\n    element and a centerpoint (mean in this case):\n    MAD = average | x - x_mean |\n\n    :param numbers: List[float] - A list of numerical values.\n    :return: float - The Mean Absolute Deviation of the input list.\n\n    For example:\n    mean_absolute_deviation([1.0, 2.0, 3.0, 4.0]) == 1.0\n\n    The mathematical formula is:\n    MAD = (1/n) * Σ |x_i - μ|, where n is the number of data points,\n    x_i is each data point, and μ is the mean of the data.\n\n    Examples:\n    >>> mean_absolute_deviation([1.0, 2.0, 3.0, 4.0, 5.0])\n    1.2\n    >>> mean_absolute_deviation([2.0, 4.0, 6.0, 8.0])\n    2.0\n    \"\"\"\n    if not numbers:\n        raise ValueError(\"Input list cannot be empty\")\n\n    if not all(isinstance(num, (int, float)) for num in numbers):\n        raise TypeError(\"All elements in the list must be numbers\")\n\n    mean = statistics.mean(numbers)\n    absolute_deviations = [abs(num - mean) for num in numbers]\n    mad = statistics.mean(absolute_deviations)\n\n    return mad", "problem_index": 27, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "867c86d9", "prompt": "\ndef is_equal_to_sum_even(n):\n    \"\"\"Evaluate whether the given number n can be written as the sum of exactly 4 positive even numbers\n    Example\n    is_equal_to_sum_even(4) == False\n    is_equal_to_sum_even(6) == False\n    is_equal_to_sum_even(8) == True\n    \"\"\"\n", "solution": "```python\ndef is_equal_to_sum_even(n):\n    \"\"\"Evaluate whether the given number n can be written as the sum of exactly 4 positive even numbers\n    Example\n    is_equal_to_sum_even(4) == False\n    is_equal_to_sum_even(6) == False\n    is_equal_to_sum_even(8) == True\n    \"\"\"\n    if not isinstance(n, int) or n < 0:\n        raise ValueError(\"Input must be a non-negative integer\")\n    if n < 8:\n        return False\n    def recursive_sum(target, count, current_sum=0, start=2):\n        if current_sum == target and count == 0:\n            return True\n        if current_sum > target or count < 0:\n            return False\n        for i in range(start, target + 1, 2):\n            if recursive_sum(target, count - 1, current_sum + i, i + 2):\n                return True\n        return False\n    return recursive_sum(n, 4)\n```", "execution_time": 23.804054021835327, "code": "def is_equal_to_sum_even(n):\n    \"\"\"Evaluate whether the given number n can be written as the sum of exactly 4 positive even numbers\n    Example\n    is_equal_to_sum_even(4) == False\n    is_equal_to_sum_even(6) == False\n    is_equal_to_sum_even(8) == True\n    \"\"\"\n    if not isinstance(n, int) or n < 0:\n        raise ValueError(\"Input must be a non-negative integer\")\n    if n < 8:\n        return False\n    def recursive_sum(target, count, current_sum=0, start=2):\n        if current_sum == target and count == 0:\n            return True\n        if current_sum > target or count < 0:\n            return False\n        for i in range(start, target + 1, 2):\n            if recursive_sum(target, count - 1, current_sum + i, i + 2):\n                return True\n        return False\n    return recursive_sum(n, 4)", "problem_index": 28, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "ede918de", "prompt": "import math\n\n\ndef poly(xs: list, x: float):\n    \"\"\"\n    Evaluates polynomial with coefficients xs at point x.\n    return xs[0] + xs[1] * x + xs[1] * x^2 + .... xs[n] * x^n\n    \"\"\"\n    return sum([coeff * math.pow(x, i) for i, coeff in enumerate(xs)])\n\n\ndef find_zero(xs: list):\n    \"\"\" xs are coefficients of a polynomial.\n    find_zero find x such that poly(x) = 0.\n    find_zero returns only only zero point, even if there are many.\n    Moreover, find_zero only takes list xs having even number of coefficients\n    and largest non zero coefficient as it guarantees\n    a solution.\n    >>> round(find_zero([1, 2]), 2) # f(x) = 1 + 2x\n    -0.5\n    >>> round(find_zero([-6, 11, -6, 1]), 2) # (x - 1) * (x - 2) * (x - 3) = -6 + 11x - 6x^2 + x^3\n    1.0\n    \"\"\"\n", "solution": "import math\nimport numpy as np\n\ndef poly(xs: list, x: float):\n    \"\"\"\n    Evaluates polynomial with coefficients xs at point x.\n    return xs[0] + xs[1] * x + xs[2] * x^2 + ... + xs[n] * x^n\n    \"\"\"\n    return sum([coeff * math.pow(x, i) for i, coeff in enumerate(xs)])\n\ndef find_zero(xs: list):\n    \"\"\"\n    xs are coefficients of a polynomial.\n    find_zero find x such that poly(x) = 0.\n    find_zero returns only one zero point, even if there are many.\n    Moreover, find_zero only takes list xs having even number of coefficients\n    and largest non zero coefficient as it guarantees a solution.\n    \"\"\"\n    if len(xs) % 2 != 0:\n        raise ValueError(\"The number of coefficients must be even.\")\n    \n    if not any(xs):\n        raise ValueError(\"All coefficients cannot be zero.\")\n    \n    if xs[-1] == 0:\n        raise ValueError(\"The largest coefficient must be non-zero.\")\n    \n    roots = np.roots(xs[::-1])\n    real_roots = [root.real for root in roots if abs(root.imag) < 1e-10]\n    \n    if real_roots:\n        return real_roots[0]\n    else:\n        raise ValueError(\"No real roots found.\")", "execution_time": 71.46828579902649, "code": "def find_zero(xs: list):\n    \"\"\"\n    xs are coefficients of a polynomial.\n    find_zero find x such that poly(x) = 0.\n    find_zero returns only one zero point, even if there are many.\n    Moreover, find_zero only takes list xs having even number of coefficients\n    and largest non zero coefficient as it guarantees a solution.\n    \"\"\"\n    if len(xs) % 2 != 0:\n        raise ValueError(\"The number of coefficients must be even.\")\n    \n    if not any(xs):\n        raise ValueError(\"All coefficients cannot be zero.\")\n    \n    if xs[-1] == 0:\n        raise ValueError(\"The largest coefficient must be non-zero.\")\n    \n    roots = np.roots(xs[::-1])\n    real_roots = [root.real for root in roots if abs(root.imag) < 1e-10]\n    \n    if real_roots:\n        return real_roots[0]\n    else:\n        raise ValueError(\"No real roots found.\")", "problem_index": 29, "partial_success": true, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "f60a7d2f", "prompt": "\ndef simplify(x, n):\n    \"\"\"Your task is to implement a function that will simplify the expression\n    x * n. The function returns True if x * n evaluates to a whole number and False\n    otherwise. Both x and n, are string representation of a fraction, and have the following format,\n    <numerator>/<denominator> where both numerator and denominator are positive whole numbers.\n\n    You can assume that x, and n are valid fractions, and do not have zero as denominator.\n\n    simplify(\"1/5\", \"5/1\") = True\n    simplify(\"1/6\", \"2/1\") = False\n    simplify(\"7/10\", \"10/2\") = False\n    \"\"\"\n", "solution": "```python\nfrom fractions import Fraction\n\ndef simplify(x, n):\n    \"\"\"Your task is to implement a function that will simplify the expression\n    x * n. The function returns True if x * n evaluates to a whole number and False\n    otherwise. Both x and n, are string representation of a fraction, and have the following format,\n    <numerator>/<denominator> where both numerator and denominator are positive whole numbers.\n\n    You can assume that x, and n are valid fractions, and do not have zero as denominator.\n\n    simplify(\"1/5\", \"5/1\") = True\n    simplify(\"1/6\", \"2/1\") = False\n    simplify(\"7/10\", \"10/2\") = False\n    \"\"\"\n    frac_x = Fraction(x)\n    frac_n = Fraction(n)\n    product = frac_x * frac_n\n    return product.denominator == 1\n```", "execution_time": 64.00599503517151, "code": "from fractions import Fraction\n\ndef simplify(x, n):\n    \"\"\"Your task is to implement a function that will simplify the expression\n    x * n. The function returns True if x * n evaluates to a whole number and False\n    otherwise. Both x and n, are string representation of a fraction, and have the following format,\n    <numerator>/<denominator> where both numerator and denominator are positive whole numbers.\n\n    You can assume that x, and n are valid fractions, and do not have zero as denominator.\n\n    simplify(\"1/5\", \"5/1\") = True\n    simplify(\"1/6\", \"2/1\") = False\n    simplify(\"7/10\", \"10/2\") = False\n    \"\"\"\n    frac_x = Fraction(x)\n    frac_n = Fraction(n)\n    product = frac_x * frac_n\n    return product.denominator == 1", "problem_index": 30, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "3d249a08", "prompt": "\ndef move_one_ball(arr):\n    \"\"\"We have an array 'arr' of N integers arr[1], arr[2], ..., arr[N].The\n    numbers in the array will be randomly ordered. Your task is to determine if\n    it is possible to get an array sorted in non-decreasing order by performing \n    the following operation on the given array:\n        You are allowed to perform right shift operation any number of times.\n    \n    One right shift operation means shifting all elements of the array by one\n    position in the right direction. The last element of the array will be moved to\n    the starting position in the array i.e. 0th index. \n\n    If it is possible to obtain the sorted array by performing the above operation\n    then return True else return False.\n    If the given array is empty then return True.\n\n    Note: The given list is guaranteed to have unique elements.\n\n    For Example:\n    \n    move_one_ball([3, 4, 5, 1, 2])==>True\n    Explanation: By performin 2 right shift operations, non-decreasing order can\n                 be achieved for the given array.\n    move_one_ball([3, 5, 4, 1, 2])==>False\n    Explanation:It is not possible to get non-decreasing order for the given\n                array by performing any number of right shift operations.\n                \n    \"\"\"\n", "solution": "```python\ndef can_be_sorted_with_rotations(arr):\n    if not arr:\n        return True\n    \n    n = len(arr)\n    breaks = 0\n    \n    for i in range(n):\n        if arr[i] > arr[(i + 1) % n]:\n            breaks += 1\n            if breaks > 1:\n                return False\n    \n    return True\n```", "execution_time": 58.44447898864746, "code": "def can_be_sorted_with_rotations(arr):\n    if not arr:\n        return True\n    \n    n = len(arr)\n    breaks = 0\n    \n    for i in range(n):\n        if arr[i] > arr[(i + 1) % n]:\n            breaks += 1\n            if breaks > 1:\n                return False\n    \n    return True", "problem_index": 31, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "43edd7f3", "prompt": "\ndef pluck(arr):\n    \"\"\"\n    \"Given an array representing a branch of a tree that has non-negative integer nodes\n    your task is to pluck one of the nodes and return it.\n    The plucked node should be the node with the smallest even value.\n    If multiple nodes with the same smallest even value are found return the node that has smallest index.\n\n    The plucked node should be returned in a list, [ smalest_value, its index ],\n    If there are no even values or the given array is empty, return [].\n\n    Example 1:\n        Input: [4,2,3]\n        Output: [2, 1]\n        Explanation: 2 has the smallest even value, and 2 has the smallest index.\n\n    Example 2:\n        Input: [1,2,3]\n        Output: [2, 1]\n        Explanation: 2 has the smallest even value, and 2 has the smallest index. \n\n    Example 3:\n        Input: []\n        Output: []\n    \n    Example 4:\n        Input: [5, 0, 3, 0, 4, 2]\n        Output: [0, 1]\n        Explanation: 0 is the smallest value, but  there are two zeros,\n                     so we will choose the first zero, which has the smallest index.\n\n    Constraints:\n        * 1 <= nodes.length <= 10000\n        * 0 <= node.value\n    \"\"\"\n", "solution": "```python\ndef pluck(arr):\n    \"\"\"\n    Pluck and return the smallest even-valued node from an array, along with its index, in the format [smallest_value, index].\n    Return an empty list if no even values exist or the array is empty.\n    \"\"\"\n    if not arr:\n        return []\n\n    even_nodes = []\n    for index, value in enumerate(arr):\n        if value is None or not isinstance(value, int):\n            continue\n        if value % 2 == 0:\n            even_nodes.append((value, index))\n    \n    if not even_nodes:\n        return []\n    \n    smallest_even_node = min(even_nodes, key=lambda x: x[0])\n    \n    smallest_value = smallest_even_node[0]\n    smallest_index = smallest_even_node[1]\n    \n    return [smallest_value, smallest_index]\n```", "execution_time": 60.08132481575012, "code": "def pluck(arr):\n    \"\"\"\n    Pluck and return the smallest even-valued node from an array, along with its index, in the format [smallest_value, index].\n    Return an empty list if no even values exist or the array is empty.\n    \"\"\"\n    if not arr:\n        return []\n\n    even_nodes = []\n    for index, value in enumerate(arr):\n        if value is None or not isinstance(value, int):\n            continue\n        if value % 2 == 0:\n            even_nodes.append((value, index))\n    \n    if not even_nodes:\n        return []\n    \n    smallest_even_node = min(even_nodes, key=lambda x: x[0])\n    \n    smallest_value = smallest_even_node[0]\n    smallest_index = smallest_even_node[1]\n    \n    return [smallest_value, smallest_index]", "problem_index": 32, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "43492f72", "prompt": "\n\ndef is_palindrome(string: str) -> bool:\n    \"\"\" Test if given string is a palindrome \"\"\"\n    return string == string[::-1]\n\n\ndef make_palindrome(string: str) -> str:\n    \"\"\" Find the shortest palindrome that begins with a supplied string.\n    Algorithm idea is simple:\n    - Find the longest postfix of supplied string that is a palindrome.\n    - Append to the end of the string reverse of a string prefix that comes before the palindromic suffix.\n    >>> make_palindrome('')\n    ''\n    >>> make_palindrome('cat')\n    'catac'\n    >>> make_palindrome('cata')\n    'catac'\n    \"\"\"\n", "solution": "```python\ndef is_palindrome(string: str) -> bool:\n    \"\"\" Test if given string is a palindrome\n    :param string: input string\n    :return: True if string is a palindrome, False otherwise\n    \"\"\"\n    return string == string[::-1]\n\n\ndef make_palindrome(string: str) -> str:\n    \"\"\" Find the shortest palindrome that begins with a supplied string.\n    Algorithm idea is simple:\n    - Find the longest postfix of supplied string that is a palindrome.\n    - Append to the end of the string reverse of a string prefix that comes before the palindromic suffix.\n    >>> make_palindrome('')\n    ''\n    >>> make_palindrome('cat')\n    'catac'\n    >>> make_palindrome('cata')\n    'catac'\n    >>> make_palindrome('a')\n    'a'\n    >>> make_palindrome('race')\n    'racecar'\n    >>> make_palindrome('abcde')\n    'abcdeedcba'\n    \"\"\"\n    if not isinstance(string, str):\n        raise TypeError(\"Input must be a string\")\n\n    if not string:\n        return string\n\n    for i in range(len(string), 0, -1):\n        if is_palindrome(string[-i:]):\n            return string + string[:-i][::-1]\n\n    return string + string[::-1]\n```", "execution_time": 85.51846098899841, "code": "def is_palindrome(string: str) -> bool:\n    \"\"\" Test if given string is a palindrome\n    :param string: input string\n    :return: True if string is a palindrome, False otherwise\n    \"\"\"\n    return string == string[::-1]\n\n\ndef make_palindrome(string: str) -> str:\n    \"\"\" Find the shortest palindrome that begins with a supplied string.\n    Algorithm idea is simple:\n    - Find the longest postfix of supplied string that is a palindrome.\n    - Append to the end of the string reverse of a string prefix that comes before the palindromic suffix.\n    >>> make_palindrome('')\n    ''\n    >>> make_palindrome('cat')\n    'catac'\n    >>> make_palindrome('cata')\n    'catac'\n    >>> make_palindrome('a')\n    'a'\n    >>> make_palindrome('race')\n    'racecar'\n    >>> make_palindrome('abcde')\n    'abcdeedcba'\n    \"\"\"\n    if not isinstance(string, str):\n        raise TypeError(\"Input must be a string\")\n\n    if not string:\n        return string\n\n    for i in range(len(string), 0, -1):\n        if is_palindrome(string[-i:]):\n            return string + string[:-i][::-1]\n\n    return string + string[::-1]", "problem_index": 33, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "25d54e79", "prompt": "\ndef count_up_to(n):\n    \"\"\"Implement a function that takes an non-negative integer and returns an array of the first n\n    integers that are prime numbers and less than n.\n    for example:\n    count_up_to(5) => [2,3]\n    count_up_to(11) => [2,3,5,7]\n    count_up_to(0) => []\n    count_up_to(20) => [2,3,5,7,11,13,17,19]\n    count_up_to(1) => []\n    count_up_to(18) => [2,3,5,7,11,13,17]\n    \"\"\"\n", "solution": "```python\ndef find_primes_up_to(n):\n    \"\"\"\n    Finds all prime numbers up to a given integer n.\n\n    Args:\n        n: A non-negative integer representing the upper limit.\n\n    Returns:\n        A list of prime numbers less than n.\n\n    Raises:\n        ValueError: If the input is not a non-negative integer.\n    \"\"\"\n    if not isinstance(n, int) or n < 0:\n        raise ValueError(\"Input must be a non-negative integer.\")\n\n    if n <= 2:\n        return []\n\n    sieve = [True] * n\n    sieve[0] = sieve[1] = False\n\n    for i in range(2, int(n**0.5) + 1):\n        if sieve[i]:\n            for j in range(i*i, n, i):\n                sieve[j] = False\n\n    primes = [i for i in range(2, n) if sieve[i]]\n    return primes\n```", "execution_time": 19.02380108833313, "code": "def find_primes_up_to(n):\n    \"\"\"\n    Finds all prime numbers up to a given integer n.\n\n    Args:\n        n: A non-negative integer representing the upper limit.\n\n    Returns:\n        A list of prime numbers less than n.\n\n    Raises:\n        ValueError: If the input is not a non-negative integer.\n    \"\"\"\n    if not isinstance(n, int) or n < 0:\n        raise ValueError(\"Input must be a non-negative integer.\")\n\n    if n <= 2:\n        return []\n\n    sieve = [True] * n\n    sieve[0] = sieve[1] = False\n\n    for i in range(2, int(n**0.5) + 1):\n        if sieve[i]:\n            for j in range(i*i, n, i):\n                sieve[j] = False\n\n    primes = [i for i in range(2, n) if sieve[i]]\n    return primes", "problem_index": 34, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "c2a0d2be", "prompt": "\ndef histogram(test):\n    \"\"\"Given a string representing a space separated lowercase letters, return a dictionary\n    of the letter with the most repetition and containing the corresponding count.\n    If several letters have the same occurrence, return all of them.\n    \n    Example:\n    histogram('a b c') == {'a': 1, 'b': 1, 'c': 1}\n    histogram('a b b a') == {'a': 2, 'b': 2}\n    histogram('a b c a b') == {'a': 2, 'b': 2}\n    histogram('b b b b a') == {'b': 4}\n    histogram('') == {}\n\n    \"\"\"\n", "solution": "```python\ndef histogram(test: str) -> dict:\n    \"\"\"\n    Given a string representing a space separated lowercase letters, \n    return a dictionary of the letters with the highest frequency and their corresponding counts.\n    If several letters have the same highest frequency, return all of them.\n    \n    Args:\n        test (str): A string of space separated lowercase letters.\n    \n    Returns:\n        dict: A dictionary containing the letters with the highest frequency and their counts.\n    \n    Raises:\n        ValueError: If the input is not a string or contains non-lowercase letters.\n    \"\"\"\n    import re\n    if not isinstance(test, str):\n        raise ValueError(\"Input must be a string\")\n    \n    test = test.strip()\n    if not test:\n        return {}\n    \n    if not re.match('^[a-z ]*$', test):\n        raise ValueError(\"Input must be a string of space-separated lowercase letters.\")\n    \n    letters = test.split()\n    from collections import Counter\n    count_dict = Counter(letters)\n    \n    if not count_dict:\n        return {}\n    \n    max_count = max(count_dict.values())\n    result = {letter: count for letter, count in count_dict.items() if count == max_count}\n    \n    return result\n```", "execution_time": 95.21143102645874, "code": "def histogram(test: str) -> dict:\n    \"\"\"\n    Given a string representing a space separated lowercase letters, \n    return a dictionary of the letters with the highest frequency and their corresponding counts.\n    If several letters have the same highest frequency, return all of them.\n    \n    Args:\n        test (str): A string of space separated lowercase letters.\n    \n    Returns:\n        dict: A dictionary containing the letters with the highest frequency and their counts.\n    \n    Raises:\n        ValueError: If the input is not a string or contains non-lowercase letters.\n    \"\"\"\n    import re\n    if not isinstance(test, str):\n        raise ValueError(\"Input must be a string\")\n    \n    test = test.strip()\n    if not test:\n        return {}\n    \n    if not re.match('^[a-z ]*$', test):\n        raise ValueError(\"Input must be a string of space-separated lowercase letters.\")\n    \n    letters = test.split()\n    from collections import Counter\n    count_dict = Counter(letters)\n    \n    if not count_dict:\n        return {}\n    \n    max_count = max(count_dict.values())\n    result = {letter: count for letter, count in count_dict.items() if count == max_count}\n    \n    return result", "problem_index": 35, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "5333a1f7", "prompt": "from typing import List\n\n\ndef has_close_elements(numbers: List[float], threshold: float) -> bool:\n    \"\"\" Check if in given list of numbers, are any two numbers closer to each other than\n    given threshold.\n    >>> has_close_elements([1.0, 2.0, 3.0], 0.5)\n    False\n    >>> has_close_elements([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3)\n    True\n    \"\"\"\n", "solution": "```python\ndef has_close_elements(numbers: List[float], threshold: float) -> bool:\n    \"\"\" Check if in given list of numbers, are any two numbers closer to each other than\n    given threshold.\n    >>> has_close_elements([1.0, 2.0, 3.0], 0.5)\n    False\n    >>> has_close_elements([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3)\n    True\n    \"\"\"\n    numbers.sort()\n    from itertools import pairwise\n    for a, b in pairwise(numbers):\n        if b - a <= threshold:\n            return True\n    return False\n```", "execution_time": 29.164105892181396, "code": "def has_close_elements(numbers: List[float], threshold: float) -> bool:\n    \"\"\" Check if in given list of numbers, are any two numbers closer to each other than\n    given threshold.\n    >>> has_close_elements([1.0, 2.0, 3.0], 0.5)\n    False\n    >>> has_close_elements([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3)\n    True\n    \"\"\"\n    numbers.sort()\n    from itertools import pairwise\n    for a, b in pairwise(numbers):\n        if b - a <= threshold:\n            return True\n    return False", "problem_index": 36, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "3b0af203", "prompt": "\ndef add_elements(arr, k):\n    \"\"\"\n    Given a non-empty array of integers arr and an integer k, return\n    the sum of the elements with at most two digits from the first k elements of arr.\n\n    Example:\n\n        Input: arr = [111,21,3,4000,5,6,7,8,9], k = 4\n        Output: 24 # sum of 21 + 3\n\n    Constraints:\n        1. 1 <= len(arr) <= 100\n        2. 1 <= k <= len(arr)\n    \"\"\"\n", "solution": "```python\ndef add_elements(arr: list[int], k: int):\n    \"\"\"\n    Given a non-empty array of integers arr and an integer k, return\n    the sum of the elements with at most two digits from the first k elements of arr.\n    The function includes negative two-digit numbers (e.g., -10 to -1).\n    \"\"\"\n    if not arr or not isinstance(arr, list) or not all(isinstance(x, int) for x in arr):\n        raise ValueError(\"Input array must be a non-empty list of integers\")\n    if not isinstance(k, int) or k < 1 or k > len(arr):\n        raise ValueError(\"k must be an integer between 1 and the length of the array\")\n    return sum(num for num in arr[:k] if -99 <= num <= 99)\n```", "execution_time": 20.354104042053223, "code": "def add_elements(arr: list[int], k: int):\n    \"\"\"\n    Given a non-empty array of integers arr and an integer k, return\n    the sum of the elements with at most two digits from the first k elements of arr.\n    The function includes negative two-digit numbers (e.g., -10 to -1).\n    \"\"\"\n    if not arr or not isinstance(arr, list) or not all(isinstance(x, int) for x in arr):\n        raise ValueError(\"Input array must be a non-empty list of integers\")\n    if not isinstance(k, int) or k < 1 or k > len(arr):\n        raise ValueError(\"k must be an integer between 1 and the length of the array\")\n    return sum(num for num in arr[:k] if -99 <= num <= 99)", "problem_index": 37, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "14f99476", "prompt": "\ndef get_odd_collatz(n):\n    \"\"\"\n    Given a positive integer n, return a sorted list that has the odd numbers in collatz sequence.\n\n    The <PERSON><PERSON> conjecture is a conjecture in mathematics that concerns a sequence defined\n    as follows: start with any positive integer n. Then each term is obtained from the \n    previous term as follows: if the previous term is even, the next term is one half of \n    the previous term. If the previous term is odd, the next term is 3 times the previous\n    term plus 1. The conjecture is that no matter what value of n, the sequence will always reach 1.\n\n    Note: \n        1. <PERSON><PERSON>(1) is [1].\n        2. returned list sorted in increasing order.\n\n    For example:\n    get_odd_collatz(5) returns [1, 5] # The collatz sequence for 5 is [5, 16, 8, 4, 2, 1], so the odd numbers are only 1, and 5.\n    \"\"\"\n", "solution": "```python\ndef get_odd_collatz(n):\n    \"\"\"\n    Given a positive integer n, return a sorted list that has the odd numbers in collatz sequence.\n\n    The <PERSON><PERSON> conjecture is a conjecture in mathematics that concerns a sequence defined\n    as follows: start with any positive integer n. Then each term is obtained from the \n    previous term as follows: if the previous term is even, the next term is one half of \n    the previous term. If the previous term is odd, the next term is 3 times the previous\n    term plus 1. The conjecture is that no matter what value of n, the sequence will always reach 1.\n\n    Note: \n        1. <PERSON><PERSON>(1) is [1].\n        2. returned list sorted in increasing order.\n\n    For example:\n    get_odd_collatz(5) returns [1, 5] # The collatz sequence for 5 is [5, 16, 8, 4, 2, 1], so the odd numbers are only 1, and 5.\n    \"\"\"\n    if not isinstance(n, int) or n <= 0:\n        raise ValueError(\"Input must be a positive integer\")\n\n    odd_numbers = set()\n    current = n\n\n    while current != 1:\n        if current % 2 != 0:\n            odd_numbers.add(current)\n        \n        if current % 2 == 0:\n            current = current // 2\n        else:\n            current = 3 * current + 1\n    \n    odd_numbers.add(1)\n    return sorted(odd_numbers)\n```", "execution_time": 75.73020911216736, "code": "def get_odd_collatz(n):\n    \"\"\"\n    Given a positive integer n, return a sorted list that has the odd numbers in collatz sequence.\n\n    The <PERSON>latz conjecture is a conjecture in mathematics that concerns a sequence defined\n    as follows: start with any positive integer n. Then each term is obtained from the \n    previous term as follows: if the previous term is even, the next term is one half of \n    the previous term. If the previous term is odd, the next term is 3 times the previous\n    term plus 1. The conjecture is that no matter what value of n, the sequence will always reach 1.\n\n    Note: \n        1. <PERSON><PERSON>(1) is [1].\n        2. returned list sorted in increasing order.\n\n    For example:\n    get_odd_collatz(5) returns [1, 5] # The collatz sequence for 5 is [5, 16, 8, 4, 2, 1], so the odd numbers are only 1, and 5.\n    \"\"\"\n    if not isinstance(n, int) or n <= 0:\n        raise ValueError(\"Input must be a positive integer\")\n\n    odd_numbers = set()\n    current = n\n\n    while current != 1:\n        if current % 2 != 0:\n            odd_numbers.add(current)\n        \n        if current % 2 == 0:\n            current = current // 2\n        else:\n            current = 3 * current + 1\n    \n    odd_numbers.add(1)\n    return sorted(odd_numbers)", "problem_index": 38, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "88493d91", "prompt": "\nFIX = \"\"\"\nAdd more test cases.\n\"\"\"\n\ndef vowels_count(s):\n    \"\"\"Write a function vowels_count which takes a string representing\n    a word as input and returns the number of vowels in the string.\n    Vowels in this case are 'a', 'e', 'i', 'o', 'u'. Here, 'y' is also a\n    vowel, but only when it is at the end of the given word.\n\n    Example:\n    >>> vowels_count(\"abcde\")\n    2\n    >>> vowels_count(\"ACEDY\")\n    3\n    \"\"\"\n", "solution": "```python\ndef vowels_count(s: str) -> int:\n    \"\"\"\n    Counts the number of vowels in a string.\n    Vowels are defined as 'a', 'e', 'i', 'o', 'u' (case-insensitive).\n    If the string is not empty and ends with 'y' (case-insensitive), 'y' is also counted as a vowel.\n    \"\"\"\n    if not isinstance(s, str):\n        raise TypeError(\"Input must be a string\")\n\n    vowels = set('aeiouAEIOU')\n    count = sum(1 for char in s if char in vowels)\n\n    if s and s[-1].lower() == 'y':\n        count += 1\n\n    return count\n```", "execution_time": 73.9307131767273, "code": "def vowels_count(s: str) -> int:\n    \"\"\"\n    Counts the number of vowels in a string.\n    Vowels are defined as 'a', 'e', 'i', 'o', 'u' (case-insensitive).\n    If the string is not empty and ends with 'y' (case-insensitive), 'y' is also counted as a vowel.\n    \"\"\"\n    if not isinstance(s, str):\n        raise TypeError(\"Input must be a string\")\n\n    vowels = set('aeiouAEIOU')\n    count = sum(1 for char in s if char in vowels)\n\n    if s and s[-1].lower() == 'y':\n        count += 1\n\n    return count", "problem_index": 39, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "86ab5631", "prompt": "\n\ndef change_base(x: int, base: int):\n    \"\"\"Change numerical base of input number x to base.\n    return string representation after the conversion.\n    base numbers are less than 10.\n    >>> change_base(8, 3)\n    '22'\n    >>> change_base(8, 2)\n    '1000'\n    >>> change_base(7, 2)\n    '111'\n    \"\"\"\n", "solution": "def change_base(x: int, base: int) -> str:\n    \"\"\"Change numerical base of input number x to base.\n    return string representation after the conversion.\n    base numbers are less than 10.\n    >>> change_base(8, 3)\n    '22'\n    >>> change_base(8, 2)\n    '1000'\n    >>> change_base(7, 2)\n    '111'\n    \"\"\"\n    if x == 0:\n        return '0'\n\n    digits = []\n    while x > 0:\n        digits.append(str(x % base))\n        x //= base\n\n    return ''.join(digits[::-1])", "execution_time": 27.42026400566101, "code": "def change_base(x: int, base: int) -> str:\n    \"\"\"Change numerical base of input number x to base.\n    return string representation after the conversion.\n    base numbers are less than 10.\n    >>> change_base(8, 3)\n    '22'\n    >>> change_base(8, 2)\n    '1000'\n    >>> change_base(7, 2)\n    '111'\n    \"\"\"\n    if x == 0:\n        return '0'\n\n    digits = []\n    while x > 0:\n        digits.append(str(x % base))\n        x //= base\n\n    return ''.join(digits[::-1])", "problem_index": 40, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "6bfe2b2a", "prompt": "\ndef specialFilter(nums):\n    \"\"\"Write a function that takes an array of numbers as input and returns \n    the number of elements in the array that are greater than 10 and both \n    first and last digits of a number are odd (1, 3, 5, 7, 9).\n    For example:\n    specialFilter([15, -73, 14, -15]) => 1 \n    specialFilter([33, -2, -3, 45, 21, 109]) => 2\n    \"\"\"\n", "solution": "```python\ndef specialFilter(nums):\n    \"\"\"Write a function that takes an array of numbers as input and returns \n    the number of elements in the array that are greater than 10 and both \n    first and last digits of a number are odd (1, 3, 5, 7, 9).\n    For example:\n    specialFilter([15, -73, 14, -15]) => 1 \n    specialFilter([33, -2, -3, 45, 21, 109]) => 2\n    \"\"\"\n    def is_odd_digit(digit):\n        return digit in {'1', '3', '5', '7', '9'}\n    \n    count = 0\n    for num in nums:\n        str_num = str(abs(num))\n        if int(str_num) > 10:\n            if is_odd_digit(str_num[0]) and is_odd_digit(str_num[-1]):\n                count += 1\n    return count\n```", "execution_time": 19.713417053222656, "code": "def specialFilter(nums):\n    \"\"\"Write a function that takes an array of numbers as input and returns \n    the number of elements in the array that are greater than 10 and both \n    first and last digits of a number are odd (1, 3, 5, 7, 9).\n    For example:\n    specialFilter([15, -73, 14, -15]) => 1 \n    specialFilter([33, -2, -3, 45, 21, 109]) => 2\n    \"\"\"\n    def is_odd_digit(digit):\n        return digit in {'1', '3', '5', '7', '9'}\n    \n    count = 0\n    for num in nums:\n        str_num = str(abs(num))\n        if int(str_num) > 10:\n            if is_odd_digit(str_num[0]) and is_odd_digit(str_num[-1]):\n                count += 1\n    return count", "problem_index": 41, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "47450522", "prompt": "from typing import List\n\n\ndef concatenate(strings: List[str]) -> str:\n    \"\"\" Concatenate list of strings into a single string\n    >>> concatenate([])\n    ''\n    >>> concatenate(['a', 'b', 'c'])\n    'abc'\n    \"\"\"\n", "solution": "```python\ndef concatenate(strings: List[str]) -> str:\n    \"\"\" Concatenate list of strings into a single string.\n    The function will raise exceptions for invalid input types.\n\n    >>> concatenate([])\n    ''\n    >>> concatenate(['a', 'b', 'c'])\n    'abc'\n    >>> concatenate(['Hello', ' ', 'World', '!'])\n    'Hello World!'\n    >>> concatenate(['Python', '3.9', 'is', 'great!'])\n    'Python3.9isgreat!'\n    >>> concatenate(['', 'test', ''])\n    'test'\n    >>> concatenate(['a' * 1000] * 1000)\n    'a' * 1000000\n\n    Raises:\n        TypeError: If the input is not a list.\n        ValueError: If any element in the list is not a string.\n    \"\"\"\n    if not isinstance(strings, list):\n        raise TypeError(\"Input must be a list of strings.\")\n    if any(not isinstance(s, str) for s in strings):\n        raise ValueError(\"All elements in the list must be strings.\")\n    \n    return ''.join(strings)\n```", "execution_time": 33.473899841308594, "code": "def concatenate(strings: List[str]) -> str:\n    \"\"\" Concatenate list of strings into a single string.\n    The function will raise exceptions for invalid input types.\n\n    >>> concatenate([])\n    ''\n    >>> concatenate(['a', 'b', 'c'])\n    'abc'\n    >>> concatenate(['Hello', ' ', 'World', '!'])\n    'Hello World!'\n    >>> concatenate(['Python', '3.9', 'is', 'great!'])\n    'Python3.9isgreat!'\n    >>> concatenate(['', 'test', ''])\n    'test'\n    >>> concatenate(['a' * 1000] * 1000)\n    'a' * 1000000\n\n    Raises:\n        TypeError: If the input is not a list.\n        ValueError: If any element in the list is not a string.\n    \"\"\"\n    if not isinstance(strings, list):\n        raise TypeError(\"Input must be a list of strings.\")\n    if any(not isinstance(s, str) for s in strings):\n        raise ValueError(\"All elements in the list must be strings.\")\n    \n    return ''.join(strings)", "problem_index": 42, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "e643c1d0", "prompt": "\n\ndef triples_sum_to_zero(l: list):\n    \"\"\"\n    triples_sum_to_zero takes a list of integers as an input.\n    it returns True if there are three distinct elements in the list that\n    sum to zero, and False otherwise.\n\n    >>> triples_sum_to_zero([1, 3, 5, 0])\n    False\n    >>> triples_sum_to_zero([1, 3, -2, 1])\n    True\n    >>> triples_sum_to_zero([1, 2, 3, 7])\n    False\n    >>> triples_sum_to_zero([2, 4, -5, 3, 9, 7])\n    True\n    >>> triples_sum_to_zero([1])\n    False\n    \"\"\"\n", "solution": "def triples_sum_to_zero(nums: list) -> bool:\n    \"\"\"\n    Given a list of integers, return True if there are three distinct elements in the list that sum to 0.\n    \"\"\"\n    if len(nums) < 3:\n        return False\n    nums = list(set(nums))  # Remove duplicates\n    nums.sort()\n    n = len(nums)\n    for i in range(n - 2):\n        left, right = i + 1, n - 1\n        while left < right:\n            total = nums[i] + nums[left] + nums[right]\n            if total == 0:\n                return True\n            elif total < 0:\n                left += 1\n            else:\n                right -= 1\n    return False", "execution_time": 71.32702493667603, "code": "def triples_sum_to_zero(nums: list) -> bool:\n    \"\"\"\n    Given a list of integers, return True if there are three distinct elements in the list that sum to 0.\n    \"\"\"\n    if len(nums) < 3:\n        return False\n    nums = list(set(nums))  # Remove duplicates\n    nums.sort()\n    n = len(nums)\n    for i in range(n - 2):\n        left, right = i + 1, n - 1\n        while left < right:\n            total = nums[i] + nums[left] + nums[right]\n            if total == 0:\n                return True\n            elif total < 0:\n                left += 1\n            else:\n                right -= 1\n    return False", "problem_index": 43, "partial_success": true, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "fa21ea72", "prompt": "\ndef minSubArraySum(nums):\n    \"\"\"\n    Given an array of integers nums, find the minimum sum of any non-empty sub-array\n    of nums.\n    Example\n    minSubArraySum([2, 3, 4, 1, 2, 4]) == 1\n    minSubArraySum([-1, -2, -3]) == -6\n    \"\"\"\n", "solution": "def minSubArraySum(nums):\n    \"\"\"Returns the minimum contiguous subarray sum in the given list of integers.\"\"\"\n    if not nums:\n        raise ValueError(\"Input array cannot be empty\")\n    \n    min_sum = float('inf')\n    current_sum = 0\n    \n    for num in nums:\n        current_sum = min(num, current_sum + num)\n        min_sum = min(min_sum, current_sum)\n    \n    return min_sum", "execution_time": 20.571111917495728, "code": "def minSubArraySum(nums):\n    \"\"\"Returns the minimum contiguous subarray sum in the given list of integers.\"\"\"\n    if not nums:\n        raise ValueError(\"Input array cannot be empty\")\n    \n    min_sum = float('inf')\n    current_sum = 0\n    \n    for num in nums:\n        current_sum = min(num, current_sum + num)\n        min_sum = min(min_sum, current_sum)\n    \n    return min_sum", "problem_index": 44, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "2ad35d93", "prompt": "from typing import List\n\n\ndef factorize(n: int) -> List[int]:\n    \"\"\" Return list of prime factors of given integer in the order from smallest to largest.\n    Each of the factors should be listed number of times corresponding to how many times it appeares in factorization.\n    Input number should be equal to the product of all factors\n    >>> factorize(8)\n    [2, 2, 2]\n    >>> factorize(25)\n    [5, 5]\n    >>> factorize(70)\n    [2, 5, 7]\n    \"\"\"\n", "solution": "```python\nfrom typing import List\n\ndef factorize(n: int) -> List[int]:\n    \"\"\" Return list of prime factors of given integer in the order from smallest to largest.\n    Each of the factors should be listed number of times corresponding to how many times it appears in factorization.\n    Input number should be equal to the product of all factors.\n    \n    >>> factorize(8)\n    [2, 2, 2]\n    >>> factorize(25)\n    [5, 5]\n    >>> factorize(70)\n    [2, 5, 7]\n    >>> factorize(1)\n    []\n    >>> factorize(0)\n    []\n    >>> factorize(-10)\n    []\n    \"\"\"\n    if n <= 1:\n        return []\n    \n    factors = []\n    divisor = 2\n    \n    while divisor * divisor <= n:\n        while n % divisor == 0:\n            factors.append(divisor)\n            n //= divisor\n        divisor += 1\n    \n    if n > 1:\n        factors.append(n)\n    \n    return factors\n```", "execution_time": 66.18431496620178, "code": "from typing import List\n\ndef factorize(n: int) -> List[int]:\n    \"\"\" Return list of prime factors of given integer in the order from smallest to largest.\n    Each of the factors should be listed number of times corresponding to how many times it appears in factorization.\n    Input number should be equal to the product of all factors.\n    \n    >>> factorize(8)\n    [2, 2, 2]\n    >>> factorize(25)\n    [5, 5]\n    >>> factorize(70)\n    [2, 5, 7]\n    >>> factorize(1)\n    []\n    >>> factorize(0)\n    []\n    >>> factorize(-10)\n    []\n    \"\"\"\n    if n <= 1:\n        return []\n    \n    factors = []\n    divisor = 2\n    \n    while divisor * divisor <= n:\n        while n % divisor == 0:\n            factors.append(divisor)\n            n //= divisor\n        divisor += 1\n    \n    if n > 1:\n        factors.append(n)\n    \n    return factors", "problem_index": 45, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "439da0d3", "prompt": "\n\ndef strlen(string: str) -> int:\n    \"\"\" Return length of given string\n    >>> strlen('')\n    0\n    >>> strlen('abc')\n    3\n    \"\"\"\n", "solution": "```python\ndef strlen(string: str) -> int:\n    \"\"\" Return length of given string\n    >>> strlen('')\n    0\n    >>> strlen('abc')\n    3\n    \"\"\"\n    return len(string)\n```", "execution_time": 21.28680181503296, "code": "def strlen(string: str) -> int:\n    \"\"\" Return length of given string\n    >>> strlen('')\n    0\n    >>> strlen('abc')\n    3\n    \"\"\"\n    return len(string)", "problem_index": 46, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "6214127d", "prompt": "\ndef match_parens(lst):\n    '''\n    You are given a list of two strings, both strings consist of open\n    parentheses '(' or close parentheses ')' only.\n    Your job is to check if it is possible to concatenate the two strings in\n    some order, that the resulting string will be good.\n    A string S is considered to be good if and only if all parentheses in S\n    are balanced. For example: the string '(())()' is good, while the string\n    '())' is not.\n    Return 'Yes' if there's a way to make a good string, and return 'No' otherwise.\n\n    Examples:\n    match_parens(['()(', ')']) == 'Yes'\n    match_parens([')', ')']) == 'No'\n    '''\n", "solution": "def match_parens(lst):\n    def is_balanced(s):\n        balance = 0\n        for char in s:\n            if char == '(':\n                balance += 1\n            elif char == ')':\n                balance -= 1\n                if balance < 0:\n                    return False\n        return balance == 0\n\n    if not isinstance(lst, list) or len(lst) != 2:\n        raise ValueError(\"Input must be a list of two strings\")\n\n    s1, s2 = lst\n\n    if not isinstance(s1, str) or not isinstance(s2, str):\n        raise ValueError(\"Both elements in the list must be strings\")\n\n    if not set(s1).issubset({'(' , ')'}) or not set(s2).issubset({'(' , ')'}):\n        raise ValueError(\"Both strings must only contain '(' and ')'\")\n\n    return 'Yes' if is_balanced(s1 + s2) or is_balanced(s2 + s1) else 'No'", "execution_time": 73.44479584693909, "code": "def match_parens(lst):\n    def is_balanced(s):\n        balance = 0\n        for char in s:\n            if char == '(':\n                balance += 1\n            elif char == ')':\n                balance -= 1\n                if balance < 0:\n                    return False\n        return balance == 0\n\n    if not isinstance(lst, list) or len(lst) != 2:\n        raise ValueError(\"Input must be a list of two strings\")\n\n    s1, s2 = lst\n\n    if not isinstance(s1, str) or not isinstance(s2, str):\n        raise ValueError(\"Both elements in the list must be strings\")\n\n    if not set(s1).issubset({'(' , ')'}) or not set(s2).issubset({'(' , ')'}):\n        raise ValueError(\"Both strings must only contain '(' and ')'\")\n\n    return 'Yes' if is_balanced(s1 + s2) or is_balanced(s2 + s1) else 'No'", "problem_index": 47, "partial_success": true, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "d418efb7", "prompt": "\ndef numerical_letter_grade(grades):\n    \"\"\"It is the last week of the semester and the teacher has to give the grades\n    to students. The teacher has been making her own algorithm for grading.\n    The only problem is, she has lost the code she used for grading.\n    She has given you a list of GPAs for some students and you have to write \n    a function that can output a list of letter grades using the following table:\n             GPA       |    Letter grade\n              4.0                A+\n            > 3.7                A \n            > 3.3                A- \n            > 3.0                B+\n            > 2.7                B \n            > 2.3                B-\n            > 2.0                C+\n            > 1.7                C\n            > 1.3                C-\n            > 1.0                D+ \n            > 0.7                D \n            > 0.0                D-\n              0.0                E\n    \n\n    Example:\n    grade_equation([4.0, 3, 1.7, 2, 3.5]) ==> ['A+', 'B', 'C-', 'C', 'A-']\n    \"\"\"\n", "solution": "def numerical_letter_grade(grades):\n    \"\"\"It is the last week of the semester and the teacher has to give the grades\n    to students. The teacher has been making her own algorithm for grading.\n    The only problem is, she has lost the code she used for grading.\n    She has given you a list of GPAs for some students and you have to write \n    a function that can output a list of letter grades using the following table:\n             GPA       |    Letter grade\n              4.0                A+\n            > 3.7                A \n            > 3.3                A- \n            > 3.0                B+\n            > 2.7                B \n            > 2.3                B-\n            > 2.0                C+\n            > 1.7                C\n            > 1.3                C-\n            > 1.0                D+ \n            > 0.7                D \n            > 0.0                D-\n              0.0                E\n    \n\n    Example:\n    grade_equation([4.0, 3, 1.7, 2, 3.5]) ==> ['A+', 'B', 'C-', 'C', 'A-']\n    \"\"\"\n    if not isinstance(grades, list):\n        raise TypeError(\"Input must be a list of GPAs.\")\n    if not all(isinstance(gpa, (int, float)) for gpa in grades):\n        raise ValueError(\"All elements in the list must be numbers.\")\n\n    letter_grades = []\n    grade_map = [\n        (4.0, 'A+'),\n        (3.7, 'A'),\n        (3.3, 'A-'),\n        (3.0, 'B+'),\n        (2.7, 'B'),\n        (2.3, 'B-'),\n        (2.0, 'C+'),\n        (1.7, 'C'),\n        (1.3, 'C-'),\n        (1.0, 'D+'),\n        (0.7, 'D'),\n        (0.0, 'E')\n    ]\n\n    for gpa in grades:\n        if gpa < 0:\n            letter_grade = 'E'\n        elif gpa == 0.0:\n            letter_grade = 'E'\n        elif gpa > 4.0:\n            letter_grade = 'A+'\n        else:\n            letter_grade = 'E'  # Default grade\n            for threshold, grade in grade_map:\n                if gpa >= threshold:\n                    letter_grade = grade\n                    break\n        letter_grades.append(letter_grade)\n\n    return letter_grades", "execution_time": 85.84358310699463, "code": "def numerical_letter_grade(grades):\n    \"\"\"It is the last week of the semester and the teacher has to give the grades\n    to students. The teacher has been making her own algorithm for grading.\n    The only problem is, she has lost the code she used for grading.\n    She has given you a list of GPAs for some students and you have to write \n    a function that can output a list of letter grades using the following table:\n             GPA       |    Letter grade\n              4.0                A+\n            > 3.7                A \n            > 3.3                A- \n            > 3.0                B+\n            > 2.7                B \n            > 2.3                B-\n            > 2.0                C+\n            > 1.7                C\n            > 1.3                C-\n            > 1.0                D+ \n            > 0.7                D \n            > 0.0                D-\n              0.0                E\n    \n\n    Example:\n    grade_equation([4.0, 3, 1.7, 2, 3.5]) ==> ['A+', 'B', 'C-', 'C', 'A-']\n    \"\"\"\n    if not isinstance(grades, list):\n        raise TypeError(\"Input must be a list of GPAs.\")\n    if not all(isinstance(gpa, (int, float)) for gpa in grades):\n        raise ValueError(\"All elements in the list must be numbers.\")\n\n    letter_grades = []\n    grade_map = [\n        (4.0, 'A+'),\n        (3.7, 'A'),\n        (3.3, 'A-'),\n        (3.0, 'B+'),\n        (2.7, 'B'),\n        (2.3, 'B-'),\n        (2.0, 'C+'),\n        (1.7, 'C'),\n        (1.3, 'C-'),\n        (1.0, 'D+'),\n        (0.7, 'D'),\n        (0.0, 'E')\n    ]\n\n    for gpa in grades:\n        if gpa < 0:\n            letter_grade = 'E'\n        elif gpa == 0.0:\n            letter_grade = 'E'\n        elif gpa > 4.0:\n            letter_grade = 'A+'\n        else:\n            letter_grade = 'E'  # Default grade\n            for threshold, grade in grade_map:\n                if gpa >= threshold:\n                    letter_grade = grade\n                    break\n        letter_grades.append(letter_grade)\n\n    return letter_grades", "problem_index": 48, "partial_success": true, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "a065916e", "prompt": "\ndef decimal_to_binary(decimal):\n    \"\"\"You will be given a number in decimal form and your task is to convert it to\n    binary format. The function should return a string, with each character representing a binary\n    number. Each character in the string will be '0' or '1'.\n\n    There will be an extra couple of characters 'db' at the beginning and at the end of the string.\n    The extra characters are there to help with the format.\n\n    Examples:\n    decimal_to_binary(15)   # returns \"db1111db\"\n    decimal_to_binary(32)   # returns \"db100000db\"\n    \"\"\"\n", "solution": "```python\ndef decimal_to_binary(decimal):\n    \"\"\"Convert a non-negative integer from decimal to binary format, \n    returning a string with 'db' prefix and suffix.\n    \n    Args:\n        decimal (int): A non-negative integer to convert.\n    \n    Returns:\n        str: A string representing the binary format with 'db' at both ends.\n    \n    Raises:\n        ValueError: If the input is not a non-negative integer.\n    \n    Examples:\n        decimal_to_binary(15)   # returns \"db1111db\"\n        decimal_to_binary(32)   # returns \"db100000db\"\n    \"\"\"\n    if not isinstance(decimal, int) or decimal < 0:\n        raise ValueError(\"Input must be a non-negative integer\")\n    \n    binary = bin(decimal)[2:]  # Convert to binary, remove '0b' prefix\n    return f\"db{binary}db\"\n```", "execution_time": 82.07690095901489, "code": "def decimal_to_binary(decimal):\n    \"\"\"Convert a non-negative integer from decimal to binary format, \n    returning a string with 'db' prefix and suffix.\n    \n    Args:\n        decimal (int): A non-negative integer to convert.\n    \n    Returns:\n        str: A string representing the binary format with 'db' at both ends.\n    \n    Raises:\n        ValueError: If the input is not a non-negative integer.\n    \n    Examples:\n        decimal_to_binary(15)   # returns \"db1111db\"\n        decimal_to_binary(32)   # returns \"db100000db\"\n    \"\"\"\n    if not isinstance(decimal, int) or decimal < 0:\n        raise ValueError(\"Input must be a non-negative integer\")\n    \n    binary = bin(decimal)[2:]  # Convert to binary, remove '0b' prefix\n    return f\"db{binary}db\"", "problem_index": 49, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "71eb7f5e", "prompt": "\n\ndef prime_fib(n: int):\n    \"\"\"\n    prime_fib returns n-th number that is a <PERSON><PERSON><PERSON><PERSON> number and it's also prime.\n    >>> prime_fib(1)\n    2\n    >>> prime_fib(2)\n    3\n    >>> prime_fib(3)\n    5\n    >>> prime_fib(4)\n    13\n    >>> prime_fib(5)\n    89\n    \"\"\"\n", "solution": "```python\ndef prime_fib(n: int) -> int:\n    \"\"\"\n    prime_fib returns n-th prime <PERSON><PERSON><PERSON><PERSON> number.\n    >>> prime_fib(1)\n    2\n    >>> prime_fib(2)\n    3\n    >>> prime_fib(3)\n    5\n    >>> prime_fib(4)\n    13\n    >>> prime_fib(5)\n    89\n    >>> prime_fib(0)\n    Traceback (most recent call last):\n        ...\n    ValueError: n must be a positive integer\n    >>> prime_fib(-1)\n    Traceback (most recent call last):\n        ...\n    ValueError: n must be a positive integer\n    \"\"\"\n    if n <= 0:\n        raise ValueError(\"n must be a positive integer\")\n    \n    def is_prime(num):\n        if num < 2:\n            return False\n        for i in range(2, int(num**0.5) + 1):\n            if num % i == 0:\n                return False\n        return True\n\n    def fibonacci_gen():\n        a, b = 0, 1\n        while True:\n            yield a\n            a, b = b, a + b\n\n    prime_fibs = []\n    cached_primes = {}\n    for fib in fibonacci_gen():\n        if fib in cached_primes:\n            if cached_primes[fib]:\n                prime_fibs.append(fib)\n                if len(prime_fibs) == n:\n                    return fib\n        elif is_prime(fib):\n            prime_fibs.append(fib)\n            cached_primes[fib] = True\n            if len(prime_fibs) == n:\n                return fib\n        else:\n            cached_primes[fib] = False\n```", "execution_time": 25.252312898635864, "code": "def prime_fib(n: int) -> int:\n    \"\"\"\n    prime_fib returns n-th prime <PERSON><PERSON><PERSON><PERSON> number.\n    >>> prime_fib(1)\n    2\n    >>> prime_fib(2)\n    3\n    >>> prime_fib(3)\n    5\n    >>> prime_fib(4)\n    13\n    >>> prime_fib(5)\n    89\n    >>> prime_fib(0)\n    Traceback (most recent call last):\n        ...\n    ValueError: n must be a positive integer\n    >>> prime_fib(-1)\n    Traceback (most recent call last):\n        ...\n    ValueError: n must be a positive integer\n    \"\"\"\n    if n <= 0:\n        raise ValueError(\"n must be a positive integer\")\n    \n    def is_prime(num):\n        if num < 2:\n            return False\n        for i in range(2, int(num**0.5) + 1):\n            if num % i == 0:\n                return False\n        return True\n\n    def fibonacci_gen():\n        a, b = 0, 1\n        while True:\n            yield a\n            a, b = b, a + b\n\n    prime_fibs = []\n    cached_primes = {}\n    for fib in fibonacci_gen():\n        if fib in cached_primes:\n            if cached_primes[fib]:\n                prime_fibs.append(fib)\n                if len(prime_fibs) == n:\n                    return fib\n        elif is_prime(fib):\n            prime_fibs.append(fib)\n            cached_primes[fib] = True\n            if len(prime_fibs) == n:\n                return fib\n        else:\n            cached_primes[fib] = False", "problem_index": 50, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "f746782d", "prompt": "\ndef next_smallest(lst):\n    \"\"\"\n    You are given a list of integers.\n    Write a function next_smallest() that returns the 2nd smallest element of the list.\n    Return None if there is no such element.\n    \n    next_smallest([1, 2, 3, 4, 5]) == 2\n    next_smallest([5, 1, 4, 3, 2]) == 2\n    next_smallest([]) == None\n    next_smallest([1, 1]) == None\n    \"\"\"\n", "solution": "```python\ndef next_smallest(lst):\n    \"\"\"\n    Finds the next smallest element in a list of numbers.\n\n    Args:\n        lst: A list of numbers.\n\n    Returns:\n        The second smallest unique element in the list.\n        Returns None if the list has fewer than two unique elements.\n    \"\"\"\n    if not isinstance(lst, list):\n        raise TypeError(\"Input must be a list.\")\n    \n    numbers = lst\n    if len(numbers) < 2:\n        return None\n    \n    unique_sorted = sorted(set(numbers))\n    \n    return unique_sorted[1] if len(unique_sorted) > 1 else None\n```", "execution_time": 18.7103009223938, "code": "def next_smallest(lst):\n    \"\"\"\n    Finds the next smallest element in a list of numbers.\n\n    Args:\n        lst: A list of numbers.\n\n    Returns:\n        The second smallest unique element in the list.\n        Returns None if the list has fewer than two unique elements.\n    \"\"\"\n    if not isinstance(lst, list):\n        raise TypeError(\"Input must be a list.\")\n    \n    numbers = lst\n    if len(numbers) < 2:\n        return None\n    \n    unique_sorted = sorted(set(numbers))\n    \n    return unique_sorted[1] if len(unique_sorted) > 1 else None", "problem_index": 51, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "f581f9d4", "prompt": "\ndef count_nums(arr):\n    \"\"\"\n    Write a function count_nums which takes an array of integers and returns\n    the number of elements which has a sum of digits > 0.\n    If a number is negative, then its first signed digit will be negative:\n    e.g. -123 has signed digits -1, 2, and 3.\n    >>> count_nums([]) == 0\n    >>> count_nums([-1, 11, -11]) == 1\n    >>> count_nums([1, 1, 2]) == 3\n    \"\"\"\n", "solution": "```python\ndef count_nums(arr):\n    \"\"\"\n    Write a function count_nums which takes an array of integers and returns\n    the number of elements which has a sum of digits > 0.\n    If a number is negative, then its first signed digit will be negative:\n    e.g. -123 has signed digits -1, 2, and 3.\n    >>> count_nums([]) == 0\n    >>> count_nums([-1, 11, -11]) == 1\n    >>> count_nums([1, 1, 2]) == 3\n    \"\"\"\n    def sum_of_digits(n):\n        return sum(int(digit) if n >= 0 else -int(digit) if i == 0 else int(digit) \n                   for i, digit in enumerate(str(abs(n))))\n\n    return sum(1 for number in arr if sum_of_digits(number) > 0)\n```", "execution_time": 21.164738178253174, "code": "def count_nums(arr):\n    \"\"\"\n    Write a function count_nums which takes an array of integers and returns\n    the number of elements which has a sum of digits > 0.\n    If a number is negative, then its first signed digit will be negative:\n    e.g. -123 has signed digits -1, 2, and 3.\n    >>> count_nums([]) == 0\n    >>> count_nums([-1, 11, -11]) == 1\n    >>> count_nums([1, 1, 2]) == 3\n    \"\"\"\n    def sum_of_digits(n):\n        return sum(int(digit) if n >= 0 else -int(digit) if i == 0 else int(digit) \n                   for i, digit in enumerate(str(abs(n))))\n\n    return sum(1 for number in arr if sum_of_digits(number) > 0)", "problem_index": 52, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "95708625", "prompt": "\ndef eat(number, need, remaining):\n    \"\"\"\n    You're a hungry rabbit, and you already have eaten a certain number of carrots,\n    but now you need to eat more carrots to complete the day's meals.\n    you should return an array of [ total number of eaten carrots after your meals,\n                                    the number of carrots left after your meals ]\n    if there are not enough remaining carrots, you will eat all remaining carrots, but will still be hungry.\n    \n    Example:\n    * eat(5, 6, 10) -> [11, 4]\n    * eat(4, 8, 9) -> [12, 1]\n    * eat(1, 10, 10) -> [11, 0]\n    * eat(2, 11, 5) -> [7, 0]\n    \n    Variables:\n    @number : integer\n        the number of carrots that you have eaten.\n    @need : integer\n        the number of carrots that you need to eat.\n    @remaining : integer\n        the number of remaining carrots thet exist in stock\n    \n    Constrain:\n    * 0 <= number <= 1000\n    * 0 <= need <= 1000\n    * 0 <= remaining <= 1000\n\n    Have fun :)\n    \"\"\"\n", "solution": "def eat(number: int, need: int, remaining: int) -> list:\n    \"\"\"\n    Calculate the total number of carrots eaten and the remaining carrots after a meal.\n\n    Args:\n    number (int): The number of carrots already eaten.\n    need (int): The number of carrots needed to eat.\n    remaining (int): The number of remaining carrots in stock.\n\n    Returns:\n    list: A list containing the total number of eaten carrots and the remaining carrots.\n    \"\"\"\n    if not isinstance(number, int) or not isinstance(need, int) or not isinstance(remaining, int):\n        raise TypeError(\"Inputs 'number', 'need', and 'remaining' must be integers.\")\n    if not (0 <= number <= 1000) or not (0 <= need <= 1000) or not (0 <= remaining <= 1000):\n        raise ValueError(\"Input values must be in the range of 0 to 1000.\")\n\n    total_eaten = number\n    carrots_to_eat = min(need, remaining)\n    total_eaten += carrots_to_eat\n    remaining -= carrots_to_eat\n\n    return [total_eaten, remaining]", "execution_time": 69.1887378692627, "code": "def eat(number: int, need: int, remaining: int) -> list:\n    \"\"\"\n    Calculate the total number of carrots eaten and the remaining carrots after a meal.\n\n    Args:\n    number (int): The number of carrots already eaten.\n    need (int): The number of carrots needed to eat.\n    remaining (int): The number of remaining carrots in stock.\n\n    Returns:\n    list: A list containing the total number of eaten carrots and the remaining carrots.\n    \"\"\"\n    if not isinstance(number, int) or not isinstance(need, int) or not isinstance(remaining, int):\n        raise TypeError(\"Inputs 'number', 'need', and 'remaining' must be integers.\")\n    if not (0 <= number <= 1000) or not (0 <= need <= 1000) or not (0 <= remaining <= 1000):\n        raise ValueError(\"Input values must be in the range of 0 to 1000.\")\n\n    total_eaten = number\n    carrots_to_eat = min(need, remaining)\n    total_eaten += carrots_to_eat\n    remaining -= carrots_to_eat\n\n    return [total_eaten, remaining]", "problem_index": 53, "partial_success": true, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "538deeb2", "prompt": "\ndef compare_one(a, b):\n    \"\"\"\n    Create a function that takes integers, floats, or strings representing\n    real numbers, and returns the larger variable in its given variable type.\n    Return None if the values are equal.\n    Note: If a real number is represented as a string, the floating point might be . or ,\n\n    compare_one(1, 2.5) ➞ 2.5\n    compare_one(1, \"2,3\") ➞ \"2,3\"\n    compare_one(\"5,1\", \"6\") ➞ \"6\"\n    compare_one(\"1\", 1) ➞ None\n    \"\"\"\n", "solution": "def compare_one(a, b):\n    \"\"\"\n    Create a function that takes integers, floats, or strings representing\n    real numbers, and returns the larger variable in its given variable type.\n    Return None if the values are equal.\n    Note: If a real number is represented as a string, the floating point might be . or ,\n\n    compare_one(1, 2.5) ➞ 2.5\n    compare_one(1, \"2,3\") ➞ \"2,3\"\n    compare_one(\"5,1\", \"6\") ➞ \"6\"\n    compare_one(\"1\", 1) ➞ None\n    \"\"\"\n    def parse_number(x):\n        if isinstance(x, (int, float)):\n            return float(x)\n        if isinstance(x, str):\n            try:\n                return float(x.replace(',', '.'))\n            except ValueError:\n                return None\n\n    a_num = parse_number(a)\n    b_num = parse_number(b)\n\n    if a_num is None or b_num is None:\n        return None\n\n    if a_num == b_num:\n        return None\n\n    return b if b_num > a_num else a", "execution_time": 63.948594093322754, "code": "def compare_one(a, b):\n    \"\"\"\n    Create a function that takes integers, floats, or strings representing\n    real numbers, and returns the larger variable in its given variable type.\n    Return None if the values are equal.\n    Note: If a real number is represented as a string, the floating point might be . or ,\n\n    compare_one(1, 2.5) ➞ 2.5\n    compare_one(1, \"2,3\") ➞ \"2,3\"\n    compare_one(\"5,1\", \"6\") ➞ \"6\"\n    compare_one(\"1\", 1) ➞ None\n    \"\"\"\n    def parse_number(x):\n        if isinstance(x, (int, float)):\n            return float(x)\n        if isinstance(x, str):\n            try:\n                return float(x.replace(',', '.'))\n            except ValueError:\n                return None\n\n    a_num = parse_number(a)\n    b_num = parse_number(b)\n\n    if a_num is None or b_num is None:\n        return None\n\n    if a_num == b_num:\n        return None\n\n    return b if b_num > a_num else a", "problem_index": 54, "partial_success": true, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "b5755f6f", "prompt": "\n\ndef median(l: list):\n    \"\"\"Return median of elements in the list l.\n    >>> median([3, 1, 2, 4, 5])\n    3\n    >>> median([-10, 4, 6, 1000, 10, 20])\n    15.0\n    \"\"\"\n", "solution": "def median(l: list) -> float:\n    \"\"\"\n    Return median of elements in the list l.\n    \n    Args:\n        l (list): A list of numerical values (int or float).\n    \n    Returns:\n        float: The median of the list.\n    \n    Raises:\n        ValueError: If the list is empty.\n    \"\"\"\n    if not l:\n        raise ValueError(\"Cannot calculate median of an empty list\")\n    \n    sorted_l = sorted(l)\n    n = len(sorted_l)\n    \n    if n % 2 == 1:\n        return sorted_l[n // 2]\n    else:\n        return (sorted_l[n // 2 - 1] + sorted_l[n // 2]) / 2.0", "execution_time": 20.196606874465942, "code": "def median(l: list) -> float:\n    \"\"\"\n    Return median of elements in the list l.\n    \n    Args:\n        l (list): A list of numerical values (int or float).\n    \n    Returns:\n        float: The median of the list.\n    \n    Raises:\n        ValueError: If the list is empty.\n    \"\"\"\n    if not l:\n        raise ValueError(\"Cannot calculate median of an empty list\")\n    \n    sorted_l = sorted(l)\n    n = len(sorted_l)\n    \n    if n % 2 == 1:\n        return sorted_l[n // 2]\n    else:\n        return (sorted_l[n // 2 - 1] + sorted_l[n // 2]) / 2.0", "problem_index": 55, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "e4050711", "prompt": "\ndef valid_date(date):\n    \"\"\"You have to write a function which validates a given date string and\n    returns True if the date is valid otherwise False.\n    The date is valid if all of the following rules are satisfied:\n    1. The date string is not empty.\n    2. The number of days is not less than 1 or higher than 31 days for months 1,3,5,7,8,10,12. And the number of days is not less than 1 or higher than 30 days for months 4,6,9,11. And, the number of days is not less than 1 or higher than 29 for the month 2.\n    3. The months should not be less than 1 or higher than 12.\n    4. The date should be in the format: mm-dd-yyyy\n\n    for example: \n    valid_date('03-11-2000') => True\n\n    valid_date('15-01-2012') => False\n\n    valid_date('04-0-2040') => False\n\n    valid_date('06-04-2020') => True\n\n    valid_date('06/04/2020') => False\n    \"\"\"\n", "solution": "```python\ndef valid_date(date):\n    if not date or len(date) != 10 or date[2] != '-' or date[5] != '-':\n        return False\n    \n    try:\n        month = int(date[0:2])\n        day = int(date[3:5])\n        year = int(date[6:])\n    except ValueError:\n        return False\n    \n    if month < 1 or month > 12:\n        return False\n    \n    days_in_month = {\n        1: 31, 2: 29, 3: 31, 4: 30, \n        5: 31, 6: 30, 7: 31, 8: 31, \n        9: 30, 10: 31, 11: 30, 12: 31\n    }\n\n    if year % 4 == 0 and (year % 100 != 0 or year % 400 == 0):\n        days_in_month[2] = 29\n    else:\n        days_in_month[2] = 28\n    \n    if day < 1 or day > days_in_month[month]:\n        return False\n    \n    return True\n```", "execution_time": 76.29617094993591, "code": "def valid_date(date):\n    if not date or len(date) != 10 or date[2] != '-' or date[5] != '-':\n        return False\n    \n    try:\n        month = int(date[0:2])\n        day = int(date[3:5])\n        year = int(date[6:])\n    except ValueError:\n        return False\n    \n    if month < 1 or month > 12:\n        return False\n    \n    days_in_month = {\n        1: 31, 2: 29, 3: 31, 4: 30, \n        5: 31, 6: 30, 7: 31, 8: 31, \n        9: 30, 10: 31, 11: 30, 12: 31\n    }\n\n    if year % 4 == 0 and (year % 100 != 0 or year % 400 == 0):\n        days_in_month[2] = 29\n    else:\n        days_in_month[2] = 28\n    \n    if day < 1 or day > days_in_month[month]:\n        return False\n    \n    return True", "problem_index": 56, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "2f72424b", "prompt": "\n\ndef correct_bracketing(brackets: str):\n    \"\"\" brackets is a string of \"(\" and \")\".\n    return True if every opening bracket has a corresponding closing bracket.\n\n    >>> correct_bracketing(\"(\")\n    False\n    >>> correct_bracketing(\"()\")\n    True\n    >>> correct_bracketing(\"(()())\")\n    True\n    >>> correct_bracketing(\")(()\")\n    False\n    \"\"\"\n", "solution": "```python\ndef correct_bracketing(brackets: str) -> bool:\n    \"\"\" brackets is a string of \"(\" and \")\".\n    return True if every opening bracket has a corresponding closing bracket.\n\n    >>> correct_bracketing(\"(\")\n    False\n    >>> correct_bracketing(\"()\")\n    True\n    >>> correct_bracketing(\"(()())\")\n    True\n    >>> correct_bracketing(\")(()\")\n    False\n    >>> correct_bracketing(\")(\")\n    False\n    >>> correct_bracketing(\"\")  # Edge case: empty string\n    True\n    >>> correct_bracketing(\"((()))\")  # Edge case: multiple nested brackets\n    True\n    >>> correct_bracketing(\"(()\")  # Edge case: unbalanced opening brackets\n    False\n    >>> correct_bracketing(\"())\")  # Edge case: unbalanced closing brackets\n    False\n    >>> correct_bracketing(\"()()()\")  # Edge case: multiple pairs\n    True\n    \"\"\"\n    for char in brackets:\n        if char != '(' and char != ')':\n            raise ValueError(\"Input string contains invalid characters. Only '(' and ')' are allowed.\")\n    balance = 0\n    for char in brackets:\n        if char == '(':\n            balance += 1\n        elif char == ')':\n            balance -= 1\n        if balance < 0:\n            return False\n    return balance == 0\n```", "execution_time": 23.355415105819702, "code": "def correct_bracketing(brackets: str) -> bool:\n    \"\"\" brackets is a string of \"(\" and \")\".\n    return True if every opening bracket has a corresponding closing bracket.\n\n    >>> correct_bracketing(\"(\")\n    False\n    >>> correct_bracketing(\"()\")\n    True\n    >>> correct_bracketing(\"(()())\")\n    True\n    >>> correct_bracketing(\")(()\")\n    False\n    >>> correct_bracketing(\")(\")\n    False\n    >>> correct_bracketing(\"\")  # Edge case: empty string\n    True\n    >>> correct_bracketing(\"((()))\")  # Edge case: multiple nested brackets\n    True\n    >>> correct_bracketing(\"(()\")  # Edge case: unbalanced opening brackets\n    False\n    >>> correct_bracketing(\"())\")  # Edge case: unbalanced closing brackets\n    False\n    >>> correct_bracketing(\"()()()\")  # Edge case: multiple pairs\n    True\n    \"\"\"\n    for char in brackets:\n        if char != '(' and char != ')':\n            raise ValueError(\"Input string contains invalid characters. Only '(' and ')' are allowed.\")\n    balance = 0\n    for char in brackets:\n        if char == '(':\n            balance += 1\n        elif char == ')':\n            balance -= 1\n        if balance < 0:\n            return False\n    return balance == 0", "problem_index": 57, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "dd9aff8d", "prompt": "\ndef smallest_change(arr):\n    \"\"\"\n    Given an array arr of integers, find the minimum number of elements that\n    need to be changed to make the array palindromic. A palindromic array is an array that\n    is read the same backwards and forwards. In one change, you can change one element to any other element.\n\n    For example:\n    smallest_change([1,2,3,5,4,7,9,6]) == 4\n    smallest_change([1, 2, 3, 4, 3, 2, 2]) == 1\n    smallest_change([1, 2, 3, 2, 1]) == 0\n    \"\"\"\n", "solution": "```python\ndef smallest_change(arr):\n    \"\"\"\n    Given an array arr of integers, find the minimum number of elements that\n    need to be changed to make the array palindromic. A palindromic array is an array that\n    is read the same backwards and forwards. In one change, you can change one element to any other element.\n\n    For example:\n    smallest_change([1,2,3,5,4,7,9,6]) == 4\n    smallest_change([1, 2, 3, 4, 3, 2, 2]) == 1\n    smallest_change([1, 2, 3, 2, 1]) == 0\n    \"\"\"\n    if not isinstance(arr, list):\n        raise TypeError(\"Input must be a list.\")\n    if not all(isinstance(x, int) for x in arr):\n        raise TypeError(\"Input list must contain only integers.\")\n\n    if not arr:\n        return 0\n    \n    left = 0\n    right = len(arr) - 1\n    changes = 0\n    \n    while left < right:\n        if arr[left] != arr[right]:\n            changes += 1\n        left += 1\n        right -= 1\n    \n    return changes\n```", "execution_time": 84.23059272766113, "code": "def smallest_change(arr):\n    \"\"\"\n    Given an array arr of integers, find the minimum number of elements that\n    need to be changed to make the array palindromic. A palindromic array is an array that\n    is read the same backwards and forwards. In one change, you can change one element to any other element.\n\n    For example:\n    smallest_change([1,2,3,5,4,7,9,6]) == 4\n    smallest_change([1, 2, 3, 4, 3, 2, 2]) == 1\n    smallest_change([1, 2, 3, 2, 1]) == 0\n    \"\"\"\n    if not isinstance(arr, list):\n        raise TypeError(\"Input must be a list.\")\n    if not all(isinstance(x, int) for x in arr):\n        raise TypeError(\"Input list must contain only integers.\")\n\n    if not arr:\n        return 0\n    \n    left = 0\n    right = len(arr) - 1\n    changes = 0\n    \n    while left < right:\n        if arr[left] != arr[right]:\n            changes += 1\n        left += 1\n        right -= 1\n    \n    return changes", "problem_index": 58, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "9bb50774", "prompt": "\n\ndef sort_third(l: list):\n    \"\"\"This function takes a list l and returns a list l' such that\n    l' is identical to l in the indicies that are not divisible by three, while its values at the indicies that are divisible by three are equal\n    to the values of the corresponding indicies of l, but sorted.\n    >>> sort_third([1, 2, 3])\n    [1, 2, 3]\n    >>> sort_third([5, 6, 3, 4, 8, 9, 2])\n    [2, 6, 3, 4, 8, 9, 5]\n    \"\"\"\n", "solution": "def sort_every_third(l: list) -> list:\n    \"\"\"This function takes a list l and returns a new list l' such that\n    l' is identical to l in the indices that are not divisible by three, while the values at indices divisible by three in l' are equal\n    to the sorted values of the corresponding indices in l. Only the values at indices divisible by three are sorted and replaced.\n    >>> sort_every_third([1, 2, 3])\n    [3, 2, 1]\n    >>> sort_every_third([5, 6, 3, 4, 8, 9, 2])\n    [2, 6, 3, 4, 8, 5, 3]\n    \"\"\"\n    if not l:\n        return l\n    result = l.copy()\n    third_indices_values = sorted(result[::3], reverse=True)\n    for i, val in zip(range(0, len(result), 3), third_indices_values):\n        result[i] = val\n    return result", "execution_time": 77.56297278404236, "code": "def sort_every_third(l: list) -> list:\n    \"\"\"This function takes a list l and returns a new list l' such that\n    l' is identical to l in the indices that are not divisible by three, while the values at indices divisible by three in l' are equal\n    to the sorted values of the corresponding indices in l. Only the values at indices divisible by three are sorted and replaced.\n    >>> sort_every_third([1, 2, 3])\n    [3, 2, 1]\n    >>> sort_every_third([5, 6, 3, 4, 8, 9, 2])\n    [2, 6, 3, 4, 8, 5, 3]\n    \"\"\"\n    if not l:\n        return l\n    result = l.copy()\n    third_indices_values = sorted(result[::3], reverse=True)\n    for i, val in zip(range(0, len(result), 3), third_indices_values):\n        result[i] = val\n    return result", "problem_index": 59, "partial_success": true, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "5194348c", "prompt": "\ndef reverse_delete(s,c):\n    \"\"\"Task\n    We are given two strings s and c, you have to deleted all the characters in s that are equal to any character in c\n    then check if the result string is palindrome.\n    A string is called palindrome if it reads the same backward as forward.\n    You should return a tuple containing the result string and True/False for the check.\n    Example\n    For s = \"abcde\", c = \"ae\", the result should be ('bcd',False)\n    For s = \"abcdef\", c = \"b\"  the result should be ('acdef',False)\n    For s = \"abcdedcba\", c = \"ab\", the result should be ('cdedc',True)\n    \"\"\"\n", "solution": "```python\ndef reverse_delete(s: str, c: str) -> tuple:\n    \"\"\"\n    This function deletes all characters in string s that are equal to any character in string c,\n    then checks if the resulting string is a palindrome.\n\n    Args:\n        s (str): The original string.\n        c (str): The string containing characters to be deleted from s.\n\n    Returns:\n        tuple: A tuple containing the resulting string and a boolean indicating whether the string is a palindrome.\n\n    Raises:\n        ValueError: If either s or c is not a string, or if c is an empty string.\n    \"\"\"\n    if not isinstance(s, str) or not isinstance(c, str):\n        raise ValueError(\"Both s and c must be strings.\")\n    if not c:\n        raise ValueError(\"c cannot be an empty string.\")\n    \n    result = ''.join(char for char in s if char not in c)\n    is_palindrome = result == result[::-1]\n    \n    return result, is_palindrome\n```", "execution_time": 64.35216903686523, "code": "def reverse_delete(s: str, c: str) -> tuple:\n    \"\"\"\n    This function deletes all characters in string s that are equal to any character in string c,\n    then checks if the resulting string is a palindrome.\n\n    Args:\n        s (str): The original string.\n        c (str): The string containing characters to be deleted from s.\n\n    Returns:\n        tuple: A tuple containing the resulting string and a boolean indicating whether the string is a palindrome.\n\n    Raises:\n        ValueError: If either s or c is not a string, or if c is an empty string.\n    \"\"\"\n    if not isinstance(s, str) or not isinstance(c, str):\n        raise ValueError(\"Both s and c must be strings.\")\n    if not c:\n        raise ValueError(\"c cannot be an empty string.\")\n    \n    result = ''.join(char for char in s if char not in c)\n    is_palindrome = result == result[::-1]\n    \n    return result, is_palindrome", "problem_index": 60, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "95d7abcf", "prompt": "\ndef maximum(arr, k):\n    \"\"\"\n    Given an array arr of integers and a positive integer k, return a sorted list \n    of length k with the maximum k numbers in arr.\n\n    Example 1:\n\n        Input: arr = [-3, -4, 5], k = 3\n        Output: [-4, -3, 5]\n\n    Example 2:\n\n        Input: arr = [4, -4, 4], k = 2\n        Output: [4, 4]\n\n    Example 3:\n\n        Input: arr = [-3, 2, 1, 2, -1, -2, 1], k = 1\n        Output: [2]\n\n    Note:\n        1. The length of the array will be in the range of [1, 1000].\n        2. The elements in the array will be in the range of [-1000, 1000].\n        3. 0 <= k <= len(arr)\n    \"\"\"\n", "solution": "```python\ndef maximum(arr, k):\n    \"\"\"\n    Returns a sorted list of the k maximum numbers from the input array.\n    \n    Args:\n        arr (list): Input list of integers\n        k (int): Number of maximum elements to return\n    \n    Returns:\n        list: Sorted list of k maximum numbers\n    \"\"\"\n    # Handle edge cases\n    if not arr or k <= 0:\n        return []\n    \n    if k > len(arr):\n        k = len(arr)\n    \n    import heapq\n    # Sort array in descending order and take first k elements\n    return heapq.nlargest(k, arr)\n```", "execution_time": 62.974924087524414, "code": "def maximum(arr, k):\n    \"\"\"\n    Returns a sorted list of the k maximum numbers from the input array.\n    \n    Args:\n        arr (list): Input list of integers\n        k (int): Number of maximum elements to return\n    \n    Returns:\n        list: Sorted list of k maximum numbers\n    \"\"\"\n    # Handle edge cases\n    if not arr or k <= 0:\n        return []\n    \n    if k > len(arr):\n        k = len(arr)\n    \n    import heapq\n    # Sort array in descending order and take first k elements\n    return heapq.nlargest(k, arr)", "problem_index": 61, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "cc18a627", "prompt": "\ndef prod_signs(arr):\n    \"\"\"\n    You are given an array arr of integers and you need to return\n    sum of magnitudes of integers multiplied by product of all signs\n    of each number in the array, represented by 1, -1 or 0.\n    Note: return None for empty arr.\n\n    Example:\n    >>> prod_signs([1, 2, 2, -4]) == -9\n    >>> prod_signs([0, 1]) == 0\n    >>> prod_signs([]) == None\n    \"\"\"\n", "solution": "```python\ndef prod_signs(arr):\n    \"\"\"\n    You are given an array arr of integers and you need to return\n    sum of magnitudes of integers multiplied by product of all signs\n    of each number in the array, represented by 1, -1 or 0.\n    Note: return None for empty arr.\n\n    Example:\n    >>> prod_signs([1, 2, 2, -4]) == -9\n    >>> prod_signs([0, 1]) == 0\n    >>> prod_signs([]) == None\n    \"\"\"\n    if not arr:\n        return None\n\n    sign_product = 1\n    magnitude_sum = 0\n\n    for num in arr:\n        magnitude_sum += abs(num)\n        if num == 0:\n            sign_product = 0\n        else:\n            sign_product *= math.copysign(1, num)\n\n    return magnitude_sum * sign_product\n```", "execution_time": 18.292665243148804, "code": "def prod_signs(arr):\n    \"\"\"\n    You are given an array arr of integers and you need to return\n    sum of magnitudes of integers multiplied by product of all signs\n    of each number in the array, represented by 1, -1 or 0.\n    Note: return None for empty arr.\n\n    Example:\n    >>> prod_signs([1, 2, 2, -4]) == -9\n    >>> prod_signs([0, 1]) == 0\n    >>> prod_signs([]) == None\n    \"\"\"\n    if not arr:\n        return None\n\n    sign_product = 1\n    magnitude_sum = 0\n\n    for num in arr:\n        magnitude_sum += abs(num)\n        if num == 0:\n            sign_product = 0\n        else:\n            sign_product *= math.copysign(1, num)\n\n    return magnitude_sum * sign_product", "problem_index": 62, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "8950d6e5", "prompt": "\n\ndef derivative(xs: list):\n    \"\"\" xs represent coefficients of a polynomial.\n    xs[0] + xs[1] * x + xs[2] * x^2 + ....\n     Return derivative of this polynomial in the same form.\n    >>> derivative([3, 1, 2, 4, 5])\n    [1, 4, 12, 20]\n    >>> derivative([1, 2, 3])\n    [2, 6]\n    \"\"\"\n", "solution": "```python\ndef derivative(xs: list[float]) -> list[float]:\n    \"\"\" xs represent coefficients of a polynomial in increasing order of power.\n    xs[0] + xs[1] * x + xs[2] * x^2 + ....\n     Returns the coefficients of the derivative polynomial in increasing order.\n    >>> derivative([3, 1, 2, 4, 5])\n    [1, 4, 12, 20]\n    >>> derivative([1, 2, 3])\n    [2, 6]\n    >>> derivative([0])\n    []\n    >>> derivative([0, 0, 0])\n    []\n    >>> derivative([1])\n    []\n    >>> derivative([])\n    []\n    \"\"\"\n    if not isinstance(xs, list):\n        raise TypeError(\"Input must be a list.\")\n    if any(not isinstance(coef, (int, float)) for coef in xs):\n        raise ValueError(\"All coefficients must be integers or floats.\")\n    if len(xs) > 0 and xs[0] != 0 and len(xs) == 1:\n        return []\n    \n    return [i * coef for i, coef in enumerate(xs) if i > 0]\n```", "execution_time": 23.32866406440735, "code": "def derivative(xs: list[float]) -> list[float]:\n    \"\"\" xs represent coefficients of a polynomial in increasing order of power.\n    xs[0] + xs[1] * x + xs[2] * x^2 + ....\n     Returns the coefficients of the derivative polynomial in increasing order.\n    >>> derivative([3, 1, 2, 4, 5])\n    [1, 4, 12, 20]\n    >>> derivative([1, 2, 3])\n    [2, 6]\n    >>> derivative([0])\n    []\n    >>> derivative([0, 0, 0])\n    []\n    >>> derivative([1])\n    []\n    >>> derivative([])\n    []\n    \"\"\"\n    if not isinstance(xs, list):\n        raise TypeError(\"Input must be a list.\")\n    if any(not isinstance(coef, (int, float)) for coef in xs):\n        raise ValueError(\"All coefficients must be integers or floats.\")\n    if len(xs) > 0 and xs[0] != 0 and len(xs) == 1:\n        return []\n    \n    return [i * coef for i, coef in enumerate(xs) if i > 0]", "problem_index": 63, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "3d5f8e5f", "prompt": "\ndef string_to_md5(text):\n    \"\"\"\n    Given a string 'text', return its md5 hash equivalent string.\n    If 'text' is an empty string, return None.\n\n    >>> string_to_md5('Hello world') == '3e25960a79dbc69b674cd4ec67a72c62'\n    \"\"\"\n", "solution": "```python\nimport hashlib\n\ndef string_to_md5(text):\n    \"\"\"\n    Given a string 'text', return its md5 hash equivalent string.\n    If 'text' is an empty string, return None.\n\n    >>> string_to_md5('Hello world') == '3e25960a79dbc69b674cd4ec67a72c62'\n    \"\"\"\n    if not text:\n        return None\n    \n    return hashlib.md5(text.encode('utf-8')).hexdigest()\n```", "execution_time": 19.006834030151367, "code": "import hashlib\n\ndef string_to_md5(text):\n    \"\"\"\n    Given a string 'text', return its md5 hash equivalent string.\n    If 'text' is an empty string, return None.\n\n    >>> string_to_md5('Hello world') == '3e25960a79dbc69b674cd4ec67a72c62'\n    \"\"\"\n    if not text:\n        return None\n    \n    return hashlib.md5(text.encode('utf-8')).hexdigest()", "problem_index": 64, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "803d0298", "prompt": "\ndef make_a_pile(n):\n    \"\"\"\n    Given a positive integer n, you have to make a pile of n levels of stones.\n    The first level has n stones.\n    The number of stones in the next level is:\n        - the next odd number if n is odd.\n        - the next even number if n is even.\n    Return the number of stones in each level in a list, where element at index\n    i represents the number of stones in the level (i+1).\n\n    Examples:\n    >>> make_a_pile(3)\n    [3, 5, 7]\n    \"\"\"\n", "solution": "def make_a_pile(n):\n    \"\"\"\n    Given a positive integer n, you have to make a pile of n levels of stones.\n    The first level has n stones.\n    The number of stones in the next level is:\n        - the next odd number if n is odd.\n        - the next even number if n is even.\n    Return the number of stones in each level in a list, where element at index\n    i represents the number of stones in the level (i+1).\n\n    Args:\n        n (int): The number of levels in the pile.\n\n    Returns:\n        list: A list of integers representing the number of stones in each level.\n    \"\"\"\n    if not isinstance(n, int) or n <= 0:\n        raise ValueError(\"n must be a positive integer\")\n\n    pile = [n]\n    stones_in_level = n\n    for _ in range(n - 1):\n        stones_in_level += 2\n        pile.append(stones_in_level)\n    return pile\n", "execution_time": 71.97338700294495, "code": "def make_a_pile(n):\n    \"\"\"\n    Given a positive integer n, you have to make a pile of n levels of stones.\n    The first level has n stones.\n    The number of stones in the next level is:\n        - the next odd number if n is odd.\n        - the next even number if n is even.\n    Return the number of stones in each level in a list, where element at index\n    i represents the number of stones in the level (i+1).\n\n    Args:\n        n (int): The number of levels in the pile.\n\n    Returns:\n        list: A list of integers representing the number of stones in each level.\n    \"\"\"\n    if not isinstance(n, int) or n <= 0:\n        raise ValueError(\"n must be a positive integer\")\n\n    pile = [n]\n    stones_in_level = n\n    for _ in range(n - 1):\n        stones_in_level += 2\n        pile.append(stones_in_level)\n    return pile", "problem_index": 65, "partial_success": true, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "914222f4", "prompt": "\ndef unique_digits(x):\n    \"\"\"Given a list of positive integers x. return a sorted list of all \n    elements that hasn't any even digit.\n\n    Note: Returned list should be sorted in increasing order.\n    \n    For example:\n    >>> unique_digits([15, 33, 1422, 1])\n    [1, 15, 33]\n    >>> unique_digits([152, 323, 1422, 10])\n    []\n    \"\"\"\n", "solution": "```python\nfrom typing import List\n\ndef unique_digits(x: List[int]) -> List[int]:\n    \"\"\"Given a list of positive integers x. return a sorted list of all \n    elements that doesn't have any even digit.\n\n    Note: Returned list should be sorted in increasing order. The returned list contains integers without even digits.\n    \n    For example:\n    >>> unique_digits([15, 33, 1422, 1])\n    [1, 15, 33]\n    >>> unique_digits([152, 323, 1422, 10])\n    []\n    \"\"\"\n    if not isinstance(x, list) or not all(isinstance(i, int) and i > 0 for i in x):\n        raise ValueError(\"Input must be a list of positive integers.\")\n    \n    def has_even_digit(n):\n        return any(int(digit) % 2 == 0 for digit in str(n))\n    \n    result = [num for num in x if not has_even_digit(num)]\n    return sorted(result)\n```", "execution_time": 27.952786207199097, "code": "from typing import List\n\ndef unique_digits(x: List[int]) -> List[int]:\n    \"\"\"Given a list of positive integers x. return a sorted list of all \n    elements that doesn't have any even digit.\n\n    Note: Returned list should be sorted in increasing order. The returned list contains integers without even digits.\n    \n    For example:\n    >>> unique_digits([15, 33, 1422, 1])\n    [1, 15, 33]\n    >>> unique_digits([152, 323, 1422, 10])\n    []\n    \"\"\"\n    if not isinstance(x, list) or not all(isinstance(i, int) and i > 0 for i in x):\n        raise ValueError(\"Input must be a list of positive integers.\")\n    \n    def has_even_digit(n):\n        return any(int(digit) % 2 == 0 for digit in str(n))\n    \n    result = [num for num in x if not has_even_digit(num)]\n    return sorted(result)", "problem_index": 66, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "31ad4168", "prompt": "\n\ndef add(x: int, y: int):\n    \"\"\"Add two numbers x and y\n    >>> add(2, 3)\n    5\n    >>> add(5, 7)\n    12\n    \"\"\"\n", "solution": "```python\ndef add(x: int, y: int) -> int:\n    \"\"\"Add two numbers x and y\n    >>> add(2, 3)\n    5\n    >>> add(5, 7)\n    12\n    \"\"\"\n    return x + y\n```", "execution_time": 23.11264181137085, "code": "def add(x: int, y: int) -> int:\n    \"\"\"Add two numbers x and y\n    >>> add(2, 3)\n    5\n    >>> add(5, 7)\n    12\n    \"\"\"\n    return x + y", "problem_index": 67, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "246a6e70", "prompt": "from typing import List\n\n\ndef intersperse(numbers: List[int], delimeter: int) -> List[int]:\n    \"\"\" Insert a number 'delimeter' between every two consecutive elements of input list `numbers'\n    >>> intersperse([], 4)\n    []\n    >>> intersperse([1, 2, 3], 4)\n    [1, 4, 2, 4, 3]\n    \"\"\"\n", "solution": "```python\nfrom typing import List\nfrom itertools import chain\n\ndef intersperse(numbers: List[int], delimiter: int) -> List[int]:\n    \"\"\" Insert a number 'delimiter' between every two consecutive elements of input list `numbers'\n    The function raises a TypeError if the input is not a list of integers or if the delimiter is not an integer.\n    >>> intersperse([], 4)\n    []\n    >>> intersperse([1, 2, 3], 4)\n    [1, 4, 2, 4, 3]\n    >>> intersperse([5], 0)\n    [5]\n    >>> intersperse([1, 2, 3, 4], 0)\n    [1, 0, 2, 0, 3, 0, 4]\n    >>> intersperse([1, 2, 3], -1)\n    [1, -1, 2, -1, 3]\n    \"\"\"\n    if not isinstance(numbers, list):\n        raise TypeError(\"Input must be a list of integers.\")\n    if not all(isinstance(x, int) for x in numbers):\n        raise ValueError(\"All elements in the input list must be integers.\")\n    if not isinstance(delimiter, int):\n        raise TypeError(\"Delimiter must be an integer.\")\n    \n    if len(numbers) == 0:\n        return []\n    \n    result = list(chain.from_iterable((numbers[i], delimiter) for i in range(len(numbers) - 1)))\n    result.append(numbers[-1])\n    \n    return result\n```", "execution_time": 26.008817195892334, "code": "from typing import List\nfrom itertools import chain\n\ndef intersperse(numbers: List[int], delimiter: int) -> List[int]:\n    \"\"\" Insert a number 'delimiter' between every two consecutive elements of input list `numbers'\n    The function raises a TypeError if the input is not a list of integers or if the delimiter is not an integer.\n    >>> intersperse([], 4)\n    []\n    >>> intersperse([1, 2, 3], 4)\n    [1, 4, 2, 4, 3]\n    >>> intersperse([5], 0)\n    [5]\n    >>> intersperse([1, 2, 3, 4], 0)\n    [1, 0, 2, 0, 3, 0, 4]\n    >>> intersperse([1, 2, 3], -1)\n    [1, -1, 2, -1, 3]\n    \"\"\"\n    if not isinstance(numbers, list):\n        raise TypeError(\"Input must be a list of integers.\")\n    if not all(isinstance(x, int) for x in numbers):\n        raise ValueError(\"All elements in the input list must be integers.\")\n    if not isinstance(delimiter, int):\n        raise TypeError(\"Delimiter must be an integer.\")\n    \n    if len(numbers) == 0:\n        return []\n    \n    result = list(chain.from_iterable((numbers[i], delimiter) for i in range(len(numbers) - 1)))\n    result.append(numbers[-1])\n    \n    return result", "problem_index": 68, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "b6ba3e91", "prompt": "\ndef get_closest_vowel(word):\n    \"\"\"You are given a word. Your task is to find the closest vowel that stands between \n    two consonants from the right side of the word (case sensitive).\n    \n    Vowels in the beginning and ending doesn't count. Return empty string if you didn't\n    find any vowel met the above condition. \n\n    You may assume that the given string contains English letter only.\n\n    Example:\n    get_closest_vowel(\"yogurt\") ==> \"u\"\n    get_closest_vowel(\"FULL\") ==> \"U\"\n    get_closest_vowel(\"quick\") ==> \"\"\n    get_closest_vowel(\"ab\") ==> \"\"\n    \"\"\"\n", "solution": "```python\ndef get_closest_vowel(word: str) -> str:\n    \"\"\"You are given a word. Your task is to find the closest vowel that stands between \n    two consonants from the right side of the word (case sensitive).\n    \n    Vowels in the beginning and ending doesn't count. Return empty string if you didn't\n    find any vowel met the above condition. \n\n    You may assume that the given string contains English letter only.\n\n    Example:\n    get_closest_vowel(\"yogurt\") ==> \"u\"\n    get_closest_vowel(\"FULL\") ==> \"U\"\n    get_closest_vowel(\"quick\") ==> \"\"\n    get_closest_vowel(\"ab\") ==> \"\"\n    \"\"\"\n    vowels = 'aeiouAEIOU'\n    \n    # Check if the word has less than 3 characters\n    if len(word) < 3:\n        return ''\n    \n    # Iterate over the word from right to left, excluding the first and last characters\n    for i in reversed(range(1, len(word) - 1)):\n        # Check if the current character is a vowel and its neighbors are consonants\n        if word[i] in vowels and word[i-1] not in vowels and word[i+1] not in vowels:\n            return word[i]\n    \n    # If no vowel meets the condition, return an empty string\n    return ''\n```", "execution_time": 62.210071086883545, "code": "def get_closest_vowel(word: str) -> str:\n    \"\"\"You are given a word. Your task is to find the closest vowel that stands between \n    two consonants from the right side of the word (case sensitive).\n    \n    Vowels in the beginning and ending doesn't count. Return empty string if you didn't\n    find any vowel met the above condition. \n\n    You may assume that the given string contains English letter only.\n\n    Example:\n    get_closest_vowel(\"yogurt\") ==> \"u\"\n    get_closest_vowel(\"FULL\") ==> \"U\"\n    get_closest_vowel(\"quick\") ==> \"\"\n    get_closest_vowel(\"ab\") ==> \"\"\n    \"\"\"\n    vowels = 'aeiouAEIOU'\n    \n    # Check if the word has less than 3 characters\n    if len(word) < 3:\n        return ''\n    \n    # Iterate over the word from right to left, excluding the first and last characters\n    for i in reversed(range(1, len(word) - 1)):\n        # Check if the current character is a vowel and its neighbors are consonants\n        if word[i] in vowels and word[i-1] not in vowels and word[i+1] not in vowels:\n            return word[i]\n    \n    # If no vowel meets the condition, return an empty string\n    return ''", "problem_index": 69, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "2da6e453", "prompt": "\ndef intersection(interval1, interval2):\n    \"\"\"You are given two intervals,\n    where each interval is a pair of integers. For example, interval = (start, end) = (1, 2).\n    The given intervals are closed which means that the interval (start, end)\n    includes both start and end.\n    For each given interval, it is assumed that its start is less or equal its end.\n    Your task is to determine whether the length of intersection of these two \n    intervals is a prime number.\n    Example, the intersection of the intervals (1, 3), (2, 4) is (2, 3)\n    which its length is 1, which not a prime number.\n    If the length of the intersection is a prime number, return \"YES\",\n    otherwise, return \"NO\".\n    If the two intervals don't intersect, return \"NO\".\n\n\n    [input/output] samples:\n    intersection((1, 2), (2, 3)) ==> \"NO\"\n    intersection((-1, 1), (0, 4)) ==> \"NO\"\n    intersection((-3, -1), (-5, 5)) ==> \"YES\"\n    \"\"\"\n", "solution": "```python\ndef is_prime(n):\n    \"\"\"Check if a number is prime.\"\"\"\n    if n < 2:\n        return False\n    for i in range(2, int(n**0.5) + 1):\n        if n % i == 0:\n            return False\n    return True\n\ndef intersection(interval1, interval2):\n    \"\"\"You are given two intervals,\n    where each interval is a pair of integers. For example, interval = (start, end) = (1, 2).\n    The given intervals are closed which means that the interval (start, end)\n    includes both start and end.\n    For each given interval, it is assumed that its start is less or equal its end.\n    Your task is to determine whether the length of intersection of these two \n    intervals is a prime number.\n    Example, the intersection of the intervals (1, 3), (2, 4) is (2, 3)\n    which its length is 1, which not a prime number.\n    If the length of the intersection is a prime number, return \"YES\",\n    otherwise, return \"NO\".\n    If the two intervals don't intersect, return \"NO\".\n\n\n    [input/output] samples:\n    intersection((1, 2), (2, 3)) ==> \"NO\"\n    intersection((-1, 1), (0, 4)) ==> \"NO\"\n    intersection((-3, -1), (-5, 5)) ==> \"YES\"\n    \"\"\"\n    start1, end1 = interval1\n    start2, end2 = interval2\n\n    intersection_start = max(start1, start2)\n    intersection_end = min(end1, end2)\n\n    if intersection_start > intersection_end:\n        return \"NO\"\n\n    intersection_length = intersection_end - intersection_start + 1\n\n    if is_prime(intersection_length):\n        return \"YES\"\n    else:\n        return \"NO\"\n```", "execution_time": 62.11274075508118, "code": "def is_prime(n):\n    \"\"\"Check if a number is prime.\"\"\"\n    if n < 2:\n        return False\n    for i in range(2, int(n**0.5) + 1):\n        if n % i == 0:\n            return False\n    return True\n\ndef intersection(interval1, interval2):\n    \"\"\"You are given two intervals,\n    where each interval is a pair of integers. For example, interval = (start, end) = (1, 2).\n    The given intervals are closed which means that the interval (start, end)\n    includes both start and end.\n    For each given interval, it is assumed that its start is less or equal its end.\n    Your task is to determine whether the length of intersection of these two \n    intervals is a prime number.\n    Example, the intersection of the intervals (1, 3), (2, 4) is (2, 3)\n    which its length is 1, which not a prime number.\n    If the length of the intersection is a prime number, return \"YES\",\n    otherwise, return \"NO\".\n    If the two intervals don't intersect, return \"NO\".\n\n\n    [input/output] samples:\n    intersection((1, 2), (2, 3)) ==> \"NO\"\n    intersection((-1, 1), (0, 4)) ==> \"NO\"\n    intersection((-3, -1), (-5, 5)) ==> \"YES\"\n    \"\"\"\n    start1, end1 = interval1\n    start2, end2 = interval2\n\n    intersection_start = max(start1, start2)\n    intersection_end = min(end1, end2)\n\n    if intersection_start > intersection_end:\n        return \"NO\"\n\n    intersection_length = intersection_end - intersection_start + 1\n\n    if is_prime(intersection_length):\n        return \"YES\"\n    else:\n        return \"NO\"", "problem_index": 70, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "a8ef7405", "prompt": "\ndef double_the_difference(lst):\n    '''\n    Given a list of numbers, return the sum of squares of the numbers\n    in the list that are odd. Ignore numbers that are negative or not integers.\n    \n    double_the_difference([1, 3, 2, 0]) == 1 + 9 + 0 + 0 = 10\n    double_the_difference([-1, -2, 0]) == 0\n    double_the_difference([9, -2]) == 81\n    double_the_difference([0]) == 0  \n   \n    If the input list is empty, return 0.\n    '''\n", "solution": "```python\ndef double_the_difference(lst):\n    '''\n    Given a list of numbers, return the sum of squares of the numbers\n    in the list that are odd. Ignore numbers that are negative or not integers.\n    \n    double_the_difference([1, 3, 2, 0]) == 1 + 9 + 0 + 0 = 10\n    double_the_difference([-1, -2, 0]) == 0\n    double_the_difference([9, -2]) == 81\n    double_the_difference([0]) == 0  \n   \n    If the input list is empty, return 0.\n    '''\n    return sum([i**2 for i in lst if isinstance(i, int) and i >= 0 and i % 2 != 0])\n```", "execution_time": 68.77505707740784, "code": "def double_the_difference(lst):\n    '''\n    Given a list of numbers, return the sum of squares of the numbers\n    in the list that are odd. Ignore numbers that are negative or not integers.\n    \n    double_the_difference([1, 3, 2, 0]) == 1 + 9 + 0 + 0 = 10\n    double_the_difference([-1, -2, 0]) == 0\n    double_the_difference([9, -2]) == 81\n    double_the_difference([0]) == 0  \n   \n    If the input list is empty, return 0.\n    '''\n    return sum([i**2 for i in lst if isinstance(i, int) and i >= 0 and i % 2 != 0])", "problem_index": 71, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "e5952af3", "prompt": "\n\ndef modp(n: int, p: int):\n    \"\"\"Return 2^n modulo p (be aware of numerics).\n    >>> modp(3, 5)\n    3\n    >>> modp(1101, 101)\n    2\n    >>> modp(0, 101)\n    1\n    >>> modp(3, 11)\n    8\n    >>> modp(100, 101)\n    1\n    \"\"\"\n", "solution": "```python\ndef modp(n: int, p: int) -> int:\n    \"\"\"\n    Calculate 2^n mod p.\n\n    Args:\n        n: The exponent.\n        p: The modulus.\n\n    Returns:\n        The result of 2^n mod p.\n\n    Raises:\n        TypeError: If n or p are not integers.\n        ValueError: If p is not a positive integer or if n is negative.\n    \"\"\"\n    if not isinstance(n, int) or not isinstance(p, int):\n        raise TypeError(\"Both n and p must be integers.\")\n    if p <= 0:\n        raise ValueError(\"p must be a positive integer.\")\n    if n < 0:\n        raise ValueError(\"n must be a non-negative integer.\")\n    result = pow(2, n, p)\n    return result\n```", "execution_time": 21.733614921569824, "code": "def modp(n: int, p: int) -> int:\n    \"\"\"\n    Calculate 2^n mod p.\n\n    Args:\n        n: The exponent.\n        p: The modulus.\n\n    Returns:\n        The result of 2^n mod p.\n\n    Raises:\n        TypeError: If n or p are not integers.\n        ValueError: If p is not a positive integer or if n is negative.\n    \"\"\"\n    if not isinstance(n, int) or not isinstance(p, int):\n        raise TypeError(\"Both n and p must be integers.\")\n    if p <= 0:\n        raise ValueError(\"p must be a positive integer.\")\n    if n < 0:\n        raise ValueError(\"n must be a non-negative integer.\")\n    result = pow(2, n, p)\n    return result", "problem_index": 72, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "d89da275", "prompt": "\n\ndef max_element(l: list):\n    \"\"\"Return maximum element in the list.\n    >>> max_element([1, 2, 3])\n    3\n    >>> max_element([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n    123\n    \"\"\"\n", "solution": "```python\ndef max_element(l: list[int]) -> int:\n    \"\"\"Return maximum element in the list.\n    >>> max_element([1, 2, 3])\n    3\n    >>> max_element([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n    123\n    \"\"\"\n    if not isinstance(l, list):\n        raise TypeError(\"Input must be a list\")\n    if len(l) == 0:\n        raise ValueError(\"List cannot be empty\")\n    return max(l)\n```", "execution_time": 21.936504125595093, "code": "def max_element(l: list[int]) -> int:\n    \"\"\"Return maximum element in the list.\n    >>> max_element([1, 2, 3])\n    3\n    >>> max_element([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n    123\n    \"\"\"\n    if not isinstance(l, list):\n        raise TypeError(\"Input must be a list\")\n    if len(l) == 0:\n        raise ValueError(\"List cannot be empty\")\n    return max(l)", "problem_index": 73, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "2eba079b", "prompt": "\ndef is_happy(s):\n    \"\"\"You are given a string s.\n    Your task is to check if the string is happy or not.\n    A string is happy if its length is at least 3 and every 3 consecutive letters are distinct\n    For example:\n    is_happy(a) => False\n    is_happy(aa) => False\n    is_happy(abcd) => True\n    is_happy(aabb) => False\n    is_happy(adb) => True\n    is_happy(xyy) => False\n    \"\"\"\n", "solution": "```python\ndef is_happy(s: str) -> bool:\n    \"\"\"\n    Checks if a given string is happy.\n    \n    A string is happy if its length is at least 3 and every 3 consecutive letters are distinct; otherwise, it is not happy.\n    \n    Args:\n        s (str): The input string to check.\n    \n    Returns:\n        bool: True if the string is happy, False otherwise.\n    \"\"\"\n    # Check if the input is a string\n    if not isinstance(s, str):\n        raise TypeError(\"Input must be a string\")\n    \n    # Check if the length of the string is less than 3\n    if len(s) < 3:\n        return False\n\n    # Iterate over the string with a sliding window of size 3\n    for i in range(len(s) - 2):\n        # Check if all characters in the current window are distinct\n        if len(set(s[i:i+3])) != 3:\n            return False\n    return True\n```", "execution_time": 21.046092987060547, "code": "def is_happy(s: str) -> bool:\n    \"\"\"\n    Checks if a given string is happy.\n    \n    A string is happy if its length is at least 3 and every 3 consecutive letters are distinct; otherwise, it is not happy.\n    \n    Args:\n        s (str): The input string to check.\n    \n    Returns:\n        bool: True if the string is happy, False otherwise.\n    \"\"\"\n    # Check if the input is a string\n    if not isinstance(s, str):\n        raise TypeError(\"Input must be a string\")\n    \n    # Check if the length of the string is less than 3\n    if len(s) < 3:\n        return False\n\n    # Iterate over the string with a sliding window of size 3\n    for i in range(len(s) - 2):\n        # Check if all characters in the current window are distinct\n        if len(set(s[i:i+3])) != 3:\n            return False\n    return True", "problem_index": 74, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "f495231b", "prompt": "\ndef iscube(a):\n    '''\n    Write a function that takes an integer a and returns True \n    if this ingeger is a cube of some integer number.\n    Note: you may assume the input is always valid.\n    Examples:\n    iscube(1) ==> True\n    iscube(2) ==> False\n    iscube(-1) ==> True\n    iscube(64) ==> True\n    iscube(0) ==> True\n    iscube(180) ==> False\n    '''\n", "solution": "```python\ndef iscube(a):\n    import math\n    if not isinstance(a, int):\n        raise ValueError(\"Input must be an integer.\")\n    if a < 0:\n        a = -a\n    cube_root = round(a ** (1/3))\n    return math.isclose(cube_root ** 3, a)\n```", "execution_time": 19.53652811050415, "code": "def iscube(a):\n    import math\n    if not isinstance(a, int):\n        raise ValueError(\"Input must be an integer.\")\n    if a < 0:\n        a = -a\n    cube_root = round(a ** (1/3))\n    return math.isclose(cube_root ** 3, a)", "problem_index": 75, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "8391e9a5", "prompt": "\n\ndef unique(l: list):\n    \"\"\"Return sorted unique elements in a list\n    >>> unique([5, 3, 5, 2, 3, 3, 9, 0, 123])\n    [0, 2, 3, 5, 9, 123]\n    \"\"\"\n", "solution": "```python\ndef unique(l: list[int]) -> list[int]:\n    \"\"\"Return sorted unique elements in a list\n    >>> unique([5, 3, 5, 2, 3, 3, 9, 0, 123])\n    [0, 2, 3, 5, 9, 123]\n    >>> unique([])\n    []\n    >>> unique([1])\n    [1]\n    >>> unique([1, 1, 1, 1])\n    [1]\n    >>> unique([-1, 0, 1, -1, 0])\n    [-1, 0, 1]\n    >>> unique([5, 3, 5, 2, 3, 3, 9, 0, 123, -5, -10])\n    [-10, -5, 0, 2, 3, 5, 9, 123]\n    \"\"\"\n    if not isinstance(l, list):\n        raise TypeError(\"Input must be a list\")\n    return sorted(set(l))\n```", "execution_time": 25.197865962982178, "code": "def unique(l: list[int]) -> list[int]:\n    \"\"\"Return sorted unique elements in a list\n    >>> unique([5, 3, 5, 2, 3, 3, 9, 0, 123])\n    [0, 2, 3, 5, 9, 123]\n    >>> unique([])\n    []\n    >>> unique([1])\n    [1]\n    >>> unique([1, 1, 1, 1])\n    [1]\n    >>> unique([-1, 0, 1, -1, 0])\n    [-1, 0, 1]\n    >>> unique([5, 3, 5, 2, 3, 3, 9, 0, 123, -5, -10])\n    [-10, -5, 0, 2, 3, 5, 9, 123]\n    \"\"\"\n    if not isinstance(l, list):\n        raise TypeError(\"Input must be a list\")\n    return sorted(set(l))", "problem_index": 76, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "d467f564", "prompt": "\n\ndef fib4(n: int):\n    \"\"\"The Fib4 number sequence is a sequence similar to the <PERSON><PERSON><PERSON>cci sequnece that's defined as follows:\n    fib4(0) -> 0\n    fib4(1) -> 0\n    fib4(2) -> 2\n    fib4(3) -> 0\n    fib4(n) -> fib4(n-1) + fib4(n-2) + fib4(n-3) + fib4(n-4).\n    Please write a function to efficiently compute the n-th element of the fib4 number sequence.  Do not use recursion.\n    >>> fib4(5)\n    4\n    >>> fib4(6)\n    8\n    >>> fib4(7)\n    14\n    \"\"\"\n", "solution": "def fib4(n: int) -> int:\n    if not isinstance(n, int):\n        raise TypeError(\"Input must be an integer\")\n    if n < 0:\n        raise ValueError(\"Input must be non-negative\")\n    \n    if n == 0:\n        return 0\n    if n == 1:\n        return 1\n    if n == 2:\n        return 1\n    if n == 3:\n        return 2\n    \n    dp = [0, 1, 1, 2]\n    \n    for _ in range(4, n + 1):\n        next_val = sum(dp) % 4\n        dp = dp[1:] + [next_val]\n    \n    return dp[-1]", "execution_time": 63.735090255737305, "code": "def fib4(n: int) -> int:\n    if not isinstance(n, int):\n        raise TypeError(\"Input must be an integer\")\n    if n < 0:\n        raise ValueError(\"Input must be non-negative\")\n    \n    if n == 0:\n        return 0\n    if n == 1:\n        return 1\n    if n == 2:\n        return 1\n    if n == 3:\n        return 2\n    \n    dp = [0, 1, 1, 2]\n    \n    for _ in range(4, n + 1):\n        next_val = sum(dp) % 4\n        dp = dp[1:] + [next_val]\n    \n    return dp[-1]", "problem_index": 77, "partial_success": true, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "821725b0", "prompt": "from typing import List\n\n\ndef filter_by_substring(strings: List[str], substring: str) -> List[str]:\n    \"\"\" Filter an input list of strings only for ones that contain given substring\n    >>> filter_by_substring([], 'a')\n    []\n    >>> filter_by_substring(['abc', 'bacd', 'cde', 'array'], 'a')\n    ['abc', 'bacd', 'array']\n    \"\"\"\n", "solution": "```python\nfrom typing import List\n\n\ndef filter_by_substring(strings: List[str], substring: str) -> List[str]:\n    \"\"\" Filter an input list of strings only for ones that contain given substring\n    >>> filter_by_substring([], 'a')\n    []\n    >>> filter_by_substring(['abc', 'bacd', 'cde', 'array'], 'a')\n    ['abc', 'bacd', 'array']\n    \"\"\"\n    return [string for string in strings if substring in string]\n```", "execution_time": 19.926753997802734, "code": "from typing import List\n\n\ndef filter_by_substring(strings: List[str], substring: str) -> List[str]:\n    \"\"\" Filter an input list of strings only for ones that contain given substring\n    >>> filter_by_substring([], 'a')\n    []\n    >>> filter_by_substring(['abc', 'bacd', 'cde', 'array'], 'a')\n    ['abc', 'bacd', 'array']\n    \"\"\"\n    return [string for string in strings if substring in string]", "problem_index": 78, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "dd94339f", "prompt": "\n\ndef pairs_sum_to_zero(l):\n    \"\"\"\n    pairs_sum_to_zero takes a list of integers as an input.\n    it returns True if there are two distinct elements in the list that\n    sum to zero, and False otherwise.\n    >>> pairs_sum_to_zero([1, 3, 5, 0])\n    False\n    >>> pairs_sum_to_zero([1, 3, -2, 1])\n    False\n    >>> pairs_sum_to_zero([1, 2, 3, 7])\n    False\n    >>> pairs_sum_to_zero([2, 4, -5, 3, 5, 7])\n    True\n    >>> pairs_sum_to_zero([1])\n    False\n    \"\"\"\n", "solution": "```python\ndef pairs_sum_to_zero(l):\n    \"\"\"\n    pairs_sum_to_zero takes a list of integers as an input.\n    it returns True if there are two distinct elements in the list that\n    sum to zero, and False otherwise.\n    >>> pairs_sum_to_zero([1, 3, 5, 0])\n    False\n    >>> pairs_sum_to_zero([1, 3, -2, 1])\n    False\n    >>> pairs_sum_to_zero([1, 2, 3, 7])\n    False\n    >>> pairs_sum_to_zero([2, 4, -5, 3, 5, 7])\n    True\n    >>> pairs_sum_to_zero([1])\n    False\n    \"\"\"\n    if not isinstance(l, list):\n        raise ValueError(\"Input must be a list.\")\n    if not l:\n        return False\n    seen = set()\n    for number in l:\n        if not isinstance(number, int):\n            raise ValueError(\"All elements in the list must be integers.\")\n        if -number in seen:\n            return True\n        seen.add(number)\n    return False\n```", "execution_time": 77.31505513191223, "code": "def pairs_sum_to_zero(l):\n    \"\"\"\n    pairs_sum_to_zero takes a list of integers as an input.\n    it returns True if there are two distinct elements in the list that\n    sum to zero, and False otherwise.\n    >>> pairs_sum_to_zero([1, 3, 5, 0])\n    False\n    >>> pairs_sum_to_zero([1, 3, -2, 1])\n    False\n    >>> pairs_sum_to_zero([1, 2, 3, 7])\n    False\n    >>> pairs_sum_to_zero([2, 4, -5, 3, 5, 7])\n    True\n    >>> pairs_sum_to_zero([1])\n    False\n    \"\"\"\n    if not isinstance(l, list):\n        raise ValueError(\"Input must be a list.\")\n    if not l:\n        return False\n    seen = set()\n    for number in l:\n        if not isinstance(number, int):\n            raise ValueError(\"All elements in the list must be integers.\")\n        if -number in seen:\n            return True\n        seen.add(number)\n    return False", "problem_index": 79, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "1b6b45fe", "prompt": "\ndef strange_sort_list(lst):\n    '''\n    Given list of integers, return list in strange order.\n    Strange sorting, is when you start with the minimum value,\n    then maximum of the remaining integers, then minimum and so on.\n\n    Examples:\n    strange_sort_list([1, 2, 3, 4]) == [1, 4, 2, 3]\n    strange_sort_list([5, 5, 5, 5]) == [5, 5, 5, 5]\n    strange_sort_list([]) == []\n    '''\n", "solution": "```python\ndef strange_sort_list(lst):\n    '''\n    Given list of integers, return list in strange order.\n    Strange sorting, is when you start with the minimum value,\n    then maximum of the remaining integers, then minimum and so on.\n\n    Examples:\n    strange_sort_list([1, 2, 3, 4]) == [1, 4, 2, 3]\n    strange_sort_list([5, 5, 5, 5]) == [5, 5, 5, 5]\n    strange_sort_list([]) == []\n    '''\n    if not lst:\n        return []\n    \n    sorted_lst = sorted(lst)\n    result = []\n    smallest_index, largest_index = 0, len(lst) - 1\n    \n    while smallest_index <= largest_index:\n        if len(result) % 2 == 0:\n            result.append(sorted_lst[smallest_index])\n            smallest_index += 1\n        else:\n            result.append(sorted_lst[largest_index])\n            largest_index -= 1\n    \n    return result\n```", "execution_time": 18.79396104812622, "code": "def strange_sort_list(lst):\n    '''\n    Given list of integers, return list in strange order.\n    Strange sorting, is when you start with the minimum value,\n    then maximum of the remaining integers, then minimum and so on.\n\n    Examples:\n    strange_sort_list([1, 2, 3, 4]) == [1, 4, 2, 3]\n    strange_sort_list([5, 5, 5, 5]) == [5, 5, 5, 5]\n    strange_sort_list([]) == []\n    '''\n    if not lst:\n        return []\n    \n    sorted_lst = sorted(lst)\n    result = []\n    smallest_index, largest_index = 0, len(lst) - 1\n    \n    while smallest_index <= largest_index:\n        if len(result) % 2 == 0:\n            result.append(sorted_lst[smallest_index])\n            smallest_index += 1\n        else:\n            result.append(sorted_lst[largest_index])\n            largest_index -= 1\n    \n    return result", "problem_index": 80, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "cae5d831", "prompt": "\ndef split_words(txt):\n    '''\n    Given a string of words, return a list of words split on whitespace, if no whitespaces exists in the text you\n    should split on commas ',' if no commas exists you should return the number of lower-case letters with odd order in the\n    alphabet, ord('a') = 0, ord('b') = 1, ... ord('z') = 25\n    Examples\n    split_words(\"Hello world!\") ➞ [\"Hello\", \"world!\"]\n    split_words(\"Hello,world!\") ➞ [\"Hello\", \"world!\"]\n    split_words(\"abcdef\") == 3 \n    '''\n", "solution": "def split_words(txt):\n    if not isinstance(txt, str):\n        raise TypeError(\"Input must be a string\")\n\n    if ' ' in txt:\n        return txt.split()\n\n    if ',' in txt:\n        return txt.split(',')\n\n    return [txt]", "execution_time": 59.96857309341431, "code": "def split_words(txt):\n    if not isinstance(txt, str):\n        raise TypeError(\"Input must be a string\")\n\n    if ' ' in txt:\n        return txt.split()\n\n    if ',' in txt:\n        return txt.split(',')\n\n    return [txt]", "problem_index": 81, "partial_success": true, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "03f66fdf", "prompt": "\ndef exchange(lst1, lst2):\n    \"\"\"In this problem, you will implement a function that takes two lists of numbers,\n    and determines whether it is possible to perform an exchange of elements\n    between them to make lst1 a list of only even numbers.\n    There is no limit on the number of exchanged elements between lst1 and lst2.\n    If it is possible to exchange elements between the lst1 and lst2 to make\n    all the elements of lst1 to be even, return \"YES\".\n    Otherwise, return \"NO\".\n    For example:\n    exchange([1, 2, 3, 4], [1, 2, 3, 4]) => \"YES\"\n    exchange([1, 2, 3, 4], [1, 5, 3, 4]) => \"NO\"\n    It is assumed that the input lists will be non-empty.\n    \"\"\"\n", "solution": "```python\ndef exchange(lst1, lst2):\n    \"\"\"In this problem, you will implement a function that takes two lists of numbers,\n    and determines whether it is possible to perform an exchange of elements\n    between them to make lst1 a list of only even numbers.\n    There is no limit on the number of exchanged elements between lst1 and lst2.\n    If it is possible to exchange elements between the lst1 and lst2 to make\n    all the elements of lst1 to be even, return \"YES\".\n    Otherwise, return \"NO\".\n    For example:\n    exchange([1, 2, 3, 4], [1, 2, 3, 4]) => \"YES\"\n    exchange([1, 2, 3, 4], [1, 5, 3, 4]) => \"NO\"\n    It is assumed that the input lists will be non-empty.\n    \"\"\"\n    if not isinstance(lst1, list) or not isinstance(lst2, list):\n        raise TypeError(\"Both inputs must be lists\")\n    if not lst1 or not lst2:\n        raise ValueError(\"Both lists must be non-empty\")\n\n    if not all(isinstance(x, int) for x in lst1) or not all(isinstance(x, int) for x in lst2):\n        raise TypeError(\"Both lists must contain only integers\")\n\n    odd_in_lst1 = [num for num in lst1 if num % 2 != 0]\n    even_in_lst2 = [num for num in lst2 if num % 2 == 0]\n\n    return \"YES\" if len(odd_in_lst1) <= len(even_in_lst2) else \"NO\"\n```", "execution_time": 82.47575092315674, "code": "def exchange(lst1, lst2):\n    \"\"\"In this problem, you will implement a function that takes two lists of numbers,\n    and determines whether it is possible to perform an exchange of elements\n    between them to make lst1 a list of only even numbers.\n    There is no limit on the number of exchanged elements between lst1 and lst2.\n    If it is possible to exchange elements between the lst1 and lst2 to make\n    all the elements of lst1 to be even, return \"YES\".\n    Otherwise, return \"NO\".\n    For example:\n    exchange([1, 2, 3, 4], [1, 2, 3, 4]) => \"YES\"\n    exchange([1, 2, 3, 4], [1, 5, 3, 4]) => \"NO\"\n    It is assumed that the input lists will be non-empty.\n    \"\"\"\n    if not isinstance(lst1, list) or not isinstance(lst2, list):\n        raise TypeError(\"Both inputs must be lists\")\n    if not lst1 or not lst2:\n        raise ValueError(\"Both lists must be non-empty\")\n\n    if not all(isinstance(x, int) for x in lst1) or not all(isinstance(x, int) for x in lst2):\n        raise TypeError(\"Both lists must contain only integers\")\n\n    odd_in_lst1 = [num for num in lst1 if num % 2 != 0]\n    even_in_lst2 = [num for num in lst2 if num % 2 == 0]\n\n    return \"YES\" if len(odd_in_lst1) <= len(even_in_lst2) else \"NO\"", "problem_index": 82, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "7fe91f95", "prompt": "\ndef is_bored(S):\n    \"\"\"\n    You'll be given a string of words, and your task is to count the number\n    of boredoms. A boredom is a sentence that starts with the word \"I\".\n    Sentences are delimited by '.', '?' or '!'.\n   \n    For example:\n    >>> is_bored(\"Hello world\")\n    0\n    >>> is_bored(\"The sky is blue. The sun is shining. I love this weather\")\n    1\n    \"\"\"\n", "solution": "```python\ndef is_bored(S):\n    \"\"\"\n    You'll be given a string of words, and your task is to count the number\n    of boredoms. A boredom is a sentence that starts with the word \"I\".\n    Sentences are delimited by '.', '?' or '!'.\n   \n    For example:\n    >>> is_bored(\"Hello world\")\n    0\n    >>> is_bored(\"The sky is blue. The sun is shining. I love this weather\")\n    1\n    \"\"\"\n    if not S.strip():\n        return 0\n    boredom_count = 0\n    sentences = S.replace('?', '.').replace('!', '.').split('.')\n    for sentence in sentences:\n        sentence = sentence.strip()\n        if sentence and sentence.split()[0].lower() == 'i':\n            boredom_count += 1\n    return boredom_count\n```", "execution_time": 21.04951000213623, "code": "def is_bored(S):\n    \"\"\"\n    You'll be given a string of words, and your task is to count the number\n    of boredoms. A boredom is a sentence that starts with the word \"I\".\n    Sentences are delimited by '.', '?' or '!'.\n   \n    For example:\n    >>> is_bored(\"Hello world\")\n    0\n    >>> is_bored(\"The sky is blue. The sun is shining. I love this weather\")\n    1\n    \"\"\"\n    if not S.strip():\n        return 0\n    boredom_count = 0\n    sentences = S.replace('?', '.').replace('!', '.').split('.')\n    for sentence in sentences:\n        sentence = sentence.strip()\n        if sentence and sentence.split()[0].lower() == 'i':\n            boredom_count += 1\n    return boredom_count", "problem_index": 83, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "ba761e63", "prompt": "\ndef starts_one_ends(n):\n    \"\"\"\n    Given a positive integer n, return the count of the numbers of n-digit\n    positive integers that start or end with 1.\n    \"\"\"\n", "solution": "```python\ndef starts_one_ends(n):\n    \"\"\"\n    Given a positive integer n, return the count of the numbers of n-digit\n    positive integers that start or end with 1.\n    For n = 1, the output is 1 (only the number 1).\n    \"\"\"\n    if n <= 0:\n        raise ValueError(\"Input must be a positive integer.\")\n    \n    if n == 1:\n        return 1  # The number 1\n    \n    start_count = 10**(n-1)  # n-digit numbers starting with 1\n    end_count = 10**(n-1)    # n-digit numbers ending with 1\n\n    # n-digit numbers that both start and end with 1\n    both_count = 10**(n-2) if n > 1 else 0\n\n    total_count = start_count + end_count - both_count\n    # Subtract both_count to avoid double-counting numbers that start and end with 1.\n    return total_count\n```", "execution_time": 23.13267993927002, "code": "def starts_one_ends(n):\n    \"\"\"\n    Given a positive integer n, return the count of the numbers of n-digit\n    positive integers that start or end with 1.\n    For n = 1, the output is 1 (only the number 1).\n    \"\"\"\n    if n <= 0:\n        raise ValueError(\"Input must be a positive integer.\")\n    \n    if n == 1:\n        return 1  # The number 1\n    \n    start_count = 10**(n-1)  # n-digit numbers starting with 1\n    end_count = 10**(n-1)    # n-digit numbers ending with 1\n\n    # n-digit numbers that both start and end with 1\n    both_count = 10**(n-2) if n > 1 else 0\n\n    total_count = start_count + end_count - both_count\n    # Subtract both_count to avoid double-counting numbers that start and end with 1.\n    return total_count", "problem_index": 84, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "5e3850cd", "prompt": "\ndef get_max_triples(n):\n    \"\"\"\n    You are given a positive integer n. You have to create an integer array a of length n.\n        For each i (1 ≤ i ≤ n), the value of a[i] = i * i - i + 1.\n        Return the number of triples (a[i], a[j], a[k]) of a where i < j < k, \n    and a[i] + a[j] + a[k] is a multiple of 3.\n\n    Example :\n        Input: n = 5\n        Output: 1\n        Explanation: \n        a = [1, 3, 7, 13, 21]\n        The only valid triple is (1, 7, 13).\n    \"\"\"\n", "solution": "```python\ndef get_max_triples(n):\n    \"\"\"\n    You are given a positive integer n. You have to create an integer array a of length n.\n        For each i (1 ≤ i ≤ n), the value of a[i] = i * i - i + 1.\n        Return the number of triples (a[i], a[j], a[k]) of a where i < j < k, \n    and a[i] + a[j] + a[k] is a multiple of 3.\n    \"\"\"\n    if not isinstance(n, int) or n <= 0:\n        raise ValueError(\"Input must be a positive integer\")\n\n    a = [i * i - i + 1 for i in range(1, n + 1)]\n    count = 0\n\n    for i in range(len(a)):\n        for j in range(i + 1, len(a)):\n            for k in range(j + 1, len(a)):\n                if (a[i] + a[j] + a[k]) % 3 == 0:\n                    count += 1\n\n    return count\n```", "execution_time": 68.98456597328186, "code": "def get_max_triples(n):\n    \"\"\"\n    You are given a positive integer n. You have to create an integer array a of length n.\n        For each i (1 ≤ i ≤ n), the value of a[i] = i * i - i + 1.\n        Return the number of triples (a[i], a[j], a[k]) of a where i < j < k, \n    and a[i] + a[j] + a[k] is a multiple of 3.\n    \"\"\"\n    if not isinstance(n, int) or n <= 0:\n        raise ValueError(\"Input must be a positive integer\")\n\n    a = [i * i - i + 1 for i in range(1, n + 1)]\n    count = 0\n\n    for i in range(len(a)):\n        for j in range(i + 1, len(a)):\n            for k in range(j + 1, len(a)):\n                if (a[i] + a[j] + a[k]) % 3 == 0:\n                    count += 1\n\n    return count", "problem_index": 85, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "3ca53f50", "prompt": "\ndef bf(planet1, planet2):\n    '''\n    There are eight planets in our solar system: the closerst to the Sun \n    is Mercury, the next one is Venus, then Earth, Mars, Jupiter, Saturn, \n    Uranus, Neptune.\n    Write a function that takes two planet names as strings planet1 and planet2. \n    The function should return a tuple containing all planets whose orbits are \n    located between the orbit of planet1 and the orbit of planet2, sorted by \n    the proximity to the sun. \n    The function should return an empty tuple if planet1 or planet2\n    are not correct planet names. \n    Examples\n    bf(\"Jupiter\", \"Neptune\") ==> (\"Saturn\", \"Uranus\")\n    bf(\"Earth\", \"Mercury\") ==> (\"Venus\")\n    bf(\"Mercury\", \"Uranus\") ==> (\"Venus\", \"Earth\", \"Mars\", \"Jupiter\", \"Saturn\")\n    '''\n", "solution": "```python\ndef bf(planet1, planet2):\n    '''\n    Returns a tuple containing all planets whose orbits are located between the orbit of planet1 and the orbit of planet2, \n    sorted by the proximity to the sun.\n    \n    Args:\n        planet1 (str): The name of the first planet.\n        planet2 (str): The name of the second planet.\n    \n    Returns:\n        tuple: A tuple containing all planets whose orbits are located between the orbit of planet1 and the orbit of planet2.\n        \n    Example:\n        bf(\"Earth\", \"Jupiter\") == (\"Mars\",)\n    '''\n    planets = [\"Mercury\", \"Venus\", \"Earth\", \"Mars\", \"Jupiter\", \"Saturn\", \"Uranus\", \"Neptune\"]\n    \n    planet1 = planet1.title()\n    planet2 = planet2.title()\n\n    # Check if planet1 and planet2 are valid planet names\n    if planet1 not in planets or planet2 not in planets:\n        return ()\n    \n    # Check if planet1 and planet2 are the same\n    if planet1 == planet2:\n        return ()\n    \n    # Find the indices of planet1 and planet2 in the planets list\n    index1 = planets.index(planet1)\n    index2 = planets.index(planet2)\n    \n    # Swap index1 and index2 if index1 is greater than index2\n    if index1 > index2:\n        index1, index2 = index2, index1\n    \n    # Return a tuple containing all planets between planet1 and planet2\n    return tuple(planets[index1 + 1:index2])\n```", "execution_time": 87.27168488502502, "code": "def bf(planet1, planet2):\n    '''\n    Returns a tuple containing all planets whose orbits are located between the orbit of planet1 and the orbit of planet2, \n    sorted by the proximity to the sun.\n    \n    Args:\n        planet1 (str): The name of the first planet.\n        planet2 (str): The name of the second planet.\n    \n    Returns:\n        tuple: A tuple containing all planets whose orbits are located between the orbit of planet1 and the orbit of planet2.\n        \n    Example:\n        bf(\"Earth\", \"Jupiter\") == (\"Mars\",)\n    '''\n    planets = [\"Mercury\", \"Venus\", \"Earth\", \"Mars\", \"Jupiter\", \"Saturn\", \"Uranus\", \"Neptune\"]\n    \n    planet1 = planet1.title()\n    planet2 = planet2.title()\n\n    # Check if planet1 and planet2 are valid planet names\n    if planet1 not in planets or planet2 not in planets:\n        return ()\n    \n    # Check if planet1 and planet2 are the same\n    if planet1 == planet2:\n        return ()\n    \n    # Find the indices of planet1 and planet2 in the planets list\n    index1 = planets.index(planet1)\n    index2 = planets.index(planet2)\n    \n    # Swap index1 and index2 if index1 is greater than index2\n    if index1 > index2:\n        index1, index2 = index2, index1\n    \n    # Return a tuple containing all planets between planet1 and planet2\n    return tuple(planets[index1 + 1:index2])", "problem_index": 86, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "4fc148aa", "prompt": "\ndef encrypt(s):\n    \"\"\"Create a function encrypt that takes a string as an argument and\n    returns a string encrypted with the alphabet being rotated. \n    The alphabet should be rotated in a manner such that the letters \n    shift down by two multiplied to two places.\n    For example:\n    encrypt('hi') returns 'lm'\n    encrypt('asdfghjkl') returns 'ewhjklnop'\n    encrypt('gf') returns 'kj'\n    encrypt('et') returns 'ix'\n    \"\"\"\n", "solution": "def encrypt(s: str) -> str:\n    \"\"\"\n    Encrypts a given string by shifting each letter down by 4 places.\n\n    Args:\n        s (str): The input string to be encrypted.\n\n    Returns:\n        str: The encrypted string.\n\n    Raises:\n        ValueError: If the input is not a string.\n    \"\"\"\n    if not isinstance(s, str):\n        raise ValueError(\"Input must be a string\")\n\n    SHIFT = 4\n    encrypted = []\n    for char in s:\n        if char.isalpha():\n            base = ord('a') if char.islower() else ord('A')\n            new_char = chr((ord(char) - base + SHIFT) % 26 + base)\n            encrypted.append(new_char)\n        else:\n            encrypted.append(char)  # Non-alphabetic characters remain unchanged\n\n    return ''.join(encrypted)", "execution_time": 82.78755378723145, "code": "def encrypt(s: str) -> str:\n    \"\"\"\n    Encrypts a given string by shifting each letter down by 4 places.\n\n    Args:\n        s (str): The input string to be encrypted.\n\n    Returns:\n        str: The encrypted string.\n\n    Raises:\n        ValueError: If the input is not a string.\n    \"\"\"\n    if not isinstance(s, str):\n        raise ValueError(\"Input must be a string\")\n\n    SHIFT = 4\n    encrypted = []\n    for char in s:\n        if char.isalpha():\n            base = ord('a') if char.islower() else ord('A')\n            new_char = chr((ord(char) - base + SHIFT) % 26 + base)\n            encrypted.append(new_char)\n        else:\n            encrypted.append(char)  # Non-alphabetic characters remain unchanged\n\n    return ''.join(encrypted)", "problem_index": 87, "partial_success": true, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "bf6194f5", "prompt": "from typing import List, <PERSON><PERSON>\n\n\ndef sum_product(numbers: List[int]) -> Tuple[int, int]:\n    \"\"\" For a given list of integers, return a tuple consisting of a sum and a product of all the integers in a list.\n    Empty sum should be equal to 0 and empty product should be equal to 1.\n    >>> sum_product([])\n    (0, 1)\n    >>> sum_product([1, 2, 3, 4])\n    (10, 24)\n    \"\"\"\n", "solution": "```python\nfrom typing import List, <PERSON><PERSON>\nfrom math import prod\n\ndef sum_product(numbers: List[int]) -> Tuple[int, int]:\n    if not numbers:\n        return (0, 1)\n    \n    total_sum = sum(numbers)\n    total_product = prod(numbers)\n    \n    return (total_sum, total_product)\n```", "execution_time": 17.90223526954651, "code": "from typing import List, <PERSON><PERSON>\nfrom math import prod\n\ndef sum_product(numbers: List[int]) -> Tuple[int, int]:\n    if not numbers:\n        return (0, 1)\n    \n    total_sum = sum(numbers)\n    total_product = prod(numbers)\n    \n    return (total_sum, total_product)", "problem_index": 88, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "27c8e447", "prompt": "\ndef int_to_mini_roman(number):\n    \"\"\"\n    Given a positive integer, obtain its roman numeral equivalent as a string,\n    and return it in lowercase.\n    Restrictions: 1 <= num <= 1000\n\n    Examples:\n    >>> int_to_mini_roman(19) == 'xix'\n    >>> int_to_mini_roman(152) == 'clii'\n    >>> int_to_mini_roman(426) == 'cdxxvi'\n    \"\"\"\n", "solution": "```python\ndef int_to_mini_roman(number):\n    \"\"\"\n    Given a positive integer, obtain its roman numeral equivalent as a string,\n    and return it in lowercase.\n    Restrictions: 1 <= num <= 1000\n\n    Examples:\n    >>> int_to_mini_roman(19) == 'xix'\n    >>> int_to_mini_roman(152) == 'clii'\n    >>> int_to_mini_roman(426) == 'cdxxvi'\n    \"\"\"\n    if not isinstance(number, int) or number < 1 or number > 1000:\n        raise ValueError(\"Input must be a positive integer between 1 and 1000.\")\n    \n    roman_numerals = [\n        (1000, 'm'), (900, 'cm'), (500, 'd'), (400, 'cd'),\n        (100, 'c'), (90, 'xc'), (50, 'l'), (40, 'xl'),\n        (10, 'x'), (9, 'ix'), (5, 'v'), (4, 'iv'),\n        (1, 'i')\n    ]\n    \n    result = []\n    for value, numeral in roman_numerals:\n        while number >= value:\n            result.append(numeral)\n            number -= value\n            \n    return ''.join(result)\n```", "execution_time": 19.954867839813232, "code": "def int_to_mini_roman(number):\n    \"\"\"\n    Given a positive integer, obtain its roman numeral equivalent as a string,\n    and return it in lowercase.\n    Restrictions: 1 <= num <= 1000\n\n    Examples:\n    >>> int_to_mini_roman(19) == 'xix'\n    >>> int_to_mini_roman(152) == 'clii'\n    >>> int_to_mini_roman(426) == 'cdxxvi'\n    \"\"\"\n    if not isinstance(number, int) or number < 1 or number > 1000:\n        raise ValueError(\"Input must be a positive integer between 1 and 1000.\")\n    \n    roman_numerals = [\n        (1000, 'm'), (900, 'cm'), (500, 'd'), (400, 'cd'),\n        (100, 'c'), (90, 'xc'), (50, 'l'), (40, 'xl'),\n        (10, 'x'), (9, 'ix'), (5, 'v'), (4, 'iv'),\n        (1, 'i')\n    ]\n    \n    result = []\n    for value, numeral in roman_numerals:\n        while number >= value:\n            result.append(numeral)\n            number -= value\n            \n    return ''.join(result)", "problem_index": 89, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "1f848401", "prompt": "\ndef odd_count(lst):\n    \"\"\"Given a list of strings, where each string consists of only digits, return a list.\n    Each element i of the output should be \"the number of odd elements in the\n    string i of the input.\" where all the i's should be replaced by the number\n    of odd digits in the i'th string of the input.\n\n    >>> odd_count(['1234567'])\n    [\"the number of odd elements 4n the str4ng 4 of the 4nput.\"]\n    >>> odd_count(['3',\"11111111\"])\n    [\"the number of odd elements 1n the str1ng 1 of the 1nput.\",\n     \"the number of odd elements 8n the str8ng 8 of the 8nput.\"]\n    \"\"\"\n", "solution": "def odd_count(lst):\n    \"\"\"Given a list of strings, where each string consists of only digits, return a list.\n    Each element of the output should be \"the number of odd elements X in the string Y of the input.\",\n    where X is the number of odd digits in the corresponding input string and Y is the index of the string in the input list (starting from 1).\n\n    >>> odd_count(['1234567'])\n    ['the number of odd elements 4 in the string 1 of the input.']\n    >>> odd_count(['3',\"11111111\"])\n    ['the number of odd elements 1 in the string 1 of the input.', 'the number of odd elements 8 in the string 2 of the input.']\n    \"\"\"\n    if not isinstance(lst, list):\n        raise ValueError(\"Input must be a list.\")\n\n    if not lst:\n        raise ValueError(\"Input list cannot be empty.\")\n    \n    result = []\n    for index, string in enumerate(lst):\n        if not isinstance(string, str) or not string.isdigit():\n            raise ValueError(f\"Element at index {index} must be a string of digits.\")\n        \n        odd_count = sum(1 for char in string if int(char) % 2 != 0)\n        result.append(f\"the number of odd elements {odd_count} in the string {index + 1} of the input.\")\n    \n    return result", "execution_time": 69.92181015014648, "code": "def odd_count(lst):\n    \"\"\"Given a list of strings, where each string consists of only digits, return a list.\n    Each element of the output should be \"the number of odd elements X in the string Y of the input.\",\n    where X is the number of odd digits in the corresponding input string and Y is the index of the string in the input list (starting from 1).\n\n    >>> odd_count(['1234567'])\n    ['the number of odd elements 4 in the string 1 of the input.']\n    >>> odd_count(['3',\"11111111\"])\n    ['the number of odd elements 1 in the string 1 of the input.', 'the number of odd elements 8 in the string 2 of the input.']\n    \"\"\"\n    if not isinstance(lst, list):\n        raise ValueError(\"Input must be a list.\")\n\n    if not lst:\n        raise ValueError(\"Input list cannot be empty.\")\n    \n    result = []\n    for index, string in enumerate(lst):\n        if not isinstance(string, str) or not string.isdigit():\n            raise ValueError(f\"Element at index {index} must be a string of digits.\")\n        \n        odd_count = sum(1 for char in string if int(char) % 2 != 0)\n        result.append(f\"the number of odd elements {odd_count} in the string {index + 1} of the input.\")\n    \n    return result", "problem_index": 90, "partial_success": true, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "46451d33", "prompt": "\n\ndef greatest_common_divisor(a: int, b: int) -> int:\n    \"\"\" Return a greatest common divisor of two integers a and b\n    >>> greatest_common_divisor(3, 5)\n    1\n    >>> greatest_common_divisor(25, 15)\n    5\n    \"\"\"\n", "solution": "```python\ndef greatest_common_divisor(a: int, b: int) -> int:\n    \"\"\" Return the greatest common divisor of two integers a and b\n    >>> greatest_common_divisor(3, 5)\n    1\n    >>> greatest_common_divisor(25, 15)\n    5\n    >>> greatest_common_divisor(0, 5)\n    5\n    >>> greatest_common_divisor(5, 0)\n    5\n    >>> greatest_common_divisor(0, 0)\n    Traceback (most recent call last):\n        ...\n    ValueError: GCD is not defined for both inputs being zero\n    >>> greatest_common_divisor(-25, 15)\n    5\n    >>> greatest_common_divisor(25, -15)\n    5\n    >>> greatest_common_divisor(-25, -15)\n    5\n    \"\"\"\n    if a == 0 and b == 0:\n        raise ValueError(\"GCD is not defined for both inputs being zero\")\n    a, b = abs(a), abs(b)\n    while b:\n        a, b = b, a % b\n    return a\n```", "execution_time": 24.293391942977905, "code": "def greatest_common_divisor(a: int, b: int) -> int:\n    \"\"\" Return the greatest common divisor of two integers a and b\n    >>> greatest_common_divisor(3, 5)\n    1\n    >>> greatest_common_divisor(25, 15)\n    5\n    >>> greatest_common_divisor(0, 5)\n    5\n    >>> greatest_common_divisor(5, 0)\n    5\n    >>> greatest_common_divisor(0, 0)\n    Traceback (most recent call last):\n        ...\n    ValueError: GCD is not defined for both inputs being zero\n    >>> greatest_common_divisor(-25, 15)\n    5\n    >>> greatest_common_divisor(25, -15)\n    5\n    >>> greatest_common_divisor(-25, -15)\n    5\n    \"\"\"\n    if a == 0 and b == 0:\n        raise ValueError(\"GCD is not defined for both inputs being zero\")\n    a, b = abs(a), abs(b)\n    while b:\n        a, b = b, a % b\n    return a", "problem_index": 91, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "121bb70b", "prompt": "\n\ndef largest_prime_factor(n: int):\n    \"\"\"Return the largest prime factor of n. Assume n > 1 and is not a prime.\n    >>> largest_prime_factor(13195)\n    29\n    >>> largest_prime_factor(2048)\n    2\n    \"\"\"\n", "solution": "```python\ndef largest_prime_factor(n: int) -> int:\n    if n <= 1:\n        raise ValueError(\"Input must be greater than 1.\")\n    \n    largest_factor = None\n    \n    while n % 2 == 0:\n        largest_factor = 2\n        n //= 2\n    \n    for i in range(3, int(n**0.5) + 1, 2):\n        while n % i == 0:\n            largest_factor = i\n            n //= i\n    \n    if n > 2:\n        largest_factor = n\n    \n    return largest_factor\n```", "execution_time": 23.774853706359863, "code": "def largest_prime_factor(n: int) -> int:\n    if n <= 1:\n        raise ValueError(\"Input must be greater than 1.\")\n    \n    largest_factor = None\n    \n    while n % 2 == 0:\n        largest_factor = 2\n        n //= 2\n    \n    for i in range(3, int(n**0.5) + 1, 2):\n        while n % i == 0:\n            largest_factor = i\n            n //= i\n    \n    if n > 2:\n        largest_factor = n\n    \n    return largest_factor", "problem_index": 92, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "2d9cf020", "prompt": "\ndef fix_spaces(text):\n    \"\"\"\n    Given a string text, replace all spaces in it with underscores, \n    and if a string has more than 2 consecutive spaces, \n    then replace all consecutive spaces with - \n    \n    fix_spaces(\"Example\") == \"Example\"\n    fix_spaces(\"Example 1\") == \"Example_1\"\n    fix_spaces(\" Example 2\") == \"_Example_2\"\n    fix_spaces(\" Example   3\") == \"_Example-3\"\n    \"\"\"\n", "solution": "def fix_spaces(text):\n    \"\"\"\n    Given a string text, replace all spaces in it with underscores, \n    and if a string has more than 2 consecutive spaces, \n    then replace all consecutive spaces with -\n    \n    fix_spaces(\"Example\") == \"Example\"\n    fix_spaces(\"Example 1\") == \"Example_1\"\n    fix_spaces(\" Example 2\") == \"_Example_2\"\n    fix_spaces(\" Example   3\") == \"_Example-3\"\n    \"\"\"\n    result = \"\"\n    space_count = 0\n    for char in text:\n        if char == \" \":\n            space_count += 1\n        else:\n            if space_count > 2:\n                result += \"-\" + char\n            elif space_count > 0:\n                result += \"_\" + char\n            else:\n                result += char\n            space_count = 0\n    if space_count > 2:\n        result += \"-\"\n    elif space_count > 0:\n        result += \"_\"\n    return result", "execution_time": 21.777252912521362, "code": "def fix_spaces(text):\n    \"\"\"\n    Given a string text, replace all spaces in it with underscores, \n    and if a string has more than 2 consecutive spaces, \n    then replace all consecutive spaces with -\n    \n    fix_spaces(\"Example\") == \"Example\"\n    fix_spaces(\"Example 1\") == \"Example_1\"\n    fix_spaces(\" Example 2\") == \"_Example_2\"\n    fix_spaces(\" Example   3\") == \"_Example-3\"\n    \"\"\"\n    result = \"\"\n    space_count = 0\n    for char in text:\n        if char == \" \":\n            space_count += 1\n        else:\n            if space_count > 2:\n                result += \"-\" + char\n            elif space_count > 0:\n                result += \"_\" + char\n            else:\n                result += char\n            space_count = 0\n    if space_count > 2:\n        result += \"-\"\n    elif space_count > 0:\n        result += \"_\"\n    return result", "problem_index": 93, "partial_success": true, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "37c5aad9", "prompt": "from typing import List\n\n\ndef below_zero(operations: List[int]) -> bool:\n    \"\"\" You're given a list of deposit and withdrawal operations on a bank account that starts with\n    zero balance. Your task is to detect if at any point the balance of account fallls below zero, and\n    at that point function should return True. Otherwise it should return False.\n    >>> below_zero([1, 2, 3])\n    False\n    >>> below_zero([1, 2, -4, 5])\n    True\n    \"\"\"\n", "solution": "```python\ndef below_zero(operations: List[int]) -> bool:\n    \"\"\" You're given a list of deposit and withdrawal operations on a bank account that starts with\n    zero balance. Your task is to detect if at any point the balance of account fallls below zero, and\n    at that point function should return True. Otherwise it should return False.\n    >>> below_zero([1, 2, 3])\n    False\n    >>> below_zero([1, 2, -4, 5])\n    True\n    \"\"\"\n    if not isinstance(operations, list) or not all(isinstance(op, int) for op in operations):\n        raise ValueError(\"Invalid input: operations must be a list of integers\")\n    \n    balance = 0\n    for operation in operations:\n        balance += operation\n        if balance < 0:\n            return True\n    return False\n```", "execution_time": 54.**************, "code": "def below_zero(operations: List[int]) -> bool:\n    \"\"\" You're given a list of deposit and withdrawal operations on a bank account that starts with\n    zero balance. Your task is to detect if at any point the balance of account fallls below zero, and\n    at that point function should return True. Otherwise it should return False.\n    >>> below_zero([1, 2, 3])\n    False\n    >>> below_zero([1, 2, -4, 5])\n    True\n    \"\"\"\n    if not isinstance(operations, list) or not all(isinstance(op, int) for op in operations):\n        raise ValueError(\"Invalid input: operations must be a list of integers\")\n    \n    balance = 0\n    for operation in operations:\n        balance += operation\n        if balance < 0:\n            return True\n    return False", "problem_index": 94, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "b86bed10", "prompt": "from typing import List\n\n\ndef parse_music(music_string: str) -> List[int]:\n    \"\"\" Input to this function is a string representing musical notes in a special ASCII format.\n    Your task is to parse this string and return list of integers corresponding to how many beats does each\n    not last.\n\n    Here is a legend:\n    'o' - whole note, lasts four beats\n    'o|' - half note, lasts two beats\n    '.|' - quater note, lasts one beat\n\n    >>> parse_music('o o| .| o| o| .| .| .| .| o o')\n    [4, 2, 1, 2, 2, 1, 1, 1, 1, 4, 4]\n    \"\"\"\n", "solution": "```python\nfrom typing import List\n\ndef parse_music(music_string: str) -> List[int]:\n    \"\"\" Input to this function is a string representing musical notes in a special ASCII format.\n    Your task is to parse this string and return list of integers corresponding to how many beats does each\n    not last.\n\n    Here is a legend:\n    'o' - whole note, lasts four beats\n    'o|' - half note, lasts two beats\n    '.|' - quater note, lasts one beat\n\n    >>> parse_music('o o| .| o| o| .| .| .| .| o o')\n    [4, 2, 1, 2, 2, 1, 1, 1, 1, 4, 4]\n    \"\"\"\n    note_durations = {\n        'o': 4,\n        'o|': 2,\n        '.|': 1\n    }\n    \n    if not isinstance(music_string, str):\n        raise ValueError(\"Input must be a string.\")\n    \n    music_string = music_string.strip()\n    notes = music_string.split()\n    durations = []\n    \n    for note in notes:\n        if note in note_durations:\n            durations.append(note_durations[note])\n        elif note == '':  # handle empty strings\n            continue\n        else:\n            raise ValueError(f\"Invalid note '{note}' found in input.\")\n    \n    return durations\n```", "execution_time": 72.2918210029602, "code": "from typing import List\n\ndef parse_music(music_string: str) -> List[int]:\n    \"\"\" Input to this function is a string representing musical notes in a special ASCII format.\n    Your task is to parse this string and return list of integers corresponding to how many beats does each\n    not last.\n\n    Here is a legend:\n    'o' - whole note, lasts four beats\n    'o|' - half note, lasts two beats\n    '.|' - quater note, lasts one beat\n\n    >>> parse_music('o o| .| o| o| .| .| .| .| o o')\n    [4, 2, 1, 2, 2, 1, 1, 1, 1, 4, 4]\n    \"\"\"\n    note_durations = {\n        'o': 4,\n        'o|': 2,\n        '.|': 1\n    }\n    \n    if not isinstance(music_string, str):\n        raise ValueError(\"Input must be a string.\")\n    \n    music_string = music_string.strip()\n    notes = music_string.split()\n    durations = []\n    \n    for note in notes:\n        if note in note_durations:\n            durations.append(note_durations[note])\n        elif note == '':  # handle empty strings\n            continue\n        else:\n            raise ValueError(f\"Invalid note '{note}' found in input.\")\n    \n    return durations", "problem_index": 95, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "cf05c8f3", "prompt": "\ndef will_it_fly(q,w):\n    '''\n    Write a function that returns True if the object q will fly, and False otherwise.\n    The object q will fly if it's balanced (it is a palindromic list) and the sum of its elements is less than or equal the maximum possible weight w.\n\n    Example:\n    will_it_fly([1, 2], 5) ➞ False \n    # 1+2 is less than the maximum possible weight, but it's unbalanced.\n\n    will_it_fly([3, 2, 3], 1) ➞ False\n    # it's balanced, but 3+2+3 is more than the maximum possible weight.\n\n    will_it_fly([3, 2, 3], 9) ➞ True\n    # 3+2+3 is less than the maximum possible weight, and it's balanced.\n\n    will_it_fly([3], 5) ➞ True\n    # 3 is less than the maximum possible weight, and it's balanced.\n    '''\n", "solution": "```python\ndef will_it_fly(q,w):\n    '''\n    Write a function that returns True if the object q will fly, and False otherwise.\n    The object q will fly if it's balanced (it is a palindromic list) and the sum of its elements is less than or equal the maximum possible weight w.\n\n    Example:\n    will_it_fly([1, 2], 5) ➞ False \n    # 1+2 is less than the maximum possible weight, but it's unbalanced.\n\n    will_it_fly([3, 2, 3], 1) ➞ False\n    # it's balanced, but 3+2+3 is more than the maximum possible weight.\n\n    will_it_fly([3, 2, 3], 9) ➞ True\n    # 3+2+3 is less than the maximum possible weight, and it's balanced.\n\n    will_it_fly([3], 5) ➞ True\n    # 3 is less than the maximum possible weight, and it's balanced.\n    '''\n    return q == q[::-1] and sum(q) <= w\n```", "execution_time": 69.46730017662048, "code": "def will_it_fly(q,w):\n    '''\n    Write a function that returns True if the object q will fly, and False otherwise.\n    The object q will fly if it's balanced (it is a palindromic list) and the sum of its elements is less than or equal the maximum possible weight w.\n\n    Example:\n    will_it_fly([1, 2], 5) ➞ False \n    # 1+2 is less than the maximum possible weight, but it's unbalanced.\n\n    will_it_fly([3, 2, 3], 1) ➞ False\n    # it's balanced, but 3+2+3 is more than the maximum possible weight.\n\n    will_it_fly([3, 2, 3], 9) ➞ True\n    # 3+2+3 is less than the maximum possible weight, and it's balanced.\n\n    will_it_fly([3], 5) ➞ True\n    # 3 is less than the maximum possible weight, and it's balanced.\n    '''\n    return q == q[::-1] and sum(q) <= w", "problem_index": 96, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "e002b069", "prompt": "\ndef words_in_sentence(sentence):\n    \"\"\"\n    You are given a string representing a sentence,\n    the sentence contains some words separated by a space,\n    and you have to return a string that contains the words from the original sentence,\n    whose lengths are prime numbers,\n    the order of the words in the new string should be the same as the original one.\n\n    Example 1:\n        Input: sentence = \"This is a test\"\n        Output: \"is\"\n\n    Example 2:\n        Input: sentence = \"lets go for swimming\"\n        Output: \"go for\"\n\n    Constraints:\n        * 1 <= len(sentence) <= 100\n        * sentence contains only letters\n    \"\"\"\n", "solution": "```python\ndef is_prime(n: int) -> bool:\n    \"\"\"\n    Checks if a number is prime.\n\n    Args:\n        n (int): The number to check.\n\n    Returns:\n        bool: True if the number is prime, False otherwise.\n    \"\"\"\n    if n <= 1:\n        return False\n    if n <= 3:\n        return True\n    if n % 2 == 0 or n % 3 == 0:\n        return False\n    i = 5\n    while i * i <= n:\n        if n % i == 0 or n % (i + 2) == 0:\n            return False\n        i += 6\n    return True\n\ndef words_in_sentence(sentence: str) -> str:\n    \"\"\"\n    You are given a string representing a sentence,\n    the sentence contains some words separated by a space,\n    and you have to return a string that contains the words from the original sentence,\n    whose lengths are prime numbers,\n    the order of the words in the new string should be the same as the original one.\n\n    Example 1:\n        Input: sentence = \"This is a test\"\n        Output: \"is\"\n\n    Example 2:\n        Input: sentence = \"lets go for swimming\"\n        Output: \"go for\"\n\n    Constraints:\n        * 1 <= len(sentence) <= 100\n        * sentence contains only letters\n    \"\"\"\n    words = sentence.split()\n    result = []\n    for word in words:\n        if is_prime(len(word)):\n            result.append(word)\n    return \" \".join(result)\n```", "execution_time": 79.77887392044067, "code": "def is_prime(n: int) -> bool:\n    \"\"\"\n    Checks if a number is prime.\n\n    Args:\n        n (int): The number to check.\n\n    Returns:\n        bool: True if the number is prime, False otherwise.\n    \"\"\"\n    if n <= 1:\n        return False\n    if n <= 3:\n        return True\n    if n % 2 == 0 or n % 3 == 0:\n        return False\n    i = 5\n    while i * i <= n:\n        if n % i == 0 or n % (i + 2) == 0:\n            return False\n        i += 6\n    return True\n\ndef words_in_sentence(sentence: str) -> str:\n    \"\"\"\n    You are given a string representing a sentence,\n    the sentence contains some words separated by a space,\n    and you have to return a string that contains the words from the original sentence,\n    whose lengths are prime numbers,\n    the order of the words in the new string should be the same as the original one.\n\n    Example 1:\n        Input: sentence = \"This is a test\"\n        Output: \"is\"\n\n    Example 2:\n        Input: sentence = \"lets go for swimming\"\n        Output: \"go for\"\n\n    Constraints:\n        * 1 <= len(sentence) <= 100\n        * sentence contains only letters\n    \"\"\"\n    words = sentence.split()\n    result = []\n    for word in words:\n        if is_prime(len(word)):\n            result.append(word)\n    return \" \".join(result)", "problem_index": 97, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "a4d0a642", "prompt": "\ndef largest_smallest_integers(lst):\n    '''\n    Create a function that returns a tuple (a, b), where 'a' is\n    the largest of negative integers, and 'b' is the smallest\n    of positive integers in a list.\n    If there is no negative or positive integers, return them as None.\n\n    Examples:\n    largest_smallest_integers([2, 4, 1, 3, 5, 7]) == (None, 1)\n    largest_smallest_integers([]) == (None, None)\n    largest_smallest_integers([0]) == (None, None)\n    '''\n", "solution": "```python\ndef largest_smallest_integers(lst):\n    \"\"\"\n    Returns a tuple (a, b) where 'a' is the largest of negative integers and 'b' is the smallest of positive integers in a list.\n    If there are no negative or positive integers, returns them as None.\n\n    Args:\n        lst (list): A list of integers.\n\n    Returns:\n        tuple: A tuple containing the largest negative integer and the smallest positive integer.\n    \"\"\"\n    largest_negative = None\n    smallest_positive = None\n\n    for num in lst:\n        if num < 0:\n            if largest_negative is None or num > largest_negative:\n                largest_negative = num\n        elif num > 0:\n            if smallest_positive is None or num < smallest_positive:\n                smallest_positive = num\n\n    return largest_negative, smallest_positive\n```", "execution_time": 61.65777087211609, "code": "def largest_smallest_integers(lst):\n    \"\"\"\n    Returns a tuple (a, b) where 'a' is the largest of negative integers and 'b' is the smallest of positive integers in a list.\n    If there are no negative or positive integers, returns them as None.\n\n    Args:\n        lst (list): A list of integers.\n\n    Returns:\n        tuple: A tuple containing the largest negative integer and the smallest positive integer.\n    \"\"\"\n    largest_negative = None\n    smallest_positive = None\n\n    for num in lst:\n        if num < 0:\n            if largest_negative is None or num > largest_negative:\n                largest_negative = num\n        elif num > 0:\n            if smallest_positive is None or num < smallest_positive:\n                smallest_positive = num\n\n    return largest_negative, smallest_positive", "problem_index": 98, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "a8e5db67", "prompt": "\ndef x_or_y(n, x, y):\n    \"\"\"A simple program which should return the value of x if n is \n    a prime number and should return the value of y otherwise.\n\n    Examples:\n    for x_or_y(7, 34, 12) == 34\n    for x_or_y(15, 8, 5) == 5\n    \n    \"\"\"\n", "solution": "```python\ndef x_or_y(n, x, y):\n    \"\"\"\n    Returns x if n is prime, otherwise returns y.\n    \n    Args:\n        n (int): Number to check for primality\n        x (any): Value to return if n is prime\n        y (any): Value to return if n is not prime\n    \n    Returns:\n        x or y based on primality of n\n    \"\"\"\n    if not isinstance(n, int):\n        raise TypeError(\"n must be an integer\")\n    # Handle edge cases\n    if n <= 1:\n        return y\n    \n    # Check for primality\n    for i in range(2, int(n**0.5) + 1):\n        if n % i == 0:\n            return y\n    \n    return x\n```", "execution_time": 38.40384006500244, "code": "def x_or_y(n, x, y):\n    \"\"\"\n    Returns x if n is prime, otherwise returns y.\n    \n    Args:\n        n (int): Number to check for primality\n        x (any): Value to return if n is prime\n        y (any): Value to return if n is not prime\n    \n    Returns:\n        x or y based on primality of n\n    \"\"\"\n    if not isinstance(n, int):\n        raise TypeError(\"n must be an integer\")\n    # Handle edge cases\n    if n <= 1:\n        return y\n    \n    # Check for primality\n    for i in range(2, int(n**0.5) + 1):\n        if n % i == 0:\n            return y\n    \n    return x", "problem_index": 99, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}]}