{"metadata": {"timestamp": "2025-07-23T16:31:01.253258", "models_used": ["openai", "anthropic", "llama"], "task_type": "mbpp"}, "results": [{"id": "db7d412d", "prompt": "Write a function to convert a given tuple of positive integers into a single integer.\n\ndef tuple_to_int(nums):", "solution": "def tuple_to_int(nums):\n    for num in nums:\n        if not isinstance(num, int) or num < 0:\n            raise ValueError(\"Tuple must contain only positive integers.\")\n    if not nums:\n        return 0\n    return int(''.join(map(str, nums)))", "execution_time": 27.154048204421997, "code": "def tuple_to_int(nums):\n    for num in nums:\n        if not isinstance(num, int) or num < 0:\n            raise ValueError(\"Tuple must contain only positive integers.\")\n    if not nums:\n        return 0\n    return int(''.join(map(str, nums)))", "problem_index": 0, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "308928d6", "prompt": "Write a function that takes in two numbers and returns a tuple with the second number and then the first number.\n\ndef swap_numbers(a,b):", "solution": "```python\ndef swap_numbers(a: float, b: float) -> tuple:\n    return (b, a)\n```", "execution_time": 20.059487104415894, "code": "def swap_numbers(a: float, b: float) -> tuple:\n    return (b, a)", "problem_index": 1, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "a696ab6a", "prompt": "Write a python function to find the last digit of a given number.\n\ndef last_Digit(n) :", "solution": "```python\ndef last_digit(n):\n    if not isinstance(n, int):\n        raise TypeError(\"Input must be an integer\")\n    return abs(n) % 10\n```", "execution_time": 18.45028781890869, "code": "def last_digit(n):\n    if not isinstance(n, int):\n        raise TypeError(\"Input must be an integer\")\n    return abs(n) % 10", "problem_index": 2, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "e4887640", "prompt": "Write a function to check whether it follows the sequence given in the patterns array.\n\ndef is_samepatterns(colors, patterns):    ", "solution": "```python\nfrom typing import List, Any\n\ndef matches_patterns(colors: List[Any], patterns: List[Any]) -> bool:\n    if not colors or not patterns:\n        return False\n\n    if len(colors) != len(patterns):\n        return False\n    \n    color_to_pattern = {}\n    pattern_to_color = {}\n    \n    for color, pattern in zip(colors, patterns):\n        if color in color_to_pattern:\n            if color_to_pattern[color] != pattern:\n                return False\n        else:\n            color_to_pattern[color] = pattern\n        \n        if pattern in pattern_to_color:\n            if pattern_to_color[pattern] != color:\n                return False\n        else:\n            pattern_to_color[pattern] = color\n            \n    return True\n```", "execution_time": 26.89754891395569, "code": "from typing import List, Any\n\ndef matches_patterns(colors: List[Any], patterns: List[Any]) -> bool:\n    if not colors or not patterns:\n        return False\n\n    if len(colors) != len(patterns):\n        return False\n    \n    color_to_pattern = {}\n    pattern_to_color = {}\n    \n    for color, pattern in zip(colors, patterns):\n        if color in color_to_pattern:\n            if color_to_pattern[color] != pattern:\n                return False\n        else:\n            color_to_pattern[color] = pattern\n        \n        if pattern in pattern_to_color:\n            if pattern_to_color[pattern] != color:\n                return False\n        else:\n            pattern_to_color[pattern] = color\n            \n    return True", "problem_index": 3, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "c0f57d53", "prompt": "Write a python function to check whether the given number can be represented as sum of non-zero powers of 2 or not.\n\ndef is_Sum_Of_Powers_Of_Two(n): ", "solution": "```python\ndef is_Sum_Of_Powers_Of_Two(n):\n    if n <= 0:\n        return False\n    # Check if n can be expressed as a sum of distinct powers of 2\n    return (n & (n - 1)) == 0\n```", "execution_time": 25.428828954696655, "code": "def is_Sum_Of_Powers_Of_Two(n):\n    if n <= 0:\n        return False\n    # Check if n can be expressed as a sum of distinct powers of 2\n    return (n & (n - 1)) == 0", "problem_index": 4, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "7c5f3e96", "prompt": "Write a python function to find sum of products of all possible sublists of a given list. https://www.geeksforgeeks.org/sum-of-products-of-all-possible-subarrays/\n\ndef sum_Of_Subarray_Prod(arr):", "solution": "```python\ndef sum_of_subarray_products(arr):\n    if not isinstance(arr, list):\n        raise TypeError(\"Input must be a list.\")\n    if not all(isinstance(x, (int, float)) for x in arr):\n        raise ValueError(\"List elements must be numeric.\")\n    \n    total_sum = 0\n    n = len(arr)\n    \n    for i in range(n):\n        product = 1\n        for j in range(i, n):\n            product *= arr[j]\n            total_sum += product\n            \n    return total_sum\n```", "execution_time": 25.314258337020874, "code": "def sum_of_subarray_products(arr):\n    if not isinstance(arr, list):\n        raise TypeError(\"Input must be a list.\")\n    if not all(isinstance(x, (int, float)) for x in arr):\n        raise ValueError(\"List elements must be numeric.\")\n    \n    total_sum = 0\n    n = len(arr)\n    \n    for i in range(n):\n        product = 1\n        for j in range(i, n):\n            product *= arr[j]\n            total_sum += product\n            \n    return total_sum", "problem_index": 5, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "7d98aed4", "prompt": "Write a function to calculate the maximum aggregate from the list of tuples.\nfrom collections import defaultdict\ndef max_aggregate(stdata):", "solution": "```python\nfrom collections import defaultdict\n\ndef max_aggregate(stdata: list[tuple[str, int]]) -> dict[str, int]:\n    \"\"\"\n    Calculates the maximum aggregate value for each unique key in a list of tuples.\n\n    Args:\n        stdata: A list of tuples, where each tuple contains a key (string) and a value (integer).\n\n    Returns:\n        A dictionary with keys and their corresponding maximum aggregate values.\n    \"\"\"\n    if not isinstance(stdata, list):\n        raise TypeError(\"Input must be a list.\")\n\n    max_values = defaultdict(int)\n    \n    for key, value in stdata:\n        if not isinstance(key, str):\n            raise TypeError(\"Key must be a string.\")\n        if not isinstance(value, (int, float)):\n            raise TypeError(\"Value must be a number.\")\n        max_values[key] = max(max_values[key], value)\n    \n    return dict(max_values)\n```", "execution_time": 21.641855001449585, "code": "from collections import defaultdict\n\ndef max_aggregate(stdata: list[tuple[str, int]]) -> dict[str, int]:\n    \"\"\"\n    Calculates the maximum aggregate value for each unique key in a list of tuples.\n\n    Args:\n        stdata: A list of tuples, where each tuple contains a key (string) and a value (integer).\n\n    Returns:\n        A dictionary with keys and their corresponding maximum aggregate values.\n    \"\"\"\n    if not isinstance(stdata, list):\n        raise TypeError(\"Input must be a list.\")\n\n    max_values = defaultdict(int)\n    \n    for key, value in stdata:\n        if not isinstance(key, str):\n            raise TypeError(\"Key must be a string.\")\n        if not isinstance(value, (int, float)):\n            raise TypeError(\"Value must be a number.\")\n        max_values[key] = max(max_values[key], value)\n    \n    return dict(max_values)", "problem_index": 6, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "ebb0bd43", "prompt": "Write a function to find the directrix of a parabola.\n\ndef parabola_directrix(a, b, c): ", "solution": "def parabola_directrix(a, b, c):\n    \"\"\"\n    Calculate the directrix of a parabola given its equation in the standard form y = ax^2 + bx + c.\n\n    Args:\n        a (float): The coefficient of the x^2 term.\n        b (float): The coefficient of the x term.\n        c (float): The constant term.\n\n    Returns:\n        float: The y-value of the directrix.\n\n    Raises:\n        ValueError: If the coefficient 'a' is zero (not a parabola).\n    \"\"\"\n    if a == 0:\n        raise ValueError(\"Coefficient 'a' must not be zero for a parabola.\")\n    \n    # Calculate the vertex\n    x_v = -b / (2 * a)\n    y_v = a * x_v**2 + b * x_v + c\n    \n    # Calculate p\n    p = 1 / (4 * a)\n    \n    # Calculate the directrix. y_v is the y-coordinate of the vertex (k).\n    directrix = y_v - p\n    \n    return directrix", "execution_time": 26.90897488594055, "code": "def parabola_directrix(a, b, c):\n    \"\"\"\n    Calculate the directrix of a parabola given its equation in the standard form y = ax^2 + bx + c.\n\n    Args:\n        a (float): The coefficient of the x^2 term.\n        b (float): The coefficient of the x term.\n        c (float): The constant term.\n\n    Returns:\n        float: The y-value of the directrix.\n\n    Raises:\n        ValueError: If the coefficient 'a' is zero (not a parabola).\n    \"\"\"\n    if a == 0:\n        raise ValueError(\"Coefficient 'a' must not be zero for a parabola.\")\n    \n    # Calculate the vertex\n    x_v = -b / (2 * a)\n    y_v = a * x_v**2 + b * x_v + c\n    \n    # Calculate p\n    p = 1 / (4 * a)\n    \n    # Calculate the directrix. y_v is the y-coordinate of the vertex (k).\n    directrix = y_v - p\n    \n    return directrix", "problem_index": 7, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "65d22f83", "prompt": "Write function to find the sum of all items in the given dictionary.\n\ndef return_sum(dict):", "solution": "```python\ndef return_sum(input_dict):\n    \"\"\"Returns the sum of all numeric values in the input dictionary.\"\"\"\n    if not isinstance(input_dict, dict):\n        raise TypeError(\"Input must be a dictionary.\")\n    return sum(value for value in input_dict.values() if isinstance(value, (int, float))) if input_dict else 0\n```", "execution_time": 24.351478815078735, "code": "def return_sum(input_dict):\n    \"\"\"Returns the sum of all numeric values in the input dictionary.\"\"\"\n    if not isinstance(input_dict, dict):\n        raise TypeError(\"Input must be a dictionary.\")\n    return sum(value for value in input_dict.values() if isinstance(value, (int, float))) if input_dict else 0", "problem_index": 8, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "6692deb7", "prompt": "Write a python function to find the sum of the product of consecutive binomial co-efficients.\n\ndef sum_Of_product(n): ", "solution": "```python\nfrom math import comb\n\ndef sum_of_product_coefficients(n):\n    if n < 1:\n        return 0\n    \n    total_sum = sum(comb(n, k) * comb(n, k+1) for k in range(n))\n    \n    return total_sum\n```", "execution_time": 25.928658962249756, "code": "from math import comb\n\ndef sum_of_product_coefficients(n):\n    if n < 1:\n        return 0\n    \n    total_sum = sum(comb(n, k) * comb(n, k+1) for k in range(n))\n    \n    return total_sum", "problem_index": 9, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "7c5fd4fd", "prompt": "Write a function to sort the given list.\nimport heapq as hq\ndef heap_sort(iterable):", "solution": "```python\nimport heapq\n\ndef heap_sort(iterable):\n    \"\"\"Sorts a list using the heap sort algorithm.\"\"\"\n    heapq.heapify(iterable)\n    sorted_list = []\n    # Extract elements from the heap and add to the sorted list\n    while iterable:\n        sorted_list.append(heapq.heappop(iterable))\n    return sorted_list\n```", "execution_time": 23.32334613800049, "code": "import heapq\n\ndef heap_sort(iterable):\n    \"\"\"Sorts a list using the heap sort algorithm.\"\"\"\n    heapq.heapify(iterable)\n    sorted_list = []\n    # Extract elements from the heap and add to the sorted list\n    while iterable:\n        sorted_list.append(heapq.heappop(iterable))\n    return sorted_list", "problem_index": 10, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "8f7cef48", "prompt": "Write a function to move all the numbers to the end of the given string.\n\ndef move_num(test_str):", "solution": "```python\ndef move_num(test_str: str) -> str:\n    \"\"\"\n    Moves all numeric characters in a string to the end, preserving the original order of both numeric and non-numeric characters.\n\n    Args:\n        test_str: The input string.\n\n    Returns:\n        A new string with all numeric characters moved to the end.\n    \"\"\"\n    non_nums = [char for char in test_str if not char.isdigit()]\n    nums = [char for char in test_str if char.isdigit()]\n\n    return ''.join(non_nums + nums)\n```", "execution_time": 27.6219801902771, "code": "def move_num(test_str: str) -> str:\n    \"\"\"\n    Moves all numeric characters in a string to the end, preserving the original order of both numeric and non-numeric characters.\n\n    Args:\n        test_str: The input string.\n\n    Returns:\n        A new string with all numeric characters moved to the end.\n    \"\"\"\n    non_nums = [char for char in test_str if not char.isdigit()]\n    nums = [char for char in test_str if char.isdigit()]\n\n    return ''.join(non_nums + nums)", "problem_index": 11, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "c291215e", "prompt": "Write a function to find squares of individual elements in a list.\n\ndef square_nums(nums):", "solution": "```python\ndef square_nums(nums):\n    \"\"\"Returns a list of squares of the given numbers.\"\"\"\n    return [x ** 2 for x in nums]\n```", "execution_time": 17.709468841552734, "code": "def square_nums(nums):\n    \"\"\"Returns a list of squares of the given numbers.\"\"\"\n    return [x ** 2 for x in nums]", "problem_index": 12, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "933cbe6e", "prompt": "Write a function to check if a string is present as a substring in a given list of string values.\n\ndef find_substring(str1, sub_str):", "solution": "```python\nfrom typing import List\n\ndef contains_substring(string_list: List[str], sub_str: str) -> bool:\n    if not isinstance(string_list, list):\n        raise TypeError(\"string_list must be a list\")\n    if not isinstance(sub_str, str):\n        raise TypeError(\"sub_str must be a string\")\n    return any(sub_str in s for s in string_list)\n```", "execution_time": 21.92643928527832, "code": "from typing import List\n\ndef contains_substring(string_list: List[str], sub_str: str) -> bool:\n    if not isinstance(string_list, list):\n        raise TypeError(\"string_list must be a list\")\n    if not isinstance(sub_str, str):\n        raise TypeError(\"sub_str must be a string\")\n    return any(sub_str in s for s in string_list)", "problem_index": 13, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "641efb36", "prompt": "Write a function to remove leading zeroes from an ip address.\nimport re\ndef removezero_ip(ip):", "solution": "```python\nimport ipaddress\n\ndef removezero_ip(ip):\n    \"\"\"\n    Removes unnecessary leading zeros from each octet of an IP address.\n\n    Args:\n        ip (str): The IP address string.\n\n    Returns:\n        str: The IP address string with leading zeros removed from each octet.\n\n    Raises:\n        ValueError: If the input is not a valid IP address.\n    \"\"\"\n    try:\n        ipaddress.ip_address(ip)  # Validate IP address format\n    except ValueError:\n        raise ValueError(\"Invalid IP address format\")\n\n    # Split IP into octets, convert each to int (drops leading zeros), convert back to string\n    return '.'.join(str(int(octet)) for octet in ip.split('.'))\n```", "execution_time": 21.500885009765625, "code": "import ipaddress\n\ndef removezero_ip(ip):\n    \"\"\"\n    Removes unnecessary leading zeros from each octet of an IP address.\n\n    Args:\n        ip (str): The IP address string.\n\n    Returns:\n        str: The IP address string with leading zeros removed from each octet.\n\n    Raises:\n        ValueError: If the input is not a valid IP address.\n    \"\"\"\n    try:\n        ipaddress.ip_address(ip)  # Validate IP address format\n    except ValueError:\n        raise ValueError(\"Invalid IP address format\")\n\n    # Split IP into octets, convert each to int (drops leading zeros), convert back to string\n    return '.'.join(str(int(octet)) for octet in ip.split('.'))", "problem_index": 14, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "bcc365e1", "prompt": "Write a function to replace whitespaces with an underscore and vice versa in a given string.\n\ndef replace_spaces(text):", "solution": "```python\ndef replace_spaces(text):\n    \"\"\"\n    Replaces single whitespace characters in a string with underscores and underscores with whitespace characters.\n\n    Args:\n        text: The input string.\n\n    Returns:\n        A new string with whitespace characters replaced by underscores and underscores replaced by whitespace characters.\n        Only single whitespace characters and underscores are replaced, not multiple consecutive ones.\n    \"\"\"\n    return ''.join('_' if char.isspace() else ' ' if char == '_' else char for char in text)\n```", "execution_time": 22.104541778564453, "code": "def replace_spaces(text):\n    \"\"\"\n    Replaces single whitespace characters in a string with underscores and underscores with whitespace characters.\n\n    Args:\n        text: The input string.\n\n    Returns:\n        A new string with whitespace characters replaced by underscores and underscores replaced by whitespace characters.\n        Only single whitespace characters and underscores are replaced, not multiple consecutive ones.\n    \"\"\"\n    return ''.join('_' if char.isspace() else ' ' if char == '_' else char for char in text)", "problem_index": 15, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "f5ac9623", "prompt": "Write a function to find area of a sector. The function takes the radius and angle as inputs. Function should return None if the angle is larger than 360 degrees.\nimport math\ndef sector_area(r,a):", "solution": "```python\nimport math\n\ndef sector_area(r, a):\n    if r <= 0:\n        return None\n    if a > 360:\n        return None\n    return (a / 360) * math.pi * r**2\n```", "execution_time": 20.239212036132812, "code": "import math\n\ndef sector_area(r, a):\n    if r <= 0:\n        return None\n    if a > 360:\n        return None\n    return (a / 360) * math.pi * r**2", "problem_index": 16, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "c072ee1e", "prompt": "Write a python function to find the sum of the per-digit difference between two integers.\n\ndef digit_distance_nums(n1, n2):", "solution": "```python\ndef digit_distance_nums(n1, n2):\n    \"\"\"\n    Calculate the sum of the absolute differences between corresponding digits of two integers.\n    If the integers have different lengths, treat missing digits as zero.\n\n    Args:\n        n1 (int): The first integer.\n        n2 (int): The second integer.\n\n    Returns:\n        int: The sum of the absolute differences between corresponding digits.\n\n    Raises:\n        TypeError: If either n1 or n2 is not an integer.\n    \"\"\"\n    if not isinstance(n1, int):\n        raise TypeError(\"n1 must be an integer\")\n    if not isinstance(n2, int):\n        raise TypeError(\"n2 must be an integer\")\n\n    # Convert numbers to strings\n    str_n1 = str(n1)\n    str_n2 = str(n2)\n    \n    # Pad the shorter string with leading zeros\n    max_length = max(len(str_n1), len(str_n2))\n    str_n1 = str_n1.zfill(max_length)\n    str_n2 = str_n2.zfill(max_length)\n    \n    # Calculate the sum of absolute differences\n    return sum(abs(int(d1) - int(d2)) for d1, d2 in zip(str_n1, str_n2))\n```", "execution_time": 23.246283054351807, "code": "def digit_distance_nums(n1, n2):\n    \"\"\"\n    Calculate the sum of the absolute differences between corresponding digits of two integers.\n    If the integers have different lengths, treat missing digits as zero.\n\n    Args:\n        n1 (int): The first integer.\n        n2 (int): The second integer.\n\n    Returns:\n        int: The sum of the absolute differences between corresponding digits.\n\n    Raises:\n        TypeError: If either n1 or n2 is not an integer.\n    \"\"\"\n    if not isinstance(n1, int):\n        raise TypeError(\"n1 must be an integer\")\n    if not isinstance(n2, int):\n        raise TypeError(\"n2 must be an integer\")\n\n    # Convert numbers to strings\n    str_n1 = str(n1)\n    str_n2 = str(n2)\n    \n    # Pad the shorter string with leading zeros\n    max_length = max(len(str_n1), len(str_n2))\n    str_n1 = str_n1.zfill(max_length)\n    str_n2 = str_n2.zfill(max_length)\n    \n    # Calculate the sum of absolute differences\n    return sum(abs(int(d1) - int(d2)) for d1, d2 in zip(str_n1, str_n2))", "problem_index": 17, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "06b823c1", "prompt": "Write a python function to find the largest number that can be formed with the given list of digits.\n\ndef find_Max_Num(arr) : ", "solution": "```python\ndef find_max_num(arr):\n    \"\"\"\n    Finds the largest number that can be formed by concatenating a list of digits.\n\n    Args:\n        arr (list of int): A list of digits (integers).\n\n    Returns:\n        str: The largest possible number formed by concatenating the digits,\n             or an empty string if the input is invalid.\n    \"\"\"\n    from functools import cmp_to_key\n\n    # Input validation: check for empty list or non-digit inputs\n    if not arr:\n        return \"\"\n    if not all(isinstance(x, int) and 0 <= x <= 9 for x in arr):\n        return \"\"\n\n    def compare(x, y):\n        # Compare concatenated results\n        if x + y > y + x:\n            return -1  # x should come before y\n        else:\n            return 1   # y should come before x\n\n    # Convert integers to strings for concatenation\n    arr = list(map(str, arr))\n    # Sort using the custom comparator\n    arr.sort(key=cmp_to_key(compare))\n    # Join sorted array into a single string and return\n    return ''.join(arr)\n```", "execution_time": 26.82543706893921, "code": "def find_max_num(arr):\n    \"\"\"\n    Finds the largest number that can be formed by concatenating a list of digits.\n\n    Args:\n        arr (list of int): A list of digits (integers).\n\n    Returns:\n        str: The largest possible number formed by concatenating the digits,\n             or an empty string if the input is invalid.\n    \"\"\"\n    from functools import cmp_to_key\n\n    # Input validation: check for empty list or non-digit inputs\n    if not arr:\n        return \"\"\n    if not all(isinstance(x, int) and 0 <= x <= 9 for x in arr):\n        return \"\"\n\n    def compare(x, y):\n        # Compare concatenated results\n        if x + y > y + x:\n            return -1  # x should come before y\n        else:\n            return 1   # y should come before x\n\n    # Convert integers to strings for concatenation\n    arr = list(map(str, arr))\n    # Sort using the custom comparator\n    arr.sort(key=cmp_to_key(compare))\n    # Join sorted array into a single string and return\n    return ''.join(arr)", "problem_index": 18, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "5c043b78", "prompt": "Write a function to count those characters which have vowels as their neighbors in the given string.\n\ndef count_vowels(test_str):", "solution": "```python\ndef count_vowels(test_str):\n    \"\"\"\n    Counts the number of characters in a string that have at least one vowel as a neighbor.\n\n    Args:\n        test_str: The input string.\n\n    Returns:\n        The number of characters with vowel neighbors.\n    \"\"\"\n    if not test_str:\n        return 0\n\n    vowels = set('aeiouAEIOU')\n\n    count = 0\n\n    for i in range(len(test_str)):\n        left_vowel = i > 0 and test_str[i-1] in vowels\n        right_vowel = i < len(test_str)-1 and test_str[i+1] in vowels\n\n        if left_vowel or right_vowel:\n            count += 1\n\n    return count\n```", "execution_time": 22.688647985458374, "code": "def count_vowels(test_str):\n    \"\"\"\n    Counts the number of characters in a string that have at least one vowel as a neighbor.\n\n    Args:\n        test_str: The input string.\n\n    Returns:\n        The number of characters with vowel neighbors.\n    \"\"\"\n    if not test_str:\n        return 0\n\n    vowels = set('aeiouAEIOU')\n\n    count = 0\n\n    for i in range(len(test_str)):\n        left_vowel = i > 0 and test_str[i-1] in vowels\n        right_vowel = i < len(test_str)-1 and test_str[i+1] in vowels\n\n        if left_vowel or right_vowel:\n            count += 1\n\n    return count", "problem_index": 19, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "31f0ca1c", "prompt": "Write a python function that takes in a non-negative number and returns the number of prime numbers less than the given non-negative number.\n\ndef count_Primes_nums(n):", "solution": "```python\ndef count_primes(n):\n    if n < 2:\n        return 0\n    \n    is_prime = [True] * n\n    is_prime[0] = is_prime[1] = False  # 0 and 1 are not prime numbers\n    \n    for i in range(2, int(n**0.5) + 1):\n        if is_prime[i]:\n            for j in range(i * i, n, i):\n                is_prime[j] = False\n                \n    return sum(is_prime)\n```", "execution_time": 23.409174919128418, "code": "def count_primes(n):\n    if n < 2:\n        return 0\n    \n    is_prime = [True] * n\n    is_prime[0] = is_prime[1] = False  # 0 and 1 are not prime numbers\n    \n    for i in range(2, int(n**0.5) + 1):\n        if is_prime[i]:\n            for j in range(i * i, n, i):\n                is_prime[j] = False\n                \n    return sum(is_prime)", "problem_index": 20, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "0d11f421", "prompt": "Write a function to find sum and average of first n natural numbers.\n\ndef sum_average(number):", "solution": "```python\ndef sum_average(n: int) -> tuple[int, float]:\n    \"\"\"\n    Calculate the sum and average of the first n natural numbers.\n\n    Args:\n        n: The number of natural numbers to consider. Must be a positive integer.\n\n    Returns:\n        A tuple containing the sum and average of the first n natural numbers.\n\n    Raises:\n        ValueError: If n is not a positive integer.\n    \"\"\"\n    if n <= 0:\n        raise ValueError(\"Input must be a positive integer.\")\n    \n    total_sum = n * (n + 1) // 2\n    average = total_sum / n\n    return total_sum, average\n```", "execution_time": 20.3632071018219, "code": "def sum_average(n: int) -> tuple[int, float]:\n    \"\"\"\n    Calculate the sum and average of the first n natural numbers.\n\n    Args:\n        n: The number of natural numbers to consider. Must be a positive integer.\n\n    Returns:\n        A tuple containing the sum and average of the first n natural numbers.\n\n    Raises:\n        ValueError: If n is not a positive integer.\n    \"\"\"\n    if n <= 0:\n        raise ValueError(\"Input must be a positive integer.\")\n    \n    total_sum = n * (n + 1) // 2\n    average = total_sum / n\n    return total_sum, average", "problem_index": 21, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "45084a5f", "prompt": "Write a python function to check whether the two numbers differ at one bit position only or not.\n\ndef differ_At_One_Bit_Pos(a,b): ", "solution": "```python\ndef differ_At_One_Bit_Pos(a: int, b: int) -> bool:\n    \"\"\"\n    Checks if two integers differ by exactly one bit position.\n\n    Args:\n        a: The first integer.\n        b: The second integer.\n\n    Returns:\n        True if the integers differ by only one bit, False otherwise.\n    \"\"\"\n    if not isinstance(a, int) or not isinstance(b, int):\n        raise TypeError(\"Inputs must be integers\")\n    xor_result = a ^ b\n    return xor_result != 0 and (xor_result & (xor_result - 1)) == 0\n```", "execution_time": 25.231464862823486, "code": "def differ_At_One_Bit_Pos(a: int, b: int) -> bool:\n    \"\"\"\n    Checks if two integers differ by exactly one bit position.\n\n    Args:\n        a: The first integer.\n        b: The second integer.\n\n    Returns:\n        True if the integers differ by only one bit, False otherwise.\n    \"\"\"\n    if not isinstance(a, int) or not isinstance(b, int):\n        raise TypeError(\"Inputs must be integers\")\n    xor_result = a ^ b\n    return xor_result != 0 and (xor_result & (xor_result - 1)) == 0", "problem_index": 22, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "83baf451", "prompt": "Write a function to find the nth octagonal number.\n\ndef is_octagonal(n): ", "solution": "```python\ndef nth_octagonal(n):\n    \"\"\"\n    Calculate the nth octagonal number.\n\n    The octagonal number is defined by the formula O(n) = n(3n - 2).\n\n    Args:\n        n (int): The index of the octagonal number to calculate. Must be a positive integer.\n\n    Returns:\n        int: The nth octagonal number.\n\n    Raises:\n        ValueError: If n is not a positive integer.\n    \"\"\"\n    if not isinstance(n, int) or n <= 0:\n        raise ValueError(\"n must be a positive integer\")\n    return n * (3 * n - 2)\n```", "execution_time": 19.770432949066162, "code": "def nth_octagonal(n):\n    \"\"\"\n    Calculate the nth octagonal number.\n\n    The octagonal number is defined by the formula O(n) = n(3n - 2).\n\n    Args:\n        n (int): The index of the octagonal number to calculate. Must be a positive integer.\n\n    Returns:\n        int: The nth octagonal number.\n\n    Raises:\n        ValueError: If n is not a positive integer.\n    \"\"\"\n    if not isinstance(n, int) or n <= 0:\n        raise ValueError(\"n must be a positive integer\")\n    return n * (3 * n - 2)", "problem_index": 23, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "1ffdeadf", "prompt": "Write a python function to find the smallest missing number from a sorted list of natural numbers.\n\ndef find_First_Missing(array,start=0,end=None):", "solution": "```python\ndef find_First_Missing(array: list[int], start: int=0, end: int | None=None) -> int:\n    \"\"\"\n    Finds the smallest missing natural number in a sorted list of integers.\n\n    The function uses a binary search approach. The core idea is that if the element at a given index 'mid'\n    is equal to 'mid', it means all elements before 'mid' are in their correct positions, so the missing number\n    must be in the right half of the array. Otherwise, the missing number is in the left half.\n    \"\"\"\n    # Handle empty array case\n    if not array:\n        return start\n    \n    # Set default end if not provided\n    if end is None:\n        end = len(array) - 1\n    \n    # Input validation: Ensure the input list is sorted and contains integers\n    if not all(isinstance(x, int) for x in array):\n        raise TypeError(\"Input list must contain integers only.\")\n    if any(array[i] > array[i+1] for i in range(len(array)-1)):\n        raise ValueError(\"Input list must be sorted in ascending order.\")\n    \n    # If first element doesn't match start, missing number is start\n    if array[0] != start:\n        return start\n    \n    # Binary search to find missing number\n    while start <= end:\n        mid = (start + end) // 2\n        \n        # If mid index matches value, search right half\n        if array[mid] == mid:\n            start = mid + 1\n        # Otherwise, search left half\n        else:\n            end = mid - 1\n    \n    # Return first missing number\n    return start\n```", "execution_time": 27.64445686340332, "code": "def find_First_Missing(array: list[int], start: int=0, end: int | None=None) -> int:\n    \"\"\"\n    Finds the smallest missing natural number in a sorted list of integers.\n\n    The function uses a binary search approach. The core idea is that if the element at a given index 'mid'\n    is equal to 'mid', it means all elements before 'mid' are in their correct positions, so the missing number\n    must be in the right half of the array. Otherwise, the missing number is in the left half.\n    \"\"\"\n    # Handle empty array case\n    if not array:\n        return start\n    \n    # Set default end if not provided\n    if end is None:\n        end = len(array) - 1\n    \n    # Input validation: Ensure the input list is sorted and contains integers\n    if not all(isinstance(x, int) for x in array):\n        raise TypeError(\"Input list must contain integers only.\")\n    if any(array[i] > array[i+1] for i in range(len(array)-1)):\n        raise ValueError(\"Input list must be sorted in ascending order.\")\n    \n    # If first element doesn't match start, missing number is start\n    if array[0] != start:\n        return start\n    \n    # Binary search to find missing number\n    while start <= end:\n        mid = (start + end) // 2\n        \n        # If mid index matches value, search right half\n        if array[mid] == mid:\n            start = mid + 1\n        # Otherwise, search left half\n        else:\n            end = mid - 1\n    \n    # Return first missing number\n    return start", "problem_index": 24, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "995f56ea", "prompt": "Write a function to append the given list to the given tuples.\n\ndef add_lists(test_list, test_tup):", "solution": "def append_list_to_tuple(test_list, test_tup):\n    return tuple(list(test_tup) + test_list)", "execution_time": 17.734450101852417, "code": "def append_list_to_tuple(test_list, test_tup):\n    return tuple(list(test_tup) + test_list)", "problem_index": 25, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "a6d98971", "prompt": "Write a function to caluclate the area of a tetrahedron.\nimport math\ndef area_tetrahedron(side):", "solution": "```python\nimport math\n\ndef area_tetrahedron(side):\n    \"\"\"\n    Calculate the surface area of a regular tetrahedron.\n\n    A regular tetrahedron is a polyhedron with four faces, each of which is an equilateral triangle.\n    It is one of the five Platonic solids.\n\n    Args:\n        side (float): The length of a side of the tetrahedron.\n\n    Returns:\n        float: The surface area of the tetrahedron, rounded to two decimal places.\n\n    Raises:\n        ValueError: If the side length is not positive.\n    \"\"\"\n    if side <= 0:\n        raise ValueError(\"Side length must be positive\")\n    \n    # Surface area formula for regular tetrahedron\n    area = math.sqrt(3) * side**2\n    \n    return round(area, 2)\n```", "execution_time": 23.33023500442505, "code": "import math\n\ndef area_tetrahedron(side):\n    \"\"\"\n    Calculate the surface area of a regular tetrahedron.\n\n    A regular tetrahedron is a polyhedron with four faces, each of which is an equilateral triangle.\n    It is one of the five Platonic solids.\n\n    Args:\n        side (float): The length of a side of the tetrahedron.\n\n    Returns:\n        float: The surface area of the tetrahedron, rounded to two decimal places.\n\n    Raises:\n        ValueError: If the side length is not positive.\n    \"\"\"\n    if side <= 0:\n        raise ValueError(\"Side length must be positive\")\n    \n    # Surface area formula for regular tetrahedron\n    area = math.sqrt(3) * side**2\n    \n    return round(area, 2)", "problem_index": 26, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "14647f39", "prompt": "Write a function to find all possible combinations of the elements of a given list.\n\ndef combinations_list(list1):", "solution": "def generate_all_combinations(list1):\n    \"\"\"\n    Generates all possible combinations of elements from a given list.\n\n    Args:\n        list1: The input list.\n\n    Returns:\n        A list of tuples, where each tuple represents a combination of elements from the input list.\n    \"\"\"\n    from itertools import combinations\n    result = []\n    for r in range(len(list1) + 1):\n        result.extend(combinations(list1, r))\n    return result", "execution_time": 23.207886934280396, "code": "def generate_all_combinations(list1):\n    \"\"\"\n    Generates all possible combinations of elements from a given list.\n\n    Args:\n        list1: The input list.\n\n    Returns:\n        A list of tuples, where each tuple represents a combination of elements from the input list.\n    \"\"\"\n    from itertools import combinations\n    result = []\n    for r in range(len(list1) + 1):\n        result.extend(combinations(list1, r))\n    return result", "problem_index": 27, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "a8904aca", "prompt": "Write a function to find the common elements in given nested lists.\n\ndef common_in_nested_lists(nestedlist):", "solution": "```python\ndef common_in_nested_lists(nestedlist: list[list]) -> list:\n    if not nestedlist or len(nestedlist) < 2:\n        return []\n    \n    common = set(nestedlist[0])\n    \n    for sublist in nestedlist[1:]:\n        common = common.intersection(set(sublist))\n    \n    return list(common)\n```", "execution_time": 25.86490511894226, "code": "def common_in_nested_lists(nestedlist: list[list]) -> list:\n    if not nestedlist or len(nestedlist) < 2:\n        return []\n    \n    common = set(nestedlist[0])\n    \n    for sublist in nestedlist[1:]:\n        common = common.intersection(set(sublist))\n    \n    return list(common)", "problem_index": 28, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "927fba9a", "prompt": "Write a function that takes in a string and character, replaces blank spaces in the string with the character, and returns the string.\n\ndef replace_blank(str1,char):", "solution": "```python\ndef replace_blank(input_string, char):\n    if not isinstance(input_string, str):\n        raise TypeError(\"Input must be a string\")\n    return input_string.replace(' ', char)\n```", "execution_time": 18.521948099136353, "code": "def replace_blank(input_string, char):\n    if not isinstance(input_string, str):\n        raise TypeError(\"Input must be a string\")\n    return input_string.replace(' ', char)", "problem_index": 29, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "afc27325", "prompt": "Write a function to check if the given tuples contain the k or not.\n\ndef check_K(test_tup, K):", "solution": "```python\ndef check_K(test_tup, K):\n    if not isinstance(test_tup, tuple):\n        raise TypeError(\"test_tup must be a tuple\")\n    return K in test_tup\n```", "execution_time": 25.077692985534668, "code": "def check_K(test_tup, K):\n    if not isinstance(test_tup, tuple):\n        raise TypeError(\"test_tup must be a tuple\")\n    return K in test_tup", "problem_index": 30, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "40e52f63", "prompt": "Write a function to find the minimum product from the pairs of tuples within a given list.\n\ndef min_product_tuple(list1):", "solution": "```python\ndef min_product_tuple(list1):\n    \"\"\"\n    Calculates the minimum product of pairs of integers in a list of tuples.\n\n    Args:\n        list1: A list of tuples, where each tuple contains two integers.\n\n    Returns:\n        The minimum product of the pairs of integers in the list.\n        Returns None if the input list is empty.\n\n    Raises:\n        ValueError: If the input list contains tuples that do not contain exactly two integers,\n                    or if the tuple contains non-integer values.\n    \"\"\"\n    if not list1:\n        return None\n    \n    min_product = float('inf')\n    for tup in list1:\n        if not isinstance(tup, tuple) or len(tup) != 2:\n            raise ValueError(\"Each element in the list must be a tuple of exactly two integers.\")\n        \n        a, b = tup\n        if not isinstance(a, int) or not isinstance(b, int):\n            raise ValueError(\"Tuple elements must be integers.\")\n            \n        product = a * b\n        if product < min_product:\n            min_product = product\n            \n    return min_product\n```", "execution_time": 26.07943105697632, "code": "def min_product_tuple(list1):\n    \"\"\"\n    Calculates the minimum product of pairs of integers in a list of tuples.\n\n    Args:\n        list1: A list of tuples, where each tuple contains two integers.\n\n    Returns:\n        The minimum product of the pairs of integers in the list.\n        Returns None if the input list is empty.\n\n    Raises:\n        ValueError: If the input list contains tuples that do not contain exactly two integers,\n                    or if the tuple contains non-integer values.\n    \"\"\"\n    if not list1:\n        return None\n    \n    min_product = float('inf')\n    for tup in list1:\n        if not isinstance(tup, tuple) or len(tup) != 2:\n            raise ValueError(\"Each element in the list must be a tuple of exactly two integers.\")\n        \n        a, b = tup\n        if not isinstance(a, int) or not isinstance(b, int):\n            raise ValueError(\"Tuple elements must be integers.\")\n            \n        product = a * b\n        if product < min_product:\n            min_product = product\n            \n    return min_product", "problem_index": 31, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "a90c9dd6", "prompt": "Write a function to sort a list of elements.\n\ndef comb_sort(nums):", "solution": "```python\ndef comb_sort(nums):\n    def get_next_gap(gap):\n        gap = (gap * 10) // 13\n        if gap < 1:\n            return 1\n        return gap\n\n    n = len(nums)\n    gap = n\n    swapped = True\n\n    while gap != 1 or swapped:\n        gap = get_next_gap(gap)\n        swapped = False\n\n        for i in range(0, n - gap):\n            j = i + gap\n            if nums[i] > nums[j]:\n                nums[i], nums[j] = nums[j], nums[i]\n                swapped = True\n\n    return nums\n```", "execution_time": 23.217339992523193, "code": "def comb_sort(nums):\n    def get_next_gap(gap):\n        gap = (gap * 10) // 13\n        if gap < 1:\n            return 1\n        return gap\n\n    n = len(nums)\n    gap = n\n    swapped = True\n\n    while gap != 1 or swapped:\n        gap = get_next_gap(gap)\n        swapped = False\n\n        for i in range(0, n - gap):\n            j = i + gap\n            if nums[i] > nums[j]:\n                nums[i], nums[j] = nums[j], nums[i]\n                swapped = True\n\n    return nums", "problem_index": 32, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "9fbf9d48", "prompt": "Write a function to check for the number of jumps required of given length to reach a point of form (d, 0) from origin in a 2d plane.\n\ndef min_Jumps(steps, d): ", "solution": "```python\nimport math\n\ndef min_jumps_to_point(steps, d):\n    \"\"\"\n    Calculates the minimum number of jumps required to reach a point (d, 0) from the origin (0, 0) in a 2D plane.\n\n    Args:\n        steps (int or float): The length of each jump. Must be greater than zero.\n        d (int or float): The horizontal distance to the target point (d, 0). Must be non-negative.\n\n    Returns:\n        int: The minimum number of jumps required to reach the point (d, 0).\n\n    Raises:\n        ValueError: If 'steps' is not greater than zero or if 'd' is negative.\n    \"\"\"\n    if steps <= 0:\n        raise ValueError(\"Jump length must be greater than zero.\")\n    if d < 0:\n        raise ValueError(\"Distance 'd' must be non-negative.\")\n    return math.ceil(d / steps)\n```", "execution_time": 28.382534980773926, "code": "import math\n\ndef min_jumps_to_point(steps, d):\n    \"\"\"\n    Calculates the minimum number of jumps required to reach a point (d, 0) from the origin (0, 0) in a 2D plane.\n\n    Args:\n        steps (int or float): The length of each jump. Must be greater than zero.\n        d (int or float): The horizontal distance to the target point (d, 0). Must be non-negative.\n\n    Returns:\n        int: The minimum number of jumps required to reach the point (d, 0).\n\n    Raises:\n        ValueError: If 'steps' is not greater than zero or if 'd' is negative.\n    \"\"\"\n    if steps <= 0:\n        raise ValueError(\"Jump length must be greater than zero.\")\n    if d < 0:\n        raise ValueError(\"Distance 'd' must be non-negative.\")\n    return math.ceil(d / steps)", "problem_index": 33, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "605760bc", "prompt": "Write a function to extract the nth element from a given list of tuples.\n\ndef extract_nth_element(list1, n):", "solution": "```python\ndef extract_nth_element(list1, n):\n    \"\"\"\n    Extract the nth element from each tuple in a given list of tuples.\n\n    Args:\n        list1 (list): A list of tuples.\n        n (int): The index of the element to extract.\n\n    Returns:\n        list: A list of nth elements from each tuple.\n\n    Raises:\n        ValueError: If n is negative.\n    \"\"\"\n    if n < 0:\n        raise ValueError(\"Index n must be non-negative.\")\n    result = [tup[n] for tup in list1 if n < len(tup)]\n    return result\n```", "execution_time": 23.621994018554688, "code": "def extract_nth_element(list1, n):\n    \"\"\"\n    Extract the nth element from each tuple in a given list of tuples.\n\n    Args:\n        list1 (list): A list of tuples.\n        n (int): The index of the element to extract.\n\n    Returns:\n        list: A list of nth elements from each tuple.\n\n    Raises:\n        ValueError: If n is negative.\n    \"\"\"\n    if n < 0:\n        raise ValueError(\"Index n must be non-negative.\")\n    result = [tup[n] for tup in list1 if n < len(tup)]\n    return result", "problem_index": 34, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "35d135bf", "prompt": "Write a python function to find the cube sum of first n even natural numbers.\n\ndef cube_Sum(n): ", "solution": "```python\ndef cube_sum(n):\n    \"\"\"\n    Calculate the sum of the cubes of the first n even natural numbers.\n\n    Args:\n        n (int): The number of even natural numbers to consider. Must be a positive integer.\n\n    Returns:\n        int: The sum of the cubes of the first n even natural numbers.\n\n    Raises:\n        TypeError: if n is not an integer.\n        ValueError: if n is not a positive integer.\n    \"\"\"\n    if not isinstance(n, int):\n        raise TypeError(\"Input must be an integer.\")\n    if n <= 0:\n        raise ValueError(\"Input must be a positive integer.\")\n\n    return 2 * n**2 * (n + 1)**2\n```", "execution_time": 20.66021704673767, "code": "def cube_sum(n):\n    \"\"\"\n    Calculate the sum of the cubes of the first n even natural numbers.\n\n    Args:\n        n (int): The number of even natural numbers to consider. Must be a positive integer.\n\n    Returns:\n        int: The sum of the cubes of the first n even natural numbers.\n\n    Raises:\n        TypeError: if n is not an integer.\n        ValueError: if n is not a positive integer.\n    \"\"\"\n    if not isinstance(n, int):\n        raise TypeError(\"Input must be an integer.\")\n    if n <= 0:\n        raise ValueError(\"Input must be a positive integer.\")\n\n    return 2 * n**2 * (n + 1)**2", "problem_index": 35, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "4e7a8f0c", "prompt": "Write a python function to check whether the given array is monotonic or not.\n\ndef is_Monotonic(A): ", "solution": "def is_Monotonic(A):\n    \"\"\"\n    Checks if an array is monotonic (either monotonically increasing or monotonically decreasing).\n\n    An array is monotonically increasing if for all i <= j, A[i] <= A[j].\n    An array is monotonically decreasing if for all i <= j, A[i] >= A[j].\n\n    Args:\n        A: A list of numbers.\n\n    Returns:\n        True if the array is monotonic, False otherwise.\n    \"\"\"\n    if len(A) <= 1:\n        return True\n    \n    increasing = decreasing = True\n    \n    for i in range(1, len(A)):\n        if A[i] < A[i-1]:\n            increasing = False\n        \n        if A[i] > A[i-1]:\n            decreasing = False\n    \n    return increasing or decreasing", "execution_time": 23.287840843200684, "code": "def is_Monotonic(A):\n    \"\"\"\n    Checks if an array is monotonic (either monotonically increasing or monotonically decreasing).\n\n    An array is monotonically increasing if for all i <= j, A[i] <= A[j].\n    An array is monotonically decreasing if for all i <= j, A[i] >= A[j].\n\n    Args:\n        A: A list of numbers.\n\n    Returns:\n        True if the array is monotonic, False otherwise.\n    \"\"\"\n    if len(A) <= 1:\n        return True\n    \n    increasing = decreasing = True\n    \n    for i in range(1, len(A)):\n        if A[i] < A[i-1]:\n            increasing = False\n        \n        if A[i] > A[i-1]:\n            decreasing = False\n    \n    return increasing or decreasing", "problem_index": 36, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "2793386a", "prompt": "Write a python function to remove first and last occurrence of a given character from the string.\n\ndef remove_Occ(s,ch): ", "solution": "def remove_first_last_occurrence(s: str, ch: str) -> str:\n    if not isinstance(s, str):\n        return \"\"\n    if not s:\n        return s\n    first_index = s.find(ch)\n    last_index = s.rfind(ch)\n    \n    if first_index == -1 or first_index == last_index:\n        return s  # Return original string if character not found or only one occurrence\n    \n    return s[:first_index] + s[first_index + 1:last_index] + s[last_index + 1:]", "execution_time": 22.73598623275757, "code": "def remove_first_last_occurrence(s: str, ch: str) -> str:\n    if not isinstance(s, str):\n        return \"\"\n    if not s:\n        return s\n    first_index = s.find(ch)\n    last_index = s.rfind(ch)\n    \n    if first_index == -1 or first_index == last_index:\n        return s  # Return original string if character not found or only one occurrence\n    \n    return s[:first_index] + s[first_index + 1:last_index] + s[last_index + 1:]", "problem_index": 37, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "8d0acb22", "prompt": "Write a function to find the product of first even and odd number of a given list.\n\ndef mul_even_odd(list1):", "solution": "```python\ndef mul_even_odd(list1: list[int]) -> int | None:\n    \"\"\"\n    Finds the product of the first even and first odd number in a list.\n\n    Handles edge cases:\n    - If the list is empty or contains only even or odd numbers, it returns None.\n    \"\"\"\n    if not isinstance(list1, list):\n        raise TypeError(\"Input must be a list.\")\n    if not all(isinstance(num, (int, float)) for num in list1):\n        raise TypeError(\"List elements must be numeric.\")\n\n    first_even = None\n    first_odd = None\n    \n    for num in list1:\n        if first_even is None and num % 2 == 0:\n            first_even = num\n        if first_odd is None and num % 2 != 0:\n            first_odd = num\n        \n        if first_even is not None and first_odd is not None:\n            break\n    \n    return first_even * first_odd if first_even and first_odd else None\n```", "execution_time": 23.97945284843445, "code": "def mul_even_odd(list1: list[int]) -> int | None:\n    \"\"\"\n    Finds the product of the first even and first odd number in a list.\n\n    Handles edge cases:\n    - If the list is empty or contains only even or odd numbers, it returns None.\n    \"\"\"\n    if not isinstance(list1, list):\n        raise TypeError(\"Input must be a list.\")\n    if not all(isinstance(num, (int, float)) for num in list1):\n        raise TypeError(\"List elements must be numeric.\")\n\n    first_even = None\n    first_odd = None\n    \n    for num in list1:\n        if first_even is None and num % 2 == 0:\n            first_even = num\n        if first_odd is None and num % 2 != 0:\n            first_odd = num\n        \n        if first_even is not None and first_odd is not None:\n            break\n    \n    return first_even * first_odd if first_even and first_odd else None", "problem_index": 38, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "9b85c473", "prompt": "Write a python function to find the product of the array multiplication modulo n.\n\ndef find_remainder(arr, n): ", "solution": "```python\ndef find_remainder(arr: list[int], n: int) -> int:\n    if not arr:\n        return 1\n    if n <= 0:\n        raise ValueError(\"n must be a positive integer\")\n    for num in arr:\n        if not isinstance(num, int):\n            raise TypeError(\"Array elements must be integers\")\n    product = 1\n    for num in arr:\n        product = (product * num) % n\n    return product\n```", "execution_time": 37.00697422027588, "code": "def find_remainder(arr: list[int], n: int) -> int:\n    if not arr:\n        return 1\n    if n <= 0:\n        raise ValueError(\"n must be a positive integer\")\n    for num in arr:\n        if not isinstance(num, int):\n            raise TypeError(\"Array elements must be integers\")\n    product = 1\n    for num in arr:\n        product = (product * num) % n\n    return product", "problem_index": 39, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "6c739b33", "prompt": "Write a python function to check whether all the bits are unset in the given range or not.\n\ndef all_Bits_Set_In_The_Given_Range(n,l,r):  ", "solution": "```python\ndef is_range_unset(n, left, right):\n    if left > right or left < 0 or right < 0:\n        return True  # Or raise an exception, depending on desired behavior\n    # Create a mask with all bits set to 1 in the given range\n    mask = ((1 << (right - left + 1)) - 1) << left\n    \n    # Use bitwise AND operation to check if any bits are set in the given range\n    return (n & mask) == 0\n```", "execution_time": 26.470157146453857, "code": "def is_range_unset(n, left, right):\n    if left > right or left < 0 or right < 0:\n        return True  # Or raise an exception, depending on desired behavior\n    # Create a mask with all bits set to 1 in the given range\n    mask = ((1 << (right - left + 1)) - 1) << left\n    \n    # Use bitwise AND operation to check if any bits are set in the given range\n    return (n & mask) == 0", "problem_index": 40, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "263734c9", "prompt": "Write a function to find the perimeter of a regular pentagon from the length of its sides.\nimport math\ndef perimeter_pentagon(a):", "solution": "def perimeter_pentagon(a):\n    \"\"\"\n    Calculate the perimeter of a regular pentagon.\n    \n    Args:\n        a (float): Length of one side of the pentagon\n    \n    Returns:\n        float: Total perimeter of the pentagon\n    \"\"\"\n    if not isinstance(a, (int, float)):\n        raise ValueError(\"Side length must be a number.\")\n    if a <= 0:\n        raise ValueError(\"Side length must be a positive number.\")\n    return 5 * a", "execution_time": 17.043078184127808, "code": "def perimeter_pentagon(a):\n    \"\"\"\n    Calculate the perimeter of a regular pentagon.\n    \n    Args:\n        a (float): Length of one side of the pentagon\n    \n    Returns:\n        float: Total perimeter of the pentagon\n    \"\"\"\n    if not isinstance(a, (int, float)):\n        raise ValueError(\"Side length must be a number.\")\n    if a <= 0:\n        raise ValueError(\"Side length must be a positive number.\")\n    return 5 * a", "problem_index": 41, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "02ad8662", "prompt": "Write a function that checks if a strings contains 'z', except at the start and end of the word.\nimport re\ndef text_match_wordz_middle(text):", "solution": "```python\ndef text_match_wordz_middle(text):\n    \"\"\"Returns True if 'z' is in the middle of the input string, False otherwise.\n\n    Args:\n        text: The input string to check.\n\n    Returns:\n        True if 'z' is present in the string, excluding the first and last characters; False otherwise.\n    \"\"\"\n    if len(text) < 3:  # Edge case: strings shorter than 3 cannot have 'z' in the middle\n        return False\n    return 'z' in text[1:-1]  # Check for 'z' in the substring excluding the first and last characters\n```", "execution_time": 22.242594957351685, "code": "def text_match_wordz_middle(text):\n    \"\"\"Returns True if 'z' is in the middle of the input string, False otherwise.\n\n    Args:\n        text: The input string to check.\n\n    Returns:\n        True if 'z' is present in the string, excluding the first and last characters; False otherwise.\n    \"\"\"\n    if len(text) < 3:  # Edge case: strings shorter than 3 cannot have 'z' in the middle\n        return False\n    return 'z' in text[1:-1]  # Check for 'z' in the substring excluding the first and last characters", "problem_index": 42, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "f0bc4ebc", "prompt": "Write a function to find the combinations of sums with tuples in the given tuple list. https://www.geeksforgeeks.org/python-combinations-of-sum-with-tuples-in-tuple-list/\nfrom itertools import combinations \ndef find_combinations(test_list):", "solution": "```python\nfrom itertools import combinations\nfrom typing import List, <PERSON><PERSON>\n\ndef find_combinations(tuple_list: List[Tuple[int]]) -> List[Tuple[int]]:\n    \"\"\"\n    Calculates all unique combinations of sums of tuples in a list.\n\n    Args:\n        tuple_list: A list of tuples containing integers.\n\n    Returns:\n        A list of unique tuples, where each tuple represents the sum of elements\n        from a combination of tuples in the input list.\n    \"\"\"\n    if not isinstance(tuple_list, list):\n        raise TypeError(\"Input must be a list.\")\n    if not all(isinstance(t, tuple) for t in tuple_list):\n        raise TypeError(\"All elements in the list must be tuples.\")\n    if tuple_list and not all(all(isinstance(x, int) for x in t) for t in tuple_list):\n        raise TypeError(\"All tuple elements must be integers.\")\n\n    result = set()\n    for r in range(1, len(tuple_list) + 1):\n        for combo in combinations(tuple_list, r):\n            result.add(tuple(map(sum, zip(*combo))))\n    return list(result)\n```", "execution_time": 23.610538005828857, "code": "from itertools import combinations\nfrom typing import List, <PERSON><PERSON>\n\ndef find_combinations(tuple_list: List[Tuple[int]]) -> List[Tuple[int]]:\n    \"\"\"\n    Calculates all unique combinations of sums of tuples in a list.\n\n    Args:\n        tuple_list: A list of tuples containing integers.\n\n    Returns:\n        A list of unique tuples, where each tuple represents the sum of elements\n        from a combination of tuples in the input list.\n    \"\"\"\n    if not isinstance(tuple_list, list):\n        raise TypeError(\"Input must be a list.\")\n    if not all(isinstance(t, tuple) for t in tuple_list):\n        raise TypeError(\"All elements in the list must be tuples.\")\n    if tuple_list and not all(all(isinstance(x, int) for x in t) for t in tuple_list):\n        raise TypeError(\"All tuple elements must be integers.\")\n\n    result = set()\n    for r in range(1, len(tuple_list) + 1):\n        for combo in combinations(tuple_list, r):\n            result.add(tuple(map(sum, zip(*combo))))\n    return list(result)", "problem_index": 43, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "265e4211", "prompt": "Write a function that takes in two lists and replaces the last element of the first list with the elements of the second list.\n\ndef replace_list(list1,list2):", "solution": "def replace_last_with_elements(list1: list, list2: list) -> list:\n    if not list1:\n        return list2\n    if not list2:\n        return list1[:-1]\n    list1 = list1[:-1]\n    list1.extend(list2)\n    return list1", "execution_time": 25.59131693840027, "code": "def replace_last_with_elements(list1: list, list2: list) -> list:\n    if not list1:\n        return list2\n    if not list2:\n        return list1[:-1]\n    list1 = list1[:-1]\n    list1.extend(list2)\n    return list1", "problem_index": 44, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "550d068a", "prompt": "Write a python function to set all even bits of a given number.\n\ndef even_bit_set_number(n): ", "solution": "def even_bit_set_number(n):\n    even_bit_mask = 0x55555555\n    return n | even_bit_mask", "execution_time": 25.16191792488098, "code": "def even_bit_set_number(n):\n    even_bit_mask = 0x55555555\n    return n | even_bit_mask", "problem_index": 45, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "71d45879", "prompt": "Write a function to calculate a dog's age in dog's years.\n\ndef dog_age(h_age):", "solution": "def dog_age(h_age):\n    if h_age < 0:\n        raise ValueError(\"Human age cannot be negative\")\n    elif h_age <= 2:\n        return h_age * 10.5\n    else:\n        return 21 + (h_age - 2) * 4", "execution_time": 21.665977001190186, "code": "def dog_age(h_age):\n    if h_age < 0:\n        raise ValueError(\"Human age cannot be negative\")\n    elif h_age <= 2:\n        return h_age * 10.5\n    else:\n        return 21 + (h_age - 2) * 4", "problem_index": 46, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "1e1ca210", "prompt": "Write a python function which takes a list of lists, where each sublist has two elements, and returns a list of two lists where the first list has the first element of each sublist and the second one has the second.\n\ndef merge(lst):  ", "solution": "def merge(lst):\n    if not lst:\n        return [[], []]\n    \n    first_elements = []\n    second_elements = []\n    \n    for sublist in lst:\n        if len(sublist) != 2:\n            raise ValueError(\"Each sublist must contain exactly two elements.\")\n        first_elements.append(sublist[0])\n        second_elements.append(sublist[1])\n    \n    return [first_elements, second_elements]", "execution_time": 23.078339099884033, "code": "def merge(lst):\n    if not lst:\n        return [[], []]\n    \n    first_elements = []\n    second_elements = []\n    \n    for sublist in lst:\n        if len(sublist) != 2:\n            raise ValueError(\"Each sublist must contain exactly two elements.\")\n        first_elements.append(sublist[0])\n        second_elements.append(sublist[1])\n    \n    return [first_elements, second_elements]", "problem_index": 47, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "f907246e", "prompt": "Write a function to interleave 3 lists of the same length into a single flat list.\n\ndef interleave_lists(list1,list2,list3):", "solution": "def interleave_lists(list1: list, list2: list, list3: list) -> list:\n    if len(set(map(len, [list1, list2, list3]))) > 1:\n        raise ValueError(\"All lists must be the same length\")\n    return [elem for group in zip(list1, list2, list3) for elem in group]", "execution_time": 23.279819011688232, "code": "def interleave_lists(list1: list, list2: list, list3: list) -> list:\n    if len(set(map(len, [list1, list2, list3]))) > 1:\n        raise ValueError(\"All lists must be the same length\")\n    return [elem for group in zip(list1, list2, list3) for elem in group]", "problem_index": 48, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "709b8f7f", "prompt": "Write a function that checks whether a string contains the 'a' character followed by two or three 'b' characters.\nimport re\ndef text_match_two_three(text):", "solution": "```python\nimport re\n\ndef text_match_two_three(text):\n    \"\"\"\n    Checks if a string contains the pattern 'a' followed by exactly 2 or 3 'b' characters.\n\n    Args:\n        text: The input string to check.\n\n    Returns:\n        True if the pattern is found in the string, False otherwise.\n    \"\"\"\n    pattern = r'ab{2,3}'  # 'a' followed by exactly 2 or 3 'b' characters\n    return bool(re.search(pattern, text))\n```", "execution_time": 38.35737204551697, "code": "import re\n\ndef text_match_two_three(text):\n    \"\"\"\n    Checks if a string contains the pattern 'a' followed by exactly 2 or 3 'b' characters.\n\n    Args:\n        text: The input string to check.\n\n    Returns:\n        True if the pattern is found in the string, False otherwise.\n    \"\"\"\n    pattern = r'ab{2,3}'  # 'a' followed by exactly 2 or 3 'b' characters\n    return bool(re.search(pattern, text))", "problem_index": 49, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "4e1327cc", "prompt": "Write a function to count lists within a list. The function should return a dictionary where every list is converted to a tuple and the value of such tuple is the number of its occurencies in the original list.\n\ndef unique_sublists(list1):", "solution": "```python\ndef count_sublists(list1: list[list]) -> dict[tuple, int]:\n    \"\"\"\n    Counts the occurrences of each sublist within a list.\n\n    Args:\n        list1: A list of lists.\n\n    Returns:\n        A dictionary where keys are tuples (converted from lists) and values are their respective counts.\n    \"\"\"\n    if not isinstance(list1, list):\n        raise TypeError(\"Input must be a list.\")\n    for sublist in list1:\n        if not isinstance(sublist, list):\n            raise TypeError(\"All elements of the input list must be lists.\")\n\n    count_dict = {}\n    for sublist in list1:\n        key = tuple(sublist)\n        count_dict[key] = count_dict.get(key, 0) + 1\n    return count_dict\n```", "execution_time": 26.107097864151, "code": "def count_sublists(list1: list[list]) -> dict[tuple, int]:\n    \"\"\"\n    Counts the occurrences of each sublist within a list.\n\n    Args:\n        list1: A list of lists.\n\n    Returns:\n        A dictionary where keys are tuples (converted from lists) and values are their respective counts.\n    \"\"\"\n    if not isinstance(list1, list):\n        raise TypeError(\"Input must be a list.\")\n    for sublist in list1:\n        if not isinstance(sublist, list):\n            raise TypeError(\"All elements of the input list must be lists.\")\n\n    count_dict = {}\n    for sublist in list1:\n        key = tuple(sublist)\n        count_dict[key] = count_dict.get(key, 0) + 1\n    return count_dict", "problem_index": 50, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "da351511", "prompt": "Write a python function to check whether the count of divisors is even. https://www.w3resource.com/python-exercises/basic/python-basic-1-exercise-24.php\nimport math \ndef count_divisors(n) : ", "solution": "```python\nimport math \ndef count_divisors(n):\n    \"\"\"\n    Counts the number of divisors of a given integer and checks if the count is even.\n\n    Args:\n        n (int): The integer for which to count divisors.\n\n    Returns:\n        bool: True if the number of divisors is even, False otherwise.\n    \"\"\"\n    if n <= 0:\n        return False\n    \n    divisor_count = 0\n    for i in range(1, int(math.sqrt(n)) + 1):\n        if n % i == 0:\n            if i * i == n:\n                divisor_count += 1\n            else:\n                divisor_count += 2\n    \n    return divisor_count % 2 == 0\n```", "execution_time": 25.296671867370605, "code": "import math \ndef count_divisors(n):\n    \"\"\"\n    Counts the number of divisors of a given integer and checks if the count is even.\n\n    Args:\n        n (int): The integer for which to count divisors.\n\n    Returns:\n        bool: True if the number of divisors is even, False otherwise.\n    \"\"\"\n    if n <= 0:\n        return False\n    \n    divisor_count = 0\n    for i in range(1, int(math.sqrt(n)) + 1):\n        if n % i == 0:\n            if i * i == n:\n                divisor_count += 1\n            else:\n                divisor_count += 2\n    \n    return divisor_count % 2 == 0", "problem_index": 51, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "20851c40", "prompt": "Write a python function to check whether a list of numbers contains only one distinct element or not.\n\ndef unique_Element(arr):", "solution": "```python\ndef unique_element(arr):\n    if not isinstance(arr, list):\n        raise TypeError(\"Input must be a list.\")\n    if len(arr) <= 1:\n        return True\n    first_element = arr[0]\n    return all(x == first_element for x in arr)\n```", "execution_time": 21.840672969818115, "code": "def unique_element(arr):\n    if not isinstance(arr, list):\n        raise TypeError(\"Input must be a list.\")\n    if len(arr) <= 1:\n        return True\n    first_element = arr[0]\n    return all(x == first_element for x in arr)", "problem_index": 52, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "66b80a14", "prompt": "Write a python function to check if the elements of a given list are unique or not.\n\ndef all_unique(test_list):", "solution": "```python\ndef all_unique(test_list):\n    return len(test_list) == len(set(test_list))\n```", "execution_time": 19.722572088241577, "code": "def all_unique(test_list):\n    return len(test_list) == len(set(test_list))", "problem_index": 53, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "471a854d", "prompt": "Write a python function to find the highest power of 2 that is less than or equal to n.\n\ndef highest_Power_of_2(n): ", "solution": "def highest_Power_of_2(n):\n    if n <= 0:\n        return 0\n    # Calculate 2^(bit_length(n)-1) using left shift\n    return 1 << (n.bit_length() - 1)", "execution_time": 21.1663019657135, "code": "def highest_Power_of_2(n):\n    if n <= 0:\n        return 0\n    # Calculate 2^(bit_length(n)-1) using left shift\n    return 1 << (n.bit_length() - 1)", "problem_index": 54, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "7cfff903", "prompt": "Write a function that takes in an array and an integer n, and re-arranges the first n elements of the given array so that all negative elements appear before positive ones, and where the relative order among negative and positive elements is preserved.\n\ndef re_arrange_array(arr, n):", "solution": "```python\ndef re_arrange_array(arr, n):\n    if n > len(arr):\n        n = len(arr)\n    negative = [x for x in arr[:n] if x < 0]\n    positive = [x for x in arr[:n] if x >= 0]\n    arr[:n] = negative + positive\n    return arr\n```", "execution_time": 24.647156953811646, "code": "def re_arrange_array(arr, n):\n    if n > len(arr):\n        n = len(arr)\n    negative = [x for x in arr[:n] if x < 0]\n    positive = [x for x in arr[:n] if x >= 0]\n    arr[:n] = negative + positive\n    return arr", "problem_index": 55, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "e36d4b73", "prompt": "Write a function that matches a string that has an a followed by three 'b'.\nimport re\ndef text_match_three(text):", "solution": "def text_match_three(text):\n    import re\n    pattern = r'ab{3}'\n    if re.search(pattern, text):\n        return True\n    return False", "execution_time": 21.8730309009552, "code": "def text_match_three(text):\n    import re\n    pattern = r'ab{3}'\n    if re.search(pattern, text):\n        return True\n    return False", "problem_index": 56, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "40eeec12", "prompt": "Write a function to check whether an element exists within a tuple.\n\ndef check_tuplex(tuplex,tuple1): ", "solution": "def check_tuplex(tuplex: tuple, element: any) -> bool:\n    return element in tuplex", "execution_time": 23.977617740631104, "code": "def check_tuplex(tuplex: tuple, element: any) -> bool:\n    return element in tuplex", "problem_index": 57, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "66a682d1", "prompt": "Write a function to remove consecutive duplicates of a given list.\nfrom itertools import groupby\ndef consecutive_duplicates(nums):", "solution": "```python\nfrom itertools import groupby\n\ndef consecutive_duplicates(nums):\n    \"\"\"Removes consecutive duplicate elements from a list, keeping only unique consecutive elements.\"\"\"\n    # groupby groups consecutive identical elements together\n    return [k for k, _ in groupby(nums)]\n```", "execution_time": 21.451069116592407, "code": "from itertools import groupby\n\ndef consecutive_duplicates(nums):\n    \"\"\"Removes consecutive duplicate elements from a list, keeping only unique consecutive elements.\"\"\"\n    # groupby groups consecutive identical elements together\n    return [k for k, _ in groupby(nums)]", "problem_index": 58, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "ca524162", "prompt": "Write a python function to split a string into characters.\n\ndef split(word): ", "solution": "```python\ndef split_into_characters(word):\n    \"\"\"\n    Splits a string into a list of its individual characters.\n\n    Args:\n        word: The input string.\n\n    Returns:\n        A list of characters in the string.\n\n    Raises:\n        TypeError: If the input is not a string.\n    \"\"\"\n    if not isinstance(word, str):\n        raise TypeError(\"Input must be a string\")\n    return list(word)\n```", "execution_time": 18.401551961898804, "code": "def split_into_characters(word):\n    \"\"\"\n    Splits a string into a list of its individual characters.\n\n    Args:\n        word: The input string.\n\n    Returns:\n        A list of characters in the string.\n\n    Raises:\n        TypeError: If the input is not a string.\n    \"\"\"\n    if not isinstance(word, str):\n        raise TypeError(\"Input must be a string\")\n    return list(word)", "problem_index": 59, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "1c80a1c3", "prompt": "Write a function to find the ratio of zeroes to non-zeroes in an array of integers.\nfrom array import array\ndef zero_count(nums):", "solution": "```python\ndef zero_count(nums: list[int]) -> float:\n    if not nums:\n        return 0\n    \n    zero_count = sum(1 for num in nums if num == 0)\n    non_zero_count = len(nums) - zero_count\n    \n    return zero_count / non_zero_count if non_zero_count > 0 else 0\n```", "execution_time": 22.435726165771484, "code": "def zero_count(nums: list[int]) -> float:\n    if not nums:\n        return 0\n    \n    zero_count = sum(1 for num in nums if num == 0)\n    non_zero_count = len(nums) - zero_count\n    \n    return zero_count / non_zero_count if non_zero_count > 0 else 0", "problem_index": 60, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "55aefa18", "prompt": "Write a python function to find the sublist having minimum length.\n\ndef Find_Min(lst): ", "solution": "def find_min(lst: list) -> list:\n    if not isinstance(lst, list):\n        raise TypeError(\"Input must be a list.\")\n\n    if not lst:\n        return []\n\n    unique_elements = set(lst)\n    required_count = len(unique_elements)\n    element_count = {}\n    left = 0\n    min_length = float('inf')\n    min_sublist = []\n\n    for right in range(len(lst)):\n        element_count[lst[right]] = element_count.get(lst[right], 0) + 1\n\n        while len(element_count) == required_count:\n            current_length = right - left + 1\n            if current_length < min_length:\n                min_length = current_length\n                min_sublist = lst[left:right + 1]\n\n            element_count[lst[left]] -= 1\n            if element_count[lst[left]] == 0:\n                del element_count[lst[left]]\n            left += 1\n\n    # Time Complexity: O(N), where N is the length of the input list.\n    return min_sublist\n", "execution_time": 25.238955974578857, "code": "def find_min(lst: list) -> list:\n    if not isinstance(lst, list):\n        raise TypeError(\"Input must be a list.\")\n\n    if not lst:\n        return []\n\n    unique_elements = set(lst)\n    required_count = len(unique_elements)\n    element_count = {}\n    left = 0\n    min_length = float('inf')\n    min_sublist = []\n\n    for right in range(len(lst)):\n        element_count[lst[right]] = element_count.get(lst[right], 0) + 1\n\n        while len(element_count) == required_count:\n            current_length = right - left + 1\n            if current_length < min_length:\n                min_length = current_length\n                min_sublist = lst[left:right + 1]\n\n            element_count[lst[left]] -= 1\n            if element_count[lst[left]] == 0:\n                del element_count[lst[left]]\n            left += 1\n\n    # Time Complexity: O(N), where N is the length of the input list.\n    return min_sublist", "problem_index": 61, "partial_success": true, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "2649d2ec", "prompt": "Write a function to concatenate each element of tuple by the delimiter.\n\ndef concatenate_tuple(test_tup):", "solution": "```python\ndef concatenate_tuple(test_tup, delimiter=','):\n    try:\n        if not isinstance(delimiter, str):\n            raise TypeError(\"Delimiter must be a string\")\n        return delimiter.join(map(str, test_tup))\n    except TypeError as e:\n        return str(e)\n    except Exception as e:\n        return \"Error: Input must be iterable\"\n```", "execution_time": 19.677654027938843, "code": "def concatenate_tuple(test_tup, delimiter=','):\n    try:\n        if not isinstance(delimiter, str):\n            raise TypeError(\"Delimiter must be a string\")\n        return delimiter.join(map(str, test_tup))\n    except TypeError as e:\n        return str(e)\n    except Exception as e:\n        return \"Error: Input must be iterable\"", "problem_index": 62, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "81c19934", "prompt": "Write a python function to find the sum of common divisors of two given numbers.\n\ndef sum(a,b): ", "solution": "```python\ndef sum_of_common_divisors(a, b):\n    if not isinstance(a, int) or not isinstance(b, int) or a <= 0 or b <= 0:\n        return 0\n\n    def find_divisors(n):\n        divisors = set()\n        for i in range(1, int(n**0.5) + 1):\n            if n % i == 0:\n                divisors.add(i)\n                divisors.add(n // i)\n        return divisors\n\n    divisors_a = find_divisors(a)\n    divisors_b = find_divisors(b)\n    common_divisors = divisors_a & divisors_b\n    return sum(common_divisors)\n```", "execution_time": 23.604037046432495, "code": "def sum_of_common_divisors(a, b):\n    if not isinstance(a, int) or not isinstance(b, int) or a <= 0 or b <= 0:\n        return 0\n\n    def find_divisors(n):\n        divisors = set()\n        for i in range(1, int(n**0.5) + 1):\n            if n % i == 0:\n                divisors.add(i)\n                divisors.add(n // i)\n        return divisors\n\n    divisors_a = find_divisors(a)\n    divisors_b = find_divisors(b)\n    common_divisors = divisors_a & divisors_b\n    return sum(common_divisors)", "problem_index": 63, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "f018df46", "prompt": "Write a python function to check whether any value in a sequence exists in a sequence or not.\n\ndef overlapping(list1,list2):  ", "solution": "def overlapping(list1, list2):\n    \"\"\"\n    Checks if there is any overlap between two lists.\n\n    Args:\n        list1: The first list.\n        list2: The second list.\n\n    Returns:\n        True if there is at least one common element between the lists, False otherwise.\n    \"\"\"\n    # Convert both lists to sets for O(1) lookup, making intersection efficient\n    return bool(set(list1) & set(list2))", "execution_time": 24.92622208595276, "code": "def overlapping(list1, list2):\n    \"\"\"\n    Checks if there is any overlap between two lists.\n\n    Args:\n        list1: The first list.\n        list2: The second list.\n\n    Returns:\n        True if there is at least one common element between the lists, False otherwise.\n    \"\"\"\n    # Convert both lists to sets for O(1) lookup, making intersection efficient\n    return bool(set(list1) & set(list2))", "problem_index": 64, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "4972219f", "prompt": "Write a function to subtract two lists element-wise.\n\ndef sub_list(nums1,nums2):", "solution": "```python\nfrom typing import List\n\ndef sub_list(nums1: List[float], nums2: List[float]) -> List[float]:\n    \"\"\"\n    Subtracts elements of two lists element-wise.\n\n    Args:\n        nums1: The first list of numbers.\n        nums2: The second list of numbers.\n\n    Returns:\n        A new list containing the element-wise difference of nums1 and nums2.\n\n    Raises:\n        ValueError: If the lists are not of the same length.\n    \"\"\"\n    if len(nums1) != len(nums2):\n        raise ValueError(\"Both lists must be of the same length.\")\n    return [a - b for a, b in zip(nums1, nums2)]\n```", "execution_time": 19.22129797935486, "code": "from typing import List\n\ndef sub_list(nums1: List[float], nums2: List[float]) -> List[float]:\n    \"\"\"\n    Subtracts elements of two lists element-wise.\n\n    Args:\n        nums1: The first list of numbers.\n        nums2: The second list of numbers.\n\n    Returns:\n        A new list containing the element-wise difference of nums1 and nums2.\n\n    Raises:\n        ValueError: If the lists are not of the same length.\n    \"\"\"\n    if len(nums1) != len(nums2):\n        raise ValueError(\"Both lists must be of the same length.\")\n    return [a - b for a, b in zip(nums1, nums2)]", "problem_index": 65, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "9f49fa1f", "prompt": "Write a function to count the total number of characters in a string.\n\ndef count_charac(str1):", "solution": "def count_characters(str1):\n    \"\"\"\n    Counts the total number of characters in a given string, including spaces and punctuation.\n\n    Args:\n        str1: The input string.\n\n    Returns:\n        The length of the string. Returns 0 if the input is None.\n    \"\"\"\n    if str1 is None:\n        return 0\n    return len(str1)", "execution_time": 20.304079055786133, "code": "def count_characters(str1):\n    \"\"\"\n    Counts the total number of characters in a given string, including spaces and punctuation.\n\n    Args:\n        str1: The input string.\n\n    Returns:\n        The length of the string. Returns 0 if the input is None.\n    \"\"\"\n    if str1 is None:\n        return 0\n    return len(str1)", "problem_index": 66, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "906db2fa", "prompt": "Write a python function to count minimum number of swaps required to convert one binary number represented as a string to another.\n\ndef min_Swaps(str1,str2) : ", "solution": "```python\ndef min_Swaps(str1, str2):\n    \"\"\"\n    Calculates the minimum number of swaps needed to convert str1 to str2.\n\n    Args:\n        str1 (str): The first binary string.\n        str2 (str): The second binary string.\n\n    Returns:\n        int: The minimum number of swaps needed, or -1 if the strings are of different lengths.\n    \"\"\"\n    if len(str1) != len(str2):\n        return -1\n    \n    count_0_to_1 = 0\n    count_1_to_0 = 0\n    \n    for a, b in zip(str1, str2):\n        if a == '0' and b == '1':\n            count_0_to_1 += 1\n        elif a == '1' and b == '0':\n            count_1_to_0 += 1\n    \n    return max(count_0_to_1, count_1_to_0)\n```", "execution_time": 23.92155885696411, "code": "def min_Swaps(str1, str2):\n    \"\"\"\n    Calculates the minimum number of swaps needed to convert str1 to str2.\n\n    Args:\n        str1 (str): The first binary string.\n        str2 (str): The second binary string.\n\n    Returns:\n        int: The minimum number of swaps needed, or -1 if the strings are of different lengths.\n    \"\"\"\n    if len(str1) != len(str2):\n        return -1\n    \n    count_0_to_1 = 0\n    count_1_to_0 = 0\n    \n    for a, b in zip(str1, str2):\n        if a == '0' and b == '1':\n            count_0_to_1 += 1\n        elif a == '1' and b == '0':\n            count_1_to_0 += 1\n    \n    return max(count_0_to_1, count_1_to_0)", "problem_index": 67, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "cd1148d1", "prompt": "Write a function to count the number of lists within a list. The function should return a dictionary, where every list is turned to a tuple, and the value of the tuple is the number of its occurrences.\n\ndef unique_sublists(list1):", "solution": "```python\ndef count_unique_sublists(list1: list[list]) -> dict[tuple, int]:\n    \"\"\"\n    Counts the occurrences of unique sublists within a given list.\n\n    Args:\n        list1: A list of sublists.\n\n    Returns:\n        A dictionary where keys are unique sublists (converted to tuples)\n        and values are their corresponding counts.\n    \"\"\"\n    sublist_counts = {}\n\n    for sublist in list1:\n        tuple_sublist = tuple(sublist)\n        sublist_counts[tuple_sublist] = sublist_counts.get(tuple_sublist, 0) + 1\n\n    return sublist_counts\n```", "execution_time": 21.54171395301819, "code": "def count_unique_sublists(list1: list[list]) -> dict[tuple, int]:\n    \"\"\"\n    Counts the occurrences of unique sublists within a given list.\n\n    Args:\n        list1: A list of sublists.\n\n    Returns:\n        A dictionary where keys are unique sublists (converted to tuples)\n        and values are their corresponding counts.\n    \"\"\"\n    sublist_counts = {}\n\n    for sublist in list1:\n        tuple_sublist = tuple(sublist)\n        sublist_counts[tuple_sublist] = sublist_counts.get(tuple_sublist, 0) + 1\n\n    return sublist_counts", "problem_index": 68, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "4d018c91", "prompt": "Write a python function to find the sum of xor of all pairs of numbers in the given list.\n\ndef pair_xor_Sum(arr,n) : ", "solution": "```python\ndef pair_xor_sum(arr: list[int]) -> int:\n    \"\"\"\n    Calculates the sum of the XOR of all unique pairs of numbers in a given list.\n\n    Time Complexity: O(n), where n is the number of elements in the input list.\n    \"\"\"\n    if not all(isinstance(x, int) for x in arr):\n        raise ValueError(\"Input list must contain only integers.\")\n\n    if not arr:\n        return 0\n\n    total_sum = 0\n    n = len(arr)\n    \n    for i in range(32):  # Assuming 32-bit integers\n        count_set = sum((num >> i) & 1 for num in arr)\n        count_unset = n - count_set\n        total_sum += (count_set * count_unset) * (1 << i)\n    \n    return total_sum\n```", "execution_time": 22.141490936279297, "code": "def pair_xor_sum(arr: list[int]) -> int:\n    \"\"\"\n    Calculates the sum of the XOR of all unique pairs of numbers in a given list.\n\n    Time Complexity: O(n), where n is the number of elements in the input list.\n    \"\"\"\n    if not all(isinstance(x, int) for x in arr):\n        raise ValueError(\"Input list must contain only integers.\")\n\n    if not arr:\n        return 0\n\n    total_sum = 0\n    n = len(arr)\n    \n    for i in range(32):  # Assuming 32-bit integers\n        count_set = sum((num >> i) & 1 for num in arr)\n        count_unset = n - count_set\n        total_sum += (count_set * count_unset) * (1 << i)\n    \n    return total_sum", "problem_index": 69, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "196e22a8", "prompt": "Write a function to pack consecutive duplicates of a given list elements into sublists.\nfrom itertools import groupby\ndef pack_consecutive_duplicates(list1):", "solution": "```python\nfrom itertools import groupby\nfrom typing import List, Any\n\ndef pack_consecutive_duplicates(input_list: List[Any]) -> List[List[Any]]:\n    \"\"\"\n    Groups consecutive duplicate elements in a list into sublists.\n\n    Args:\n        input_list: The input list to be processed.\n\n    Returns:\n        A list of lists, where each sublist contains consecutive duplicate elements from the input list.\n    \"\"\"\n    return [list(group) for key, group in groupby(input_list)]\n```", "execution_time": 20.476624965667725, "code": "from itertools import groupby\nfrom typing import List, Any\n\ndef pack_consecutive_duplicates(input_list: List[Any]) -> List[List[Any]]:\n    \"\"\"\n    Groups consecutive duplicate elements in a list into sublists.\n\n    Args:\n        input_list: The input list to be processed.\n\n    Returns:\n        A list of lists, where each sublist contains consecutive duplicate elements from the input list.\n    \"\"\"\n    return [list(group) for key, group in groupby(input_list)]", "problem_index": 70, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "7ac316e8", "prompt": "Write a function to create a new tuple from the given string and list.\n\ndef new_tuple(test_list, test_str):", "solution": "```python\ndef new_tuple(test_list, test_str):\n    return tuple(test_list) + tuple(test_str)\n```", "execution_time": 20.88975691795349, "code": "def new_tuple(test_list, test_str):\n    return tuple(test_list) + tuple(test_str)", "problem_index": 71, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "9563c3a1", "prompt": "Write a function to check whether the product of numbers in a list is even or not.\n\ndef is_product_even(arr): ", "solution": "def is_product_even(arr):\n    \"\"\"\n    Checks if the product of numbers in a list is even.\n\n    Args:\n        arr (list of int): A list of integers.\n\n    Returns:\n        bool: True if the product is even, False otherwise.\n    \"\"\"\n    if not arr:\n        return False\n    \n    for num in arr:\n        if num % 2 == 0:\n            return True\n    \n    return False", "execution_time": 20.742748737335205, "code": "def is_product_even(arr):\n    \"\"\"\n    Checks if the product of numbers in a list is even.\n\n    Args:\n        arr (list of int): A list of integers.\n\n    Returns:\n        bool: True if the product is even, False otherwise.\n    \"\"\"\n    if not arr:\n        return False\n    \n    for num in arr:\n        if num % 2 == 0:\n            return True\n    \n    return False", "problem_index": 72, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "8eb42b8c", "prompt": "Write a function to find the maximum sum of increasing subsequence from prefix until ith index and also including a given kth element which is after i, i.e., k > i .\n\ndef max_sum_increasing_subseq(a, n, index, k):", "solution": "```python\ndef max_sum_increasing_subseq(a, n, index, k):\n    if index < 0 or k >= n or index >= n or index >= k:\n        return 0  # Edge case handling\n\n    max_sums = [0] * n\n    for j in range(index + 1):\n        max_sums[j] = a[j]\n        for m in range(j):\n            if a[m] < a[j]:\n                max_sums[j] = max(max_sums[j], max_sums[m] + a[j])\n\n    max_sum_so_far = 0\n    for i in range(index + 1):\n        if a[i] < a[k]:\n            max_sum_so_far = max(max_sum_so_far, max_sums[i])\n\n    if max_sum_so_far == 0:\n        return 0\n\n    return max_sum_so_far + a[k]\n```", "execution_time": 45.09383511543274, "code": "def max_sum_increasing_subseq(a, n, index, k):\n    if index < 0 or k >= n or index >= n or index >= k:\n        return 0  # Edge case handling\n\n    max_sums = [0] * n\n    for j in range(index + 1):\n        max_sums[j] = a[j]\n        for m in range(j):\n            if a[m] < a[j]:\n                max_sums[j] = max(max_sums[j], max_sums[m] + a[j])\n\n    max_sum_so_far = 0\n    for i in range(index + 1):\n        if a[i] < a[k]:\n            max_sum_so_far = max(max_sum_so_far, max_sums[i])\n\n    if max_sum_so_far == 0:\n        return 0\n\n    return max_sum_so_far + a[k]", "problem_index": 73, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "cdbff069", "prompt": "Write a function to count the pairs of reverse strings in the given string list. https://www.geeksforgeeks.org/python-program-to-count-the-pairs-of-reverse-strings/\n\ndef count_reverse_pairs(test_list):", "solution": "```python\ndef count_reverse_pairs(test_list):\n    seen = set()\n    count = 0\n    \n    for string in test_list:\n        reversed_string = string[::-1]\n        if string != reversed_string and reversed_string in seen:\n            count += 1\n        seen.add(string)\n    \n    return count\n```", "execution_time": 24.73192596435547, "code": "def count_reverse_pairs(test_list):\n    seen = set()\n    count = 0\n    \n    for string in test_list:\n        reversed_string = string[::-1]\n        if string != reversed_string and reversed_string in seen:\n            count += 1\n        seen.add(string)\n    \n    return count", "problem_index": 74, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "b05e03cd", "prompt": "The input is defined as two lists of the same length. Write a function to count indices where the lists have the same values.\nfrom operator import eq\ndef count_same_pair(nums1, nums2):", "solution": "```python\ndef count_same_pair(nums1, nums2):\n    \"\"\"\n    Counts the number of indices where two lists have the same value.\n\n    Args:\n        nums1 (list): The first list.\n        nums2 (list): The second list.\n\n    Returns:\n        int: The number of indices where the lists have the same value.\n\n    Raises:\n        ValueError: If the input lists are not of equal length.\n    \"\"\"\n    if len(nums1) != len(nums2):\n        raise ValueError(\"Input lists must have equal length.\")\n\n    return sum(a == b for a, b in zip(nums1, nums2))\n```", "execution_time": 22.020857095718384, "code": "def count_same_pair(nums1, nums2):\n    \"\"\"\n    Counts the number of indices where two lists have the same value.\n\n    Args:\n        nums1 (list): The first list.\n        nums2 (list): The second list.\n\n    Returns:\n        int: The number of indices where the lists have the same value.\n\n    Raises:\n        ValueError: If the input lists are not of equal length.\n    \"\"\"\n    if len(nums1) != len(nums2):\n        raise ValueError(\"Input lists must have equal length.\")\n\n    return sum(a == b for a, b in zip(nums1, nums2))", "problem_index": 75, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "ce1e0b20", "prompt": "Write a function to find the volume of a sphere.\nimport math\ndef volume_sphere(r):", "solution": "```python\nimport math\n\ndef volume_sphere(r: float) -> float:\n    \"\"\"\n    Calculate the volume of a sphere.\n\n    Args:\n        r: The radius of the sphere.\n\n    Returns:\n        The volume of the sphere.\n\n    Raises:\n        ValueError: If the radius is negative.\n    \"\"\"\n    if r < 0:\n        raise ValueError(\"Radius cannot be negative\")\n    return (4/3) * math.pi * (r ** 3)\n```", "execution_time": 20.022137880325317, "code": "import math\n\ndef volume_sphere(r: float) -> float:\n    \"\"\"\n    Calculate the volume of a sphere.\n\n    Args:\n        r: The radius of the sphere.\n\n    Returns:\n        The volume of the sphere.\n\n    Raises:\n        ValueError: If the radius is negative.\n    \"\"\"\n    if r < 0:\n        raise ValueError(\"Radius cannot be negative\")\n    return (4/3) * math.pi * (r ** 3)", "problem_index": 76, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "8f0dc913", "prompt": "Write a function to find the lateral surface area of a cube given its side length.\n\ndef lateralsurface_cube(l):", "solution": "```python\ndef lateralsurface_cube(l):\n    if l < 0:\n        raise ValueError(\"Side length must be non-negative\")\n    return 4 * (l ** 2)\n```", "execution_time": 22.915728092193604, "code": "def lateralsurface_cube(l):\n    if l < 0:\n        raise ValueError(\"Side length must be non-negative\")\n    return 4 * (l ** 2)", "problem_index": 77, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "c95d5b07", "prompt": "Write a function to search a string for a regex pattern. The function should return the matching subtring, a start index and an end index.\nimport re\ndef find_literals(text, pattern):", "solution": "```python\nimport re\n\ndef find_literals(text, pattern):\n    try:\n        re.compile(pattern) # Validate regex pattern\n    except re.error as e:\n        return f\"Invalid regex pattern: {e}\"\n\n    try:\n        match = re.search(pattern, text)\n        if match:\n            return (match.group(), match.start(), match.end())\n        return None\n    except re.error as e:\n        return f\"Regex search error: {e}\"\n```", "execution_time": 20.467111825942993, "code": "import re\n\ndef find_literals(text, pattern):\n    try:\n        re.compile(pattern) # Validate regex pattern\n    except re.error as e:\n        return f\"Invalid regex pattern: {e}\"\n\n    try:\n        match = re.search(pattern, text)\n        if match:\n            return (match.group(), match.start(), match.end())\n        return None\n    except re.error as e:\n        return f\"Regex search error: {e}\"", "problem_index": 78, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "099a4687", "prompt": "Write a function to locate the right insertion point for a specified value in sorted order.\nimport bisect\ndef right_insertion(a, x):", "solution": "def right_insertion(a, x):\n    # Returns the rightmost index for inserting x in sorted list a\n    return bisect.bisect_right(a, x)", "execution_time": 22.49195098876953, "code": "def right_insertion(a, x):\n    # Returns the rightmost index for inserting x in sorted list a\n    return bisect.bisect_right(a, x)", "problem_index": 79, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "1489c1b6", "prompt": "Write a function to join a list of multiple integers into a single integer.\n\ndef multiple_to_single(L):", "solution": "```python\ndef multiple_to_single(L):\n    \"\"\"\n    Concatenates a list of integers into a single integer.\n\n    Args:\n        L: A list of integers.\n\n    Returns:\n        An integer formed by concatenating the integers in the input list.\n\n    Raises:\n        TypeError: If the input is not a list or if the list contains non-integer values.\n        ValueError: If the list is empty.\n    \"\"\"\n    if not isinstance(L, list):\n        raise TypeError(\"Input must be a list.\")\n\n    if not L:\n        raise ValueError(\"Input list cannot be empty.\")\n\n    for item in L:\n        if not isinstance(item, int):\n            raise TypeError(\"List must contain only integers.\")\n\n    return int(''.join(map(str, L)))\n```", "execution_time": 21.959908962249756, "code": "def multiple_to_single(L):\n    \"\"\"\n    Concatenates a list of integers into a single integer.\n\n    Args:\n        L: A list of integers.\n\n    Returns:\n        An integer formed by concatenating the integers in the input list.\n\n    Raises:\n        TypeError: If the input is not a list or if the list contains non-integer values.\n        ValueError: If the list is empty.\n    \"\"\"\n    if not isinstance(L, list):\n        raise TypeError(\"Input must be a list.\")\n\n    if not L:\n        raise ValueError(\"Input list cannot be empty.\")\n\n    for item in L:\n        if not isinstance(item, int):\n            raise TypeError(\"List must contain only integers.\")\n\n    return int(''.join(map(str, L)))", "problem_index": 80, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "46282537", "prompt": "Write a function to find the first adverb and their positions in a given sentence.\nimport re\ndef find_adverb_position(text):", "solution": "```python\nimport re\n\ndef find_adverb_position(text):\n    if not isinstance(text, str):\n        return None\n    if not text:\n        return None\n    pattern = r'\\b(\\w+ly|very|too|well|fast)\\b'\n    match = re.search(pattern, text)\n    if match:\n        return {\n            'adverb': match.group(),\n            'start': match.start(),\n            'end': match.end()\n        }\n    return None\n```", "execution_time": 27.879544019699097, "code": "import re\n\ndef find_adverb_position(text):\n    if not isinstance(text, str):\n        return None\n    if not text:\n        return None\n    pattern = r'\\b(\\w+ly|very|too|well|fast)\\b'\n    match = re.search(pattern, text)\n    if match:\n        return {\n            'adverb': match.group(),\n            'start': match.start(),\n            'end': match.end()\n        }\n    return None", "problem_index": 81, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "81cab716", "prompt": "Write a python function to count true booleans in the given list.\n\ndef count(lst):   ", "solution": "def count(lst: list) -> int:\n    if not isinstance(lst, list):\n        raise TypeError(\"Input must be a list.\")\n    return sum(bool(x) for x in lst)", "execution_time": 21.57348394393921, "code": "def count(lst: list) -> int:\n    if not isinstance(lst, list):\n        raise TypeError(\"Input must be a list.\")\n    return sum(bool(x) for x in lst)", "problem_index": 82, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "37585fa6", "prompt": "Write a function that takes in a list and an integer n and returns a list containing the n largest items from the list.\nimport heapq\ndef larg_nnum(list1,n):", "solution": "```python\nimport heapq\nfrom typing import List\n\ndef largest_n_numbers(list1: List[int], n: int) -> List[int]:\n    \"\"\"\n    Return the n largest elements from the input list.\n\n    Args:\n        list1: The input list of numbers.\n        n: The number of largest elements to return.\n\n    Returns:\n        A list containing the n largest elements from list1, in descending order.\n        Returns an empty list if list1 is empty or n is non-positive.\n        Returns the entire list if n is greater than the length of list1.\n    \"\"\"\n    # Handle edge cases\n    if not list1 or n <= 0:\n        return []\n    \n    # Return n largest elements, or all if n > list length\n    return heapq.nlargest(n, list1)\n```", "execution_time": 24.594130754470825, "code": "import heapq\nfrom typing import List\n\ndef largest_n_numbers(list1: List[int], n: int) -> List[int]:\n    \"\"\"\n    Return the n largest elements from the input list.\n\n    Args:\n        list1: The input list of numbers.\n        n: The number of largest elements to return.\n\n    Returns:\n        A list containing the n largest elements from list1, in descending order.\n        Returns an empty list if list1 is empty or n is non-positive.\n        Returns the entire list if n is greater than the length of list1.\n    \"\"\"\n    # Handle edge cases\n    if not list1 or n <= 0:\n        return []\n    \n    # Return n largest elements, or all if n > list length\n    return heapq.nlargest(n, list1)", "problem_index": 83, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "05639c88", "prompt": "Write a python function to find the difference between the sum of cubes of the first n natural numbers and the sum of the first n natural numbers.\n\ndef difference(n) :  ", "solution": "```python\ndef difference(n):\n    \"\"\"Calculates the difference between the sum of cubes and sum of first n natural numbers.\"\"\"\n    sum_n = n * (n + 1) // 2\n    sum_cubes = sum_n ** 2\n    return sum_cubes - sum_n\n```", "execution_time": 21.369521856307983, "code": "def difference(n):\n    \"\"\"Calculates the difference between the sum of cubes and sum of first n natural numbers.\"\"\"\n    sum_n = n * (n + 1) // 2\n    sum_cubes = sum_n ** 2\n    return sum_cubes - sum_n", "problem_index": 84, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "8034095d", "prompt": "Write a function to find minimum k records from tuple list. https://www.geeksforgeeks.org/python-find-minimum-k-records-from-tuple-list/ - in this case a verbatim copy of test cases\n\ndef min_k(test_list, K):", "solution": "```python\ndef min_k(test_list: list[tuple], K: int) -> list[tuple]:\n    \"\"\"\n    Finds the K tuples with the smallest first element from a list of tuples.\n\n    Args:\n        test_list: A list of tuples.\n        K: The number of tuples to return.\n\n    Returns:\n        A list containing the K tuples with the smallest first element, sorted accordingly.\n        Returns an empty list if the input list is empty or if K is non-positive.\n        If K is greater than the length of the list, returns the entire sorted list.\n    \"\"\"\n    # Validate inputs\n    if not test_list or K <= 0:\n        return []\n\n    # Handle the case where K is larger than the list length\n    if K > len(test_list):\n        return sorted(test_list, key=lambda x: x[0])\n\n    # Sort by first tuple element, return K smallest\n    return sorted(test_list, key=lambda x: x[0])[:K]\n```", "execution_time": 25.605478763580322, "code": "def min_k(test_list: list[tuple], K: int) -> list[tuple]:\n    \"\"\"\n    Finds the K tuples with the smallest first element from a list of tuples.\n\n    Args:\n        test_list: A list of tuples.\n        K: The number of tuples to return.\n\n    Returns:\n        A list containing the K tuples with the smallest first element, sorted accordingly.\n        Returns an empty list if the input list is empty or if K is non-positive.\n        If K is greater than the length of the list, returns the entire sorted list.\n    \"\"\"\n    # Validate inputs\n    if not test_list or K <= 0:\n        return []\n\n    # Handle the case where K is larger than the list length\n    if K > len(test_list):\n        return sorted(test_list, key=lambda x: x[0])\n\n    # Sort by first tuple element, return K smallest\n    return sorted(test_list, key=lambda x: x[0])[:K]", "problem_index": 85, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "83242b30", "prompt": "Write a python function to find the minimum number of rotations (greater than 0) required to get the same string.\n\ndef find_Rotations(str): ", "solution": "```python\ndef find_Rotations(str):\n    # Handle edge cases\n    if not str or len(str) <= 1:\n        return -1\n    \n    # Double the string to check all possible rotations\n    doubled = str + str\n    \n    # Find first occurrence of original string in doubled string\n    for i in range(1, len(str)):\n        if str == doubled[i:i+len(str)]:\n            return i\n    \n    # If no rotation works, return -1\n    return -1\n```", "execution_time": 27.250613927841187, "code": "def find_Rotations(str):\n    # Handle edge cases\n    if not str or len(str) <= 1:\n        return -1\n    \n    # Double the string to check all possible rotations\n    doubled = str + str\n    \n    # Find first occurrence of original string in doubled string\n    for i in range(1, len(str)):\n        if str == doubled[i:i+len(str)]:\n            return i\n    \n    # If no rotation works, return -1\n    return -1", "problem_index": 86, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "6fb52a44", "prompt": "Write a python function to check whether every even index contains even numbers of a given list.\n\ndef even_position(nums):", "solution": "```python\ndef even_position(nums):\n    # Empty list has no odd-indexed elements to check\n    # Handle empty list case\n    if not nums:\n        # Empty list considered valid since no even-indexed elements violate the condition\n        return True\n    \n    # Check if all even-indexed elements are even\n    return all(nums[i] % 2 == 0 for i in range(0, len(nums), 2))\n```", "execution_time": 30.08332896232605, "code": "def even_position(nums):\n    # Empty list has no odd-indexed elements to check\n    # Handle empty list case\n    if not nums:\n        # Empty list considered valid since no even-indexed elements violate the condition\n        return True\n    \n    # Check if all even-indexed elements are even\n    return all(nums[i] % 2 == 0 for i in range(0, len(nums), 2))", "problem_index": 87, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "df14295b", "prompt": "Write a python function to find the difference between largest and smallest value in a given list.\n\ndef big_diff(nums):", "solution": "def big_diff(nums: list[int | float]) -> int | float:\n    if not nums:\n        return 0\n    return max(nums) - min(nums)", "execution_time": 25.724578857421875, "code": "def big_diff(nums: list[int | float]) -> int | float:\n    if not nums:\n        return 0\n    return max(nums) - min(nums)", "problem_index": 88, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "1b0e1155", "prompt": "Write a function to find the largest sum of a contiguous array in the modified array which is formed by repeating the given array k times.\n\ndef max_sub_array_sum_repeated(a, n, k): ", "solution": "```python\ndef max_sub_array_sum_repeated(a, n, k):\n    if not a:\n        return 0\n    if k <= 0:\n        return 0\n\n    def kadane(arr):\n        max_ending_here = max_so_far = arr[0]\n        for x in arr[1:]:\n            max_ending_here = max(x, max_ending_here + x)\n            max_so_far = max(max_so_far, max_ending_here)\n        return max_so_far\n\n    max_kadane = kadane(a)\n    \n    if k == 1:\n        return max_kadane\n\n    max_prefix_sum = float('-inf')\n    current_prefix_sum = 0\n    for i in range(n):\n        current_prefix_sum += a[i]\n        max_prefix_sum = max(max_prefix_sum, current_prefix_sum)\n\n    max_suffix_sum = float('-inf')\n    current_suffix_sum = 0\n    for i in range(n - 1, -1, -1):\n        current_suffix_sum += a[i]\n        max_suffix_sum = max(max_suffix_sum, current_suffix_sum)\n\n    total_sum = sum(a)\n\n    if total_sum > 0:\n        return max(max_kadane, max_suffix_sum + max_prefix_sum + (k - 2) * total_sum)\n    else:\n        return max(max_kadane, max_suffix_sum + max_prefix_sum)\n```", "execution_time": 32.0764479637146, "code": "def max_sub_array_sum_repeated(a, n, k):\n    if not a:\n        return 0\n    if k <= 0:\n        return 0\n\n    def kadane(arr):\n        max_ending_here = max_so_far = arr[0]\n        for x in arr[1:]:\n            max_ending_here = max(x, max_ending_here + x)\n            max_so_far = max(max_so_far, max_ending_here)\n        return max_so_far\n\n    max_kadane = kadane(a)\n    \n    if k == 1:\n        return max_kadane\n\n    max_prefix_sum = float('-inf')\n    current_prefix_sum = 0\n    for i in range(n):\n        current_prefix_sum += a[i]\n        max_prefix_sum = max(max_prefix_sum, current_prefix_sum)\n\n    max_suffix_sum = float('-inf')\n    current_suffix_sum = 0\n    for i in range(n - 1, -1, -1):\n        current_suffix_sum += a[i]\n        max_suffix_sum = max(max_suffix_sum, current_suffix_sum)\n\n    total_sum = sum(a)\n\n    if total_sum > 0:\n        return max(max_kadane, max_suffix_sum + max_prefix_sum + (k - 2) * total_sum)\n    else:\n        return max(max_kadane, max_suffix_sum + max_prefix_sum)", "problem_index": 89, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "a3ddd245", "prompt": "Write a function to find the number of elements that occurs before the tuple element in the given tuple.\n\ndef count_first_elements(test_tup):", "solution": "def count_first_elements(test_tup, elem):\n    \"\"\"\n    Counts the number of elements before a specified tuple element.\n\n    Args:\n        test_tup (tuple): The tuple to search within.\n        elem: The element to search for.\n\n    Returns:\n        int: The number of elements before the specified element in the tuple.\n             Returns -1 if the element is not found.\n    \"\"\"\n    for i, x in enumerate(test_tup):\n        if x == elem:\n            return i\n    return -1", "execution_time": 22.803652048110962, "code": "def count_first_elements(test_tup, elem):\n    \"\"\"\n    Counts the number of elements before a specified tuple element.\n\n    Args:\n        test_tup (tuple): The tuple to search within.\n        elem: The element to search for.\n\n    Returns:\n        int: The number of elements before the specified element in the tuple.\n             Returns -1 if the element is not found.\n    \"\"\"\n    for i, x in enumerate(test_tup):\n        if x == elem:\n            return i\n    return -1", "problem_index": 90, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "d613a780", "prompt": "Write a function to that returns true if the input string contains sequences of lowercase letters joined with an underscore and false otherwise.\nimport re\ndef text_lowercase_underscore(text):", "solution": "```python\nimport re\n\ndef text_lowercase_underscore(text):\n    if not isinstance(text, str):\n        raise TypeError(\"Input must be a string\")\n    if not text:\n        return False\n    pattern = r'^[a-z]+(_[a-z]+)*$'\n    return bool(re.match(pattern, text))\n```", "execution_time": 24.600581884384155, "code": "import re\n\ndef text_lowercase_underscore(text):\n    if not isinstance(text, str):\n        raise TypeError(\"Input must be a string\")\n    if not text:\n        return False\n    pattern = r'^[a-z]+(_[a-z]+)*$'\n    return bool(re.match(pattern, text))", "problem_index": 91, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "70cebd56", "prompt": "Write a function that matches a string that has an a followed by one or more b's.\nimport re\ndef text_match_one(text):", "solution": "```python\nimport re\ndef text_match_one(text):\n    pattern = r'ab+'\n    return bool(re.search(pattern, text))\n```", "execution_time": 25.85262703895569, "code": "import re\ndef text_match_one(text):\n    pattern = r'ab+'\n    return bool(re.search(pattern, text))", "problem_index": 92, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "5b688552", "prompt": "Write a function to check if all the elements in tuple have same data type or not.\n\ndef check_type(test_tuple):", "solution": "```python\ndef check_type(test_tuple):\n    \"\"\"Returns True if all elements in the tuple have the same data type, False otherwise.\n\n    Args:\n        test_tuple: The tuple to check.\n\n    Returns:\n        True if all elements have the same data type, False otherwise.\n    \"\"\"\n    if not test_tuple:  # Handle empty tuple case\n        return True\n    first_type = type(test_tuple[0])\n    return all(type(item) == first_type for item in test_tuple)\n```", "execution_time": 23.782257795333862, "code": "def check_type(test_tuple):\n    \"\"\"Returns True if all elements in the tuple have the same data type, False otherwise.\n\n    Args:\n        test_tuple: The tuple to check.\n\n    Returns:\n        True if all elements have the same data type, False otherwise.\n    \"\"\"\n    if not test_tuple:  # Handle empty tuple case\n        return True\n    first_type = type(test_tuple[0])\n    return all(type(item) == first_type for item in test_tuple)", "problem_index": 93, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "ffd589f1", "prompt": "Write a function to calculate the sum of the negative numbers of a given list of numbers.\n\ndef sum_negativenum(nums):", "solution": "```python\ndef sum_negative_numbers(nums):\n    \"\"\"\n    Calculate the sum of all negative numbers in a list.\n\n    Args:\n        nums (list): A list of numbers (int or float).\n\n    Returns:\n        int or float: The sum of the negative numbers in the list.\n                     Returns 0 if the list is empty or contains no negative numbers.\n\n    Raises:\n        TypeError: If the input is not a list or if the list contains non-numeric elements.\n    \"\"\"\n    if not isinstance(nums, list):\n        raise TypeError(\"Input must be a list.\")\n    if not nums:\n        return 0\n    for num in nums:\n        if not isinstance(num, (int, float)):\n            raise TypeError(\"List elements must be numbers.\")\n    return sum(num for num in nums if num < 0)\n```", "execution_time": 22.986379146575928, "code": "def sum_negative_numbers(nums):\n    \"\"\"\n    Calculate the sum of all negative numbers in a list.\n\n    Args:\n        nums (list): A list of numbers (int or float).\n\n    Returns:\n        int or float: The sum of the negative numbers in the list.\n                     Returns 0 if the list is empty or contains no negative numbers.\n\n    Raises:\n        TypeError: If the input is not a list or if the list contains non-numeric elements.\n    \"\"\"\n    if not isinstance(nums, list):\n        raise TypeError(\"Input must be a list.\")\n    if not nums:\n        return 0\n    for num in nums:\n        if not isinstance(num, (int, float)):\n            raise TypeError(\"List elements must be numbers.\")\n    return sum(num for num in nums if num < 0)", "problem_index": 94, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "bd2f2832", "prompt": "Write a function to extract specified size of strings from a given list of string values.\n\ndef extract_string(string_list, size):", "solution": "```python\nfrom typing import List\n\ndef extract_string(string_list: List[str], size: int) -> List[str]:\n    \"\"\"\n    Extracts strings of a specified size from a given list of strings.\n\n    Args:\n        string_list: A list of strings.\n        size: The desired length of the strings to extract.\n\n    Returns:\n        A new list containing only the strings from string_list that have the specified length.\n    \"\"\"\n    if string_list is None:\n        raise ValueError(\"string_list cannot be None\")\n    if not isinstance(string_list, list):\n        raise TypeError(\"string_list must be a list\")\n    if not isinstance(size, int):\n        raise TypeError(\"size must be an integer\")\n    if size < 0:\n        raise ValueError(\"size must be a non-negative integer\")\n    return [string for string in string_list if len(string) == size]\n```", "execution_time": 22.583537817001343, "code": "from typing import List\n\ndef extract_string(string_list: List[str], size: int) -> List[str]:\n    \"\"\"\n    Extracts strings of a specified size from a given list of strings.\n\n    Args:\n        string_list: A list of strings.\n        size: The desired length of the strings to extract.\n\n    Returns:\n        A new list containing only the strings from string_list that have the specified length.\n    \"\"\"\n    if string_list is None:\n        raise ValueError(\"string_list cannot be None\")\n    if not isinstance(string_list, list):\n        raise TypeError(\"string_list must be a list\")\n    if not isinstance(size, int):\n        raise TypeError(\"size must be an integer\")\n    if size < 0:\n        raise ValueError(\"size must be a non-negative integer\")\n    return [string for string in string_list if len(string) == size]", "problem_index": 95, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "7290d95a", "prompt": "Write a python function which takes a list and returns a list with the same elements, but the k'th element removed.\n\ndef remove_kth_element(list1, L):", "solution": "```python\ndef remove_kth_element(list1, k):\n    \"\"\"\n    Removes the k-th element from a list.\n\n    Args:\n        list1: The input list.\n        k: The index of the element to remove (0-based).\n\n    Returns:\n        A new list with the k-th element removed. If k is out of bounds,\n        the original list is returned. If the input list is empty, the\n        original empty list is returned.\n    \"\"\"\n    if not list1:\n        return list1\n    if k < 0 or k >= len(list1):\n        return list1\n    return list1[:k] + list1[k+1:]\n```", "execution_time": 24.763020038604736, "code": "def remove_kth_element(list1, k):\n    \"\"\"\n    Removes the k-th element from a list.\n\n    Args:\n        list1: The input list.\n        k: The index of the element to remove (0-based).\n\n    Returns:\n        A new list with the k-th element removed. If k is out of bounds,\n        the original list is returned. If the input list is empty, the\n        original empty list is returned.\n    \"\"\"\n    if not list1:\n        return list1\n    if k < 0 or k >= len(list1):\n        return list1\n    return list1[:k] + list1[k+1:]", "problem_index": 96, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "b29aaae4", "prompt": "Write a function to flatten a given nested list structure.\n\ndef flatten_list(list1):", "solution": "```python\ndef flatten_list(nested_list):\n    \"\"\"\n    Flattens a nested list into a single-level list.\n\n    For example:\n    >>> flatten_list([1, [2, 3], [4, [5]]])\n    [1, 2, 3, 4, 5]\n\n    Another example:\n    Given input [1, [2, 3, [4, 5]], 6], the function returns [1, 2, 3, 4, 5, 6].\n    \"\"\"\n    def flatten_helper(lst):\n        flat = []\n        for item in lst:\n            if isinstance(item, list):\n                flat.extend(flatten_helper(item))\n            else:\n                flat.append(item)\n        return flat\n    return flatten_helper(nested_list)\n```", "execution_time": 23.94269895553589, "code": "def flatten_list(nested_list):\n    \"\"\"\n    Flattens a nested list into a single-level list.\n\n    For example:\n    >>> flatten_list([1, [2, 3], [4, [5]]])\n    [1, 2, 3, 4, 5]\n\n    Another example:\n    Given input [1, [2, 3, [4, 5]], 6], the function returns [1, 2, 3, 4, 5, 6].\n    \"\"\"\n    def flatten_helper(lst):\n        flat = []\n        for item in lst:\n            if isinstance(item, list):\n                flat.extend(flatten_helper(item))\n            else:\n                flat.append(item)\n        return flat\n    return flatten_helper(nested_list)", "problem_index": 97, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "bb03f96e", "prompt": "Write a python function to find the sum of all odd length subarrays. https://www.geeksforgeeks.org/sum-of-all-odd-length-subarrays/\n\ndef odd_length_sum(arr):", "solution": "```python\ndef odd_length_sum(arr):\n    n = len(arr)\n    total_sum = 0\n\n    for i in range(n):\n        # The number of subarrays containing arr[i] is (i + 1) * (n - i).\n        # Half of these subarrays will have odd length, and half will have even length.\n        # To account for the case where (i + 1) * (n - i) is even, we add 1 and then integer divide by 2.\n        # This ensures we get the correct number of odd length subarrays.\n        subarrays = ((i + 1) * (n - i) + 1) // 2  # Calculate the number of odd-length subarrays containing arr[i]\n        total_sum += arr[i] * subarrays\n\n    return total_sum\n```", "execution_time": 31.***************, "code": "def odd_length_sum(arr):\n    n = len(arr)\n    total_sum = 0\n\n    for i in range(n):\n        # The number of subarrays containing arr[i] is (i + 1) * (n - i).\n        # Half of these subarrays will have odd length, and half will have even length.\n        # To account for the case where (i + 1) * (n - i) is even, we add 1 and then integer divide by 2.\n        # This ensures we get the correct number of odd length subarrays.\n        subarrays = ((i + 1) * (n - i) + 1) // 2  # Calculate the number of odd-length subarrays containing arr[i]\n        total_sum += arr[i] * subarrays\n\n    return total_sum", "problem_index": 98, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}, {"id": "48be9183", "prompt": "Write a function that gives loss amount on a sale if the given amount has loss else return 0.\n\ndef loss_amount(actual_cost,sale_amount): ", "solution": "```python\ndef loss_amount(actual_cost: float, sale_amount: float) -> float:\n    \"\"\"\n    Calculates the loss amount on a sale.\n\n    Args:\n        actual_cost (float): The actual cost of the item.\n        sale_amount (float): The amount the item was sold for.\n\n    Returns:\n        float: The loss amount if the sale amount is less than the actual cost, otherwise 0.\n    \"\"\"\n    if not isinstance(actual_cost, (int, float)) or actual_cost < 0:\n        raise ValueError(\"actual_cost must be a non-negative number\")\n    if not isinstance(sale_amount, (int, float)) or sale_amount < 0:\n        raise ValueError(\"sale_amount must be a non-negative number\")\n    return max(0, actual_cost - sale_amount)\n```", "execution_time": 27.965349674224854, "code": "def loss_amount(actual_cost: float, sale_amount: float) -> float:\n    \"\"\"\n    Calculates the loss amount on a sale.\n\n    Args:\n        actual_cost (float): The actual cost of the item.\n        sale_amount (float): The amount the item was sold for.\n\n    Returns:\n        float: The loss amount if the sale amount is less than the actual cost, otherwise 0.\n    \"\"\"\n    if not isinstance(actual_cost, (int, float)) or actual_cost < 0:\n        raise ValueError(\"actual_cost must be a non-negative number\")\n    if not isinstance(sale_amount, (int, float)) or sale_amount < 0:\n        raise ValueError(\"sale_amount must be a non-negative number\")\n    return max(0, actual_cost - sale_amount)", "problem_index": 99, "quality_score": 0.35, "processing_mode": "simplified_collaborative"}]}